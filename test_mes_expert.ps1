# MES咨询专家智能体测试脚本
# 测试新配置的BotId: 7533214653929881610

$baseUrl = "http://localhost:64922"

Write-Host "=== MES咨询专家智能体测试 ===" -ForegroundColor Green
Write-Host ""

# 测试1: 健康检查
Write-Host "1. 测试健康检查..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/health" -Method GET
    Write-Host "健康检查结果: $healthResponse" -ForegroundColor Green
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 测试2: 快速消息测试
Write-Host "2. 测试MES专家快速回复..." -ForegroundColor Yellow
$quickMessage = "请简单介绍一下MES系统的核心价值"

try {
    $quickResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/quick-send" -Method POST -Body (ConvertTo-Json $quickMessage) -ContentType "application/json"
    
    Write-Host "请求成功: $($quickResponse.success)" -ForegroundColor Green
    Write-Host "处理时间: $($quickResponse.processingTimeMs)ms" -ForegroundColor Cyan
    Write-Host "AI回复:" -ForegroundColor Cyan
    Write-Host $quickResponse.content -ForegroundColor White
} catch {
    Write-Host "快速消息测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试3: 完整消息测试（带记忆）
Write-Host "3. 测试完整消息处理（带记忆）..." -ForegroundColor Yellow

$fullMessage = @{
    content = "我是一家汽车零部件制造企业的生产总监，我们正在考虑实施MES系统来提升生产管理水平。请问MES系统主要能解决哪些生产管理痛点？"
    userId = "production_director_001"
    enableMemory = $true
    systemPrompt = "你是一位资深的MES系统咨询专家，拥有丰富的制造业数字化转型经验，特别擅长帮助汽车零部件企业优化生产管理流程。"
}

try {
    $fullResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/send-message" -Method POST -Body (ConvertTo-Json $fullMessage) -ContentType "application/json"
    
    Write-Host "请求成功: $($fullResponse.success)" -ForegroundColor Green
    Write-Host "会话ID: $($fullResponse.sessionId)" -ForegroundColor Cyan
    Write-Host "处理时间: $($fullResponse.processingTimeMs)ms" -ForegroundColor Cyan
    Write-Host "AI回复:" -ForegroundColor Cyan
    Write-Host $fullResponse.content -ForegroundColor White
    
    # 保存会话ID用于后续测试
    $sessionId = $fullResponse.sessionId
    
    Write-Host ""
    Write-Host "4. 测试对话记忆功能..." -ForegroundColor Yellow
    
    # 继续对话测试
    $followUpMessage = @{
        content = "那么实施MES系统大概需要多长时间？成本如何？"
        userId = "production_director_001"
        enableMemory = $true
        sessionId = $sessionId
    }
    
    $followUpResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/send-message" -Method POST -Body (ConvertTo-Json $followUpMessage) -ContentType "application/json"
    
    Write-Host "继续对话成功: $($followUpResponse.success)" -ForegroundColor Green
    Write-Host "AI回复:" -ForegroundColor Cyan
    Write-Host $followUpResponse.content -ForegroundColor White
    
} catch {
    Write-Host "完整消息测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果所有测试都通过，说明MES咨询专家智能体配置成功！" -ForegroundColor Green
Write-Host ""
Write-Host "Swagger UI地址: $baseUrl/swagger" -ForegroundColor Yellow
Write-Host "你可以在浏览器中打开上述地址进行更多测试" -ForegroundColor Yellow
