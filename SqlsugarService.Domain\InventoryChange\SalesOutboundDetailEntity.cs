using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Materials;
using SqlsugarService.Domain.Warehouse;
using System;

namespace SqlsugarService.Domain.InventoryChange
{
    /// <summary>
    /// 销售出库明细实体类
    /// </summary>
    [SugarTable("sales_outbound_detail")]
    public class SalesOutboundDetailEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 出库单ID
        /// </summary>
        public Guid OutboundId { get; set; }

        /// <summary>
        /// 产品ID（关联产品表）
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 产品编号（对应产品表中的MaterialNumber）
        /// </summary>
        public string ProductCode { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// 到期日期
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 批次号（对应产品表中的批次管理）
        /// </summary>
        public string BatchNumber { get; set; } = string.Empty;

        /// <summary>
        /// 出库数量
        /// </summary>
        public decimal OutboundQuantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        public Guid? StorageLocationId { get; set; }

        /// <summary>
        /// 库位名称
        /// </summary>
        public string? StorageLocationName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 导航属性 - 销售出库单
        /// </summary>
        [Navigate(NavigateType.ManyToOne, nameof(OutboundId))]
        public SalesOutboundEntity? Outbound { get; set; }

        /// <summary>
        /// 导航属性 - 产品信息
        /// </summary>
        [Navigate(NavigateType.ManyToOne, nameof(ProductId))]
        public ProductEntity? Product { get; set; }

        /// <summary>
        /// 导航属性 - 库位信息
        /// </summary>
        [Navigate(NavigateType.ManyToOne, nameof(StorageLocationId))]
        public WarehouseLocationEntity? StorageLocation { get; set; }
    }
}