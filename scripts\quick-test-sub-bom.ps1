# 快速测试子BOM功能的PowerShell脚本
# 用于验证子BOM展开功能是否正常工作

param(
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== 快速测试子BOM功能 ===" -ForegroundColor Green

# 测试数据
$mainBomId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"  # 主BOM ID
$subBomId = "bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"    # 子BOM ID

# 测试函数
function Test-SubBomFunction {
    param(
        [string]$Url,
        [string]$Description,
        [string]$ExpectedPattern
    )
    
    Write-Host "`n测试: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method Get -ContentType "application/json" -TimeoutSec 30
        
        if ($response.isSuc) {
            Write-Host "✅ 成功" -ForegroundColor Green
            
            if ($response.data -and $response.data.Count -gt 0) {
                Write-Host "返回数据数量: $($response.data.Count)" -ForegroundColor Cyan
                
                # 检查是否包含预期的内容
                $jsonResponse = $response | ConvertTo-Json -Depth 10
                if ($jsonResponse -match $ExpectedPattern) {
                    Write-Host "✅ 包含预期内容" -ForegroundColor Green
                } else {
                    Write-Host "⚠️ 未找到预期内容" -ForegroundColor Yellow
                }
                
                # 显示简要结构
                Show-BriefStructure $response.data
            } else {
                Write-Host "返回空数据" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ 失败: $($response.message)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示简要结构
function Show-BriefStructure {
    param(
        [array]$Nodes,
        [int]$Level = 0
    )
    
    foreach ($node in $Nodes) {
        $indent = "  " * $Level
        $nodeInfo = "$($node.name)"
        
        if ($node.children -and $node.children.Count -gt 0) {
            $nodeInfo += " [子节点: $($node.children.Count)]"
            Write-Host "$indent- $nodeInfo" -ForegroundColor Cyan
            
            # 只显示第一层子节点
            if ($Level == 0) {
                foreach ($child in $node.children) {
                    Write-Host "$indent  - $($child.name)" -ForegroundColor White
                }
            }
        } else {
            Write-Host "$indent- $nodeInfo [无子节点]" -ForegroundColor White
        }
    }
}

# 测试各个接口
try {
    Write-Host "=== 测试子BOM功能 ===" -ForegroundColor Cyan
    
    # 1. 测试主BOM（应该包含子BOM展开）
    Test-SubBomFunction -Url "$BaseUrl/api/ProductionPlans/bom-tree/$mainBomId" -Description "测试主BOM（应包含子BOM展开）" -ExpectedPattern "子产品B"
    
    # 2. 测试子BOM（应该显示子BOM的内容）
    Test-SubBomFunction -Url "$BaseUrl/api/ProductionPlans/bom-tree/$subBomId" -Description "测试子BOM（应显示子BOM内容）" -ExpectedPattern "基础物料"
    
    # 3. 测试所有BOM（应该包含所有BOM）
    Test-SubBomFunction -Url "$BaseUrl/api/ProductionPlans/bom-tree" -Description "测试所有BOM（应包含所有BOM）" -ExpectedPattern "主产品A"
    
} catch {
    Write-Host "测试过程中发生错误：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 快速测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "预期结果：" -ForegroundColor Cyan
Write-Host "1. 主BOM应显示：主产品A -> 基础物料1、基础物料2、子产品B" -ForegroundColor White
Write-Host "2. 子产品B应展开显示其子节点：基础物料3、基础物料4" -ForegroundColor White
Write-Host "3. 子BOM应显示：子产品B -> 基础物料3、基础物料4" -ForegroundColor White
Write-Host ""
Write-Host "如果测试失败，请先执行：" -ForegroundColor Yellow
Write-Host ".\scripts\insert-sub-bom-data.ps1" -ForegroundColor White 