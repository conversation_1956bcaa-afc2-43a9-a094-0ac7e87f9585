namespace SqlsugarService.Application.AI.LangChain.Dtos
{
    /// <summary>
    /// LangChain响应数据传输对象
    /// </summary>
    public class LangChainResponseDto
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; } = true;

        /// <summary>
        /// AI生成的回复内容
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// 会话ID，用于后续请求中的上下文关联
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 错误信息，仅当Success为false时有值
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 使用的模型名称
        /// </summary>
        public string? ModelName { get; set; }

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 令牌使用情况
        /// </summary>
        public TokenUsage? TokenUsage { get; set; }
    }

    /// <summary>
    /// 令牌使用情况
    /// </summary>
    public class TokenUsage
    {
        /// <summary>
        /// 输入令牌数量
        /// </summary>
        public int PromptTokens { get; set; }

        /// <summary>
        /// 输出令牌数量
        /// </summary>
        public int CompletionTokens { get; set; }

        /// <summary>
        /// 总令牌数量
        /// </summary>
        public int TotalTokens { get; set; }
    }
} 