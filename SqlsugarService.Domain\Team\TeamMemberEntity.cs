using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Team;
using System;

namespace SqlsugarService.Domain.Team
{
    /// <summary>
    /// 班组成员信息实体类 (班组与用户的中间表)
    /// </summary>
    public class TeamMemberEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 班组Id (外键)
        /// </summary>
        public Guid TeamId { get; set; }

        /// <summary>
        /// 班组导航属性
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(TeamId))]
        public TeamEntity Team { get; set; }

        /// <summary>
        /// 用户Id (外键)
        /// </summary>
        public Guid UserId { get; set; }

        // 注意：这里假设你有一个Users实体类，如果没有，可以暂时注释掉
        // [Navigate(NavigateType.OneToOne, nameof(UserId))]
        // public UserEntity User { get; set; }

        /// <summary>
        /// 岗位Id (外键)
        /// </summary>
        public Guid PositionId { get; set; }

        /// <summary>
        /// 是否为组长
        /// </summary>
        public bool IsLeader { get; set; } = false;

        /// <summary>
        /// 软删除标记
        /// </summary>
        public new bool IsDeleted { get; set; } = false;
    }
} 