using SqlsugarService.Domain.BOM;
using SqlsugarService.Domain.Craftsmanship;
using SqlsugarService.Domain.Materials;

namespace SqlsugarService.Application.DTOs.PlanDto
{
    /// <summary>
    /// BOM树形结构DTO
    /// </summary>
    public class BomTreeStructureDto
    {
        /// <summary>
        /// BOM项目ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// BOM ID
        /// </summary>
        public Guid BomId { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        public Guid MaterialId { get; set; }

        /// <summary>
        /// 父级项目ID
        /// </summary>
        public string ParentItemId { get; set; }

        /// <summary>
        /// 用量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal LossRate { get; set; }

        /// <summary>
        /// 投入产出类型
        /// </summary>
        public MaterialRelationType InOutType { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// BOM编号
        /// </summary>
        public string BomNumber { get; set; }

        /// <summary>
        /// BOM版本
        /// </summary>
        public string BomVersion { get; set; }

        /// <summary>
        /// 产品名称（来自ProductEntity）
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品编号（来自ProductEntity）
        /// </summary>
        public string ProductNumber { get; set; }

        /// <summary>
        /// 产品规格（来自ProductEntity）
        /// </summary>
        public string ProductSpecification { get; set; }

        /// <summary>
        /// 产品类型（来自ProductEntity）
        /// </summary>
        public MaterialTypeEnum ProductType { get; set; }

        /// <summary>
        /// 物料名称（来自MaterialEntity）
        /// </summary>
        public string MaterialName { get; set; }

        /// <summary>
        /// 物料编号（来自MaterialEntity）
        /// </summary>
        public string MaterialNumber { get; set; }

        /// <summary>
        /// 物料规格（来自MaterialEntity）
        /// </summary>
        public string MaterialSpecification { get; set; }

        /// <summary>
        /// 物料类型（来自MaterialEntity）
        /// </summary>
        public MaterialTypeEnum? MaterialType { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int Sequence { get; set; }

        /// <summary>
        /// 显示名称（优先显示产品名称，其次显示物料名称）
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// 显示产品编号（优先使用产品编号，其次使用物料编号）
        /// </summary>
        public string DisplayProductNumber { get; set; }

        /// <summary>
        /// 显示规格型号（优先使用产品规格，其次使用物料规格）
        /// </summary>
        public string DisplaySpecification { get; set; }

        /// <summary>
        /// 使用量
        /// </summary>
        public decimal UsageQuantity { get; set; }

        /// <summary>
        /// 使用比例
        /// </summary>
        public string UsageRatio { get; set; }

        /// <summary>
        /// 是否可展开（有子节点）
        /// </summary>
        public bool IsExpandable { get; set; }

        /// <summary>
        /// 层级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 子节点列表
        /// </summary>
        public List<BomTreeStructureDto> Children { get; set; } = new List<BomTreeStructureDto>();
    }
} 