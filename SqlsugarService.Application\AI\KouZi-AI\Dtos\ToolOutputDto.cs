using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.AI.KouZi_AI.Dtos
{
    /// <summary>
    /// 工具执行输出结果DTO - 单个工具的执行结果载体
    /// 
    /// 此DTO封装了客户端执行工具后的结果数据：
    /// 
    /// 核心概念：
    /// - 工具调用ID：唯一标识一次工具调用请求
    /// - 执行结果：工具实际执行后的输出数据
    /// - 结果格式：支持文本、JSON、文件等多种格式
    /// 
    /// 使用场景：
    /// - IoT设备控制结果：如"灯已打开"、"温度调节成功"
    /// - 文件操作结果：如文件ID、文件路径、操作状态
    /// - API调用结果：如天气数据、用户信息、计算结果
    /// - 数据库查询结果：如查询到的记录、统计数据
    /// - 系统操作结果：如进程状态、系统信息、性能数据
    /// 
    /// 数据流向：
    /// 客户端工具执行 -> ToolOutputDto -> 扣子AI处理 -> 继续对话
    /// 
    /// 技术特点：
    /// - 唯一性：通过ToolCallId确保结果对应正确的工具调用
    /// - 灵活性：Output字段支持各种数据格式
    /// - 可追溯：可以根据ToolCallId追踪完整的工具调用链
    /// - 标准化：符合扣子AI平台的工具调用规范
    /// </summary>
    public class ToolOutputDto
    {
        /// <summary>
        /// 工具调用标识符 - 关联工具请求与执行结果的关键字段
        /// 
        /// 标识符特征：
        /// - 全局唯一：在整个扣子AI平台中唯一标识一次工具调用
        /// - 生成来源：由扣子AI在发起工具调用时自动生成
        /// - 格式规范：通常为长字符串格式（如"BUJJF0dAQ0NAEBVeQkVKEV5HFURFXhFCEhFeFxdHShcSQEtFSxY****"）
        /// - 不可变性：一旦生成就不能修改，确保调用链的完整性
        /// 
        /// 业务作用：
        /// - 结果关联：将执行结果与特定的工具调用请求关联
        /// - 并发处理：支持多个工具并发执行时的结果正确分发
        /// - 错误追踪：出现问题时可以精确定位到具体的工具调用
        /// - 状态管理：跟踪工具调用从发起到完成的完整生命周期
        /// 
        /// 获取方式：
        /// - 流式响应：在conversation.chat.requires_action事件中的tool_calls字段
        /// - 非流式响应：在Chat Object的required_action.submit_tool_outputs.tool_calls中
        /// - 响应示例：response.data.required_action.submit_tool_outputs.tool_calls[0].id
        /// 
        /// 使用注意：
        /// - 必须完全匹配：提交的ToolCallId必须与AI发起请求时的完全一致
        /// - 区分大小写：标识符通常区分大小写，需要精确传递
        /// - 及时提交：工具调用通常有超时限制，需要及时提交结果
        /// - 唯一对应：一个ToolCallId只能提交一次结果
        /// 
        /// 技术实现：
        /// - 字符串类型：使用字符串类型保证兼容性
        /// - 长度适中：通常长度在50-100字符之间
        /// - 编码安全：使用URL安全的字符集
        /// - 存储效率：适合作为索引和缓存键使用
        /// 
        /// 错误处理：
        /// - ID不存在：提交不存在的ToolCallId会返回错误
        /// - ID过期：超时的ToolCallId无法接受结果提交
        /// - 重复提交：同一个ID重复提交会被拒绝
        /// - 格式错误：格式不正确的ID会导致验证失败
        /// </summary>
        [Required(ErrorMessage = "工具调用ID不能为空")]
        public string ToolCallId { get; set; }
        
        /// <summary>
        /// 工具执行结果输出 - 工具实际执行后的数据载荷
        /// 
        /// 输出数据的特征和格式：
        /// 
        /// 数据类型支持：
        /// - 纯文本：简单的文本结果，如"操作成功"、"Tokyo"、"温度已调节到25度"
        /// - JSON格式：结构化数据，如{"temperature": 25, "humidity": 60, "status": "success"}
        /// - 文件引用：文件类型结果，需要先上传文件获取file_id，然后以JSON格式传入
        /// - 错误信息：执行失败时的错误描述，如"设备离线"、"权限不足"
        /// - 状态信息：操作状态描述，如"正在处理中"、"已完成"、"等待确认"
        /// 
        /// 文件类型处理：
        /// - 上传步骤：先调用扣子AI的上传文件API获取file_id
        /// - 格式要求：以序列化的JSON格式传入file_id
        /// - 示例格式：{\"file\": \"12345\"}（其中12345是file_id）
        /// - 多文件支持：可以包含多个file_id的JSON结构
        /// 
        /// 数据格式建议：
        /// - 结构化数据：尽量使用JSON格式，便于AI理解和处理
        /// - 错误信息：包含具体的错误代码和描述信息
        /// - 状态数据：包含操作结果和相关的元数据信息
        /// - 时间信息：包含操作的时间戳和耗时信息
        /// 
        /// 内容长度和限制：
        /// - 合理长度：避免过长的输出内容影响性能
        /// - 关键信息：重点包含AI需要的关键信息
        /// - 格式化：保持良好的格式便于AI解析
        /// - 编码安全：确保特殊字符的正确编码
        /// 
        /// 业务场景示例：
        /// 
        /// IoT设备控制：
        /// - 成功：\"设备控制成功，客厅灯已打开\"
        /// - 失败：\"设备控制失败，设备离线或网络异常\"
        /// - 状态：{\"device_id\": \"light_001\", \"status\": \"on\", \"brightness\": 80}
        /// 
        /// 文件操作：
        /// - 文件生成：{\"file\": \"12345\", \"filename\": \"report.pdf\", \"size\": 1024000}
        /// - 操作成功：\"文件已成功保存到 /documents/report.pdf\"
        /// - 操作失败：\"文件保存失败，磁盘空间不足\"
        /// 
        /// API调用：
        /// - 天气查询：{\"city\": \"Tokyo\", \"temperature\": \"25°C\", \"weather\": \"晴朗\"}
        /// - 用户查询：{\"user_id\": \"123\", \"name\": \"张三\", \"status\": \"active\"}
        /// - 调用失败：\"API调用失败，服务暂时不可用\"
        /// 
        /// 数据库操作：
        /// - 查询结果：{\"total\": 150, \"records\": [{...}, {...}]}
        /// - 更新结果：\"成功更新3条记录\"
        /// - 操作失败：\"数据库连接失败，请稍后重试\"
        /// 
        /// 最佳实践：
        /// - 信息完整：包含AI需要的所有关键信息
        /// - 格式统一：保持输出格式的一致性
        /// - 错误友好：提供清晰的错误信息和解决建议
        /// - 性能考虑：避免过大的数据传输影响性能
        /// - 安全性：不要在输出中包含敏感信息
        /// </summary>
        [Required(ErrorMessage = "工具执行结果不能为空")]
        public string Output { get; set; }
    }
}