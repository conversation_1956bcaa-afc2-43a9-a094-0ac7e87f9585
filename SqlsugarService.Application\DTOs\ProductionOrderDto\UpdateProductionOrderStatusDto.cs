using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.ProductionOrderDto
{
    /// <summary>
    /// 修改生产工单状态DTO
    /// </summary>
    public class UpdateProductionOrderStatusDto
    {
        /// <summary>
        /// 生产工单ID
        /// </summary>
        [Required(ErrorMessage = "生产工单ID不能为空")]
        public Guid Id { get; set; }

        /// <summary>
        /// 工单状态（待排产、未开始、进行中、已完成、已暂停、已关闭等）
        /// </summary>
        [Required(ErrorMessage = "工单状态不能为空")]
        [StringLength(50, ErrorMessage = "工单状态长度不能超过50个字符")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 更新人
        /// </summary>
        [StringLength(100, ErrorMessage = "更新人长度不能超过100个字符")]
        public string? UpdatedBy { get; set; }
    }
} 