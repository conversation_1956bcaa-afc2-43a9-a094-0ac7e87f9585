# HTTP 500.19 错误解决指南

## 🚨 **当前问题**
- **错误**: HTTP 500.19 - Internal Server Error
- **原因**: IIS无法读取配置文件或访问应用程序目录
- **位置**: localhost:8065

## ✅ **解决方案**

### **方案一: 使用Kestrel服务器（推荐）**

**不使用IIS，直接运行应用程序：**

1. **打开PowerShell（管理员）**
2. **运行启动脚本：**
   ```powershell
   cd "C:\Users\<USER>\Desktop\EmployeeService\EmployeeService"
   powershell -ExecutionPolicy Bypass -File "SqlsugarService.API\start-app.ps1"
   ```
3. **访问**: http://localhost:8080

### **方案二: 修复IIS权限**

#### **1. 安装ASP.NET Core Hosting Bundle**
- 下载: https://dotnet.microsoft.com/download/dotnet/6.0
- 选择 "ASP.NET Core Runtime 6.0.x - Windows Hosting Bundle"
- 安装后重启电脑

#### **2. 启用IIS功能**
1. **打开"控制面板" → "程序" → "启用或关闭Windows功能"**
2. **勾选以下项目：**
   - ✅ Internet Information Services
   - ✅ World Wide Web Services
   - ✅ Application Development Features
     - ✅ ASP.NET 4.8
     - ✅ .NET Extensibility 4.8
     - ✅ ISAPI Extensions
     - ✅ ISAPI Filters

#### **3. 设置目录权限**
1. **右键点击发布目录：**
   ```
   SqlsugarService.API\bin\Release\net6.0\publish
   ```
2. **选择"属性" → "安全"选项卡**
3. **点击"编辑"按钮**
4. **添加以下用户并给予"完全控制"权限：**
   - `IIS_IUSRS`
   - `IUSR`
   - `Everyone` (临时测试用)

#### **4. 配置IIS应用程序池**
1. **打开IIS管理器**
2. **创建新应用程序池：**
   - 名称: `SqlsugarService`
   - .NET CLR版本: `无托管代码`
   - 托管管道模式: `集成`
3. **高级设置：**
   - 标识: `ApplicationPoolIdentity`
   - 启动模式: `AlwaysRunning`

#### **5. 创建IIS网站**
1. **右键"网站" → "添加网站"**
2. **配置：**
   - 网站名称: `SqlsugarService.API`
   - 应用程序池: `SqlsugarService`
   - 物理路径: `C:\Users\<USER>\Desktop\EmployeeService\EmployeeService\SqlsugarService.API\bin\Release\net6.0\publish`
   - 端口: `8065` (或其他)

### **方案三: 使用IIS Express（开发环境）**

#### **1. 修改launchSettings.json**
```json
{
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:8065",
      "sslPort": 0
    }
  },
  "profiles": {
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
```

#### **2. 在Visual Studio中运行**
- 选择"IIS Express"配置
- 按F5启动

## 🔧 **快速修复命令**

### **重置IIS（管理员PowerShell）**
```powershell
# 重置IIS
iisreset

# 重新注册ASP.NET
%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_regiis.exe -i
```

### **检查端口占用**
```powershell
# 检查端口8065是否被占用
netstat -ano | findstr :8065

# 如果被占用，终止进程
taskkill /PID <进程ID> /F
```

### **设置权限（管理员PowerShell）**
```powershell
# 进入发布目录
cd "SqlsugarService.API\bin\Release\net6.0\publish"

# 设置权限
icacls . /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls . /grant "IUSR:(OI)(CI)RX" /T
```

## 🎯 **推荐解决方案**

**对于开发环境，建议使用方案一（Kestrel）：**

1. **不需要配置IIS**
2. **避免权限问题**
3. **更容易调试**
4. **性能更好**

**运行命令：**
```powershell
cd "C:\Users\<USER>\Desktop\EmployeeService\EmployeeService"
powershell -ExecutionPolicy Bypass -File "SqlsugarService.API\start-app.ps1"
```

**访问地址：**
- 主页: http://localhost:8080/
- Swagger: http://localhost:8080/swagger
- 健康检查: http://localhost:8080/health

## 📞 **如果仍有问题**

1. **检查Windows事件日志**
   - 打开"事件查看器"
   - 查看"Windows日志" → "应用程序"
   - 查找相关错误信息

2. **启用详细错误信息**
   - 在web.config中添加：
   ```xml
   <system.webServer>
     <httpErrors errorMode="Detailed" />
   </system.webServer>
   ```

3. **使用开发环境配置**
   - 设置环境变量：`ASPNETCORE_ENVIRONMENT=Development`
