﻿using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.DTOs.ProductionOrderDto
{
    public class GetProductionOrderSearchDto:Seach
    {
        /// <summary>
        /// 工单编号/工单名称
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 工单状态（待排产、未开始、进行中、已完成、已暂停、已关闭等）
        /// </summary>
        public string? Status { get; set; }
    }
}
