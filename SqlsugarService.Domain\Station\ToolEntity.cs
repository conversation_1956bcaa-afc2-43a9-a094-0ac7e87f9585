using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Station;
using System;

namespace SqlsugarService.Domain.Station
{
    /// <summary>
    /// 工具信息实体类 (工装夹具档案)
    /// </summary>
    public class ToolEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 工装夹具编号
        /// </summary>
        public string ToolCode { get; set; }

        /// <summary>
        /// 工装夹具名称
        /// </summary>
        public string ToolName { get; set; }

        /// <summary>
        /// 工装夹具类型Id
        /// </summary>
        public Guid ToolCategoryId { get; set; }

        /// <summary>
        /// 工装夹具类型导航属性
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ToolCategoryId))]
        public ToolCategoryEntity ToolCategory { get; set; }

        /// <summary>
        /// 工装夹具型号
        /// </summary>
        public string ToolModel { get; set; }

        /// <summary>
        /// 工装夹具数量 (总数)
        /// </summary>
        public int TotalQuantity { get; set; }

        /// <summary>
        /// 可用数量
        /// </summary>
        public int AvailableQuantity { get; set; }

        /// <summary>
        /// 存放库位
        /// </summary>
        public string StorageLocation { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        public int UsageCount { get; set; }

        /// <summary>
        /// 预警次数 (达到该次数后预警)
        /// </summary>
        public int WarningThreshold { get; set; }

        /// <summary>
        /// 状态 (如: 在库, 使用中, 维修中)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 关联站点Id (外键)
        /// </summary>
        public Guid StationId { get; set; }

        /// <summary>
        /// 站点导航属性
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(StationId))]
        public StationEntity Station { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 软删除标记
        /// </summary>
        public new bool IsDeleted { get; set; } = false;
    }
} 