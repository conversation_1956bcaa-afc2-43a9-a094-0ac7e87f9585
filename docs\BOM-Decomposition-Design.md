# BOM分解成生产工单的设计方案

## 📋 **概述**

本文档详细说明了如何将生产计划中的BOM树形根据产品物料分解成多个生产工单的完整思路和实现方案，包括状态控制和撤销功能。

## 🎯 **核心需求**

1. **通过BomId获取BOM树形结构**
2. **根据产品物料分解成多个生产工单**
3. **只有在生产计划是未分解状态下才能分解**
4. **支持撤销已分解的工单**

## 🏗️ **整体架构**

### 1. **核心组件**
- **BomDecompositionService**：BOM分解服务
- **BomDecompositionController**：API控制器
- **数据结构**：BomTreeNode、DecompositionItem、BomDecompositionResult等

### 2. **数据流图**
```
生产计划 → 状态验证 → BOM树形获取 → 产品物料分析 → 工单生成 → 状态更新
    ↓           ↓           ↓           ↓           ↓         ↓
  计划信息   状态检查   树形结构    分解项目    生产工单    已分解状态
```

## 🔍 **状态控制机制**

### 1. **生产计划状态定义**
```csharp
// 生产计划状态枚举
0 => "未分解"    // 可以执行分解操作
1 => "已分解"    // 可以执行撤销操作
2 => "已完成"    // 不允许操作
3 => "已关闭"    // 不允许操作
4 => "已撤回"    // 不允许操作
5 => "进行中"    // 不允许操作
```

### 2. **状态验证逻辑**
```csharp
// 分解验证
if (productionPlan.Status != 0)
{
    return "生产计划状态为{statusDesc}，无法分解";
}

// 撤销验证
if (productionPlan.Status != 1)
{
    return "生产计划状态为{statusDesc}，无法撤销";
}
```

## 🔧 **核心功能实现**

### 1. **BOM树形获取**
```csharp
private async Task<List<BomTreeNode>> GetBomTreeStructure(Guid bomId)
{
    // 1. 查询BOM项目
    var bomItems = await _bomItemRepository.AsQueryable()
        .Where(bi => bi.BomId == bomId)
        .ToListAsync();

    // 2. 查询物料信息
    var materials = await _db.Queryable<MaterialEntity>()
        .Where(m => materialIds.Contains(m.Id))
        .ToListAsync();

    // 3. 构建树形结构
    return BuildBomTree(bomItems);
}
```

### 2. **产品物料识别**
```csharp
private void AnalyzeNodeForDecomposition(BomTreeNode node, decimal parentQuantity, List<DecompositionItem> decompositionItems)
{
    // 计算实际数量
    var actualQuantity = node.Quantity * parentQuantity;

    // 识别产品物料
    if (node.MaterialType == MaterialTypeEnum.Product)
    {
        var decompositionItem = new DecompositionItem
        {
            MaterialId = node.MaterialId,
            MaterialName = node.MaterialName,
            RequiredQuantity = actualQuantity,
            IsProduct = true
        };
        decompositionItems.Add(decompositionItem);
    }

    // 递归分析子节点
    foreach (var child in node.Children)
    {
        AnalyzeNodeForDecomposition(child, actualQuantity, decompositionItems);
    }
}
```

### 3. **工单生成**
```csharp
private async Task<List<ProductionOrder>> GenerateProductionOrders(List<DecompositionItem> decompositionItems, ProductionPlan productionPlan)
{
    var productionOrders = new List<ProductionOrder>();

    foreach (var item in decompositionItems.Where(d => d.IsProduct))
    {
        var productionOrder = new ProductionOrder
        {
            Id = Guid.NewGuid(),
            OrderNumber = GenerateOrderNumber(),
            OrderName = $"{item.MaterialName}生产工单",
            ProductionPlanId = productionPlan.Id,
            ProductId = item.MaterialId,
            PlanQuantity = item.RequiredQuantity,
            Status = "待排产"
        };

        productionOrders.Add(productionOrder);
    }

    return productionOrders;
}
```

## 🛡️ **安全控制机制**

### 1. **状态验证**
- **分解前验证**：确保生产计划状态为"未分解"
- **撤销前验证**：确保生产计划状态为"已分解"
- **工单状态检查**：只有"待排产"状态的工单才能撤销

### 2. **数据完整性**
- **事务性操作**：确保分解和撤销操作的原子性
- **状态一致性**：操作完成后及时更新生产计划状态
- **关联数据清理**：撤销时清理相关的生产工单

### 3. **软删除机制**
- **软删除标记**：使用`IsDeleted`字段标记删除状态
- **删除信息记录**：记录删除时间(`DeletedAt`)和删除者(`DeletedBy`)
- **数据保留**：软删除的数据仍然保留在数据库中
- **恢复功能**：支持恢复已软删除的工单
- **永久删除**：支持永久删除已软删除的工单

### 4. **错误处理**
- **异常捕获**：完整的异常处理机制
- **错误信息**：详细的错误提示信息
- **回滚机制**：操作失败时的数据回滚

## 📊 **数据结构设计**

### 1. **BomTreeNode** - BOM树节点
```csharp
public class BomTreeNode
{
    public Guid Id { get; set; }
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; }
    public MaterialTypeEnum MaterialType { get; set; }
    public decimal Quantity { get; set; }
    public string Unit { get; set; }
    public string ParentItemId { get; set; }
    public int Level { get; set; }
    public List<BomTreeNode> Children { get; set; }
}
```

### 2. **DecompositionItem** - 分解项目
```csharp
public class DecompositionItem
{
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; }
    public decimal RequiredQuantity { get; set; }
    public string Unit { get; set; }
    public int Level { get; set; }
    public bool IsProduct { get; set; }
    public string ParentNodeId { get; set; }
}
```

### 3. **BomDecompositionResult** - 分解结果
```csharp
public class BomDecompositionResult
{
    public Guid ProductionPlanId { get; set; }
    public int TotalOrders { get; set; }
    public List<ProductionOrder> Orders { get; set; }
    public List<DecompositionItem> DecompositionItems { get; set; }
    public DateTime? DecompositionTime { get; set; }
    public DateTime? UndoTime { get; set; }
}
```

## 🔧 **API接口设计**

### 1. **分解生产计划**
```
POST /api/BomDecomposition/decompose/{productionPlanId}
```
**功能**：将生产计划分解为生产工单
**前置条件**：生产计划状态为"未分解"
**后置条件**：生产计划状态更新为"已分解"

### 2. **撤销分解**
```
POST /api/BomDecomposition/undo/{productionPlanId}
```
**功能**：撤销已分解的工单
**前置条件**：生产计划状态为"已分解"，工单状态为"待排产"
**后置条件**：生产计划状态更新为"未分解"

### 3. **预览分解**
```
GET /api/BomDecomposition/preview/{productionPlanId}
```
**功能**：预览分解结果，不保存工单
**返回**：分解预览信息

### 4. **获取分解状态**
```
GET /api/BomDecomposition/status/{productionPlanId}
```
**功能**：获取生产计划的分解状态
**返回**：当前状态和可执行操作

### 5. **查询已删除的工单**
```
GET /api/BomDecomposition/deleted-orders/{productionPlanId}
```
**功能**：查询已软删除的生产工单
**返回**：已删除的工单列表

### 6. **恢复已删除的工单**
```
POST /api/BomDecomposition/restore/{productionPlanId}
```
**功能**：恢复已软删除的工单
**请求体**：`{ "OrderIds": ["guid1", "guid2"] }`
**前置条件**：生产计划状态为"已分解"
**后置条件**：工单恢复，生产计划状态保持"已分解"

### 7. **永久删除工单**
```
POST /api/BomDecomposition/permanently-delete/{productionPlanId}
```
**功能**：永久删除已软删除的工单
**请求体**：`{ "OrderIds": ["guid1", "guid2"] }`
**前置条件**：生产计划状态为"已分解"，工单已软删除
**后置条件**：工单从数据库中永久删除

## 📈 **业务流程**

### 1. **分解流程**
```
1. 验证生产计划状态（必须是未分解状态）
2. 通过BomId获取BOM树形结构
3. 分析BOM结构，识别产品物料
4. 生成生产工单
5. 计算工单时间安排
6. 保存生产工单
7. 更新生产计划状态为已分解
```

### 2. **撤销流程**
```
1. 验证生产计划状态（必须是已分解状态）
2. 查询相关的生产工单
3. 检查工单状态（必须是待排产状态）
4. 删除生产工单
5. 更新生产计划状态为未分解
```

### 3. **预览流程**
```
1. 获取生产计划信息
2. 通过BomId获取BOM树形结构
3. 分析BOM结构
4. 生成预览结果
```

### 4. **软删除流程**
```
1. 验证生产计划状态（必须是已分解状态）
2. 查询相关的生产工单（排除已删除的）
3. 检查工单状态（必须是待排产状态）
4. 标记工单为软删除（IsDeleted = true）
5. 记录删除时间和删除者
6. 更新生产计划状态为未分解
```

### 5. **恢复工单流程**
```
1. 验证生产计划状态（必须是已分解状态）
2. 查询要恢复的已删除工单
3. 取消软删除标记（IsDeleted = false）
4. 清除删除时间和删除者信息
5. 更新工单修改信息
6. 保持生产计划状态为已分解
```

### 6. **永久删除流程**
```
1. 验证生产计划状态（必须是已分解状态）
2. 查询要永久删除的已删除工单
3. 从数据库中永久删除工单记录
4. 记录删除结果
```

## 🎯 **应用场景**

### 1. **生产计划分解**
- **场景**：生产计划创建后，需要分解为具体的生产工单
- **操作**：执行分解操作
- **结果**：生成多个生产工单

### 2. **工单撤销**
- **场景**：分解后发现错误，需要撤销重新分解
- **操作**：执行撤销操作
- **结果**：删除工单，恢复未分解状态

### 3. **分解预览**
- **场景**：分解前查看分解结果
- **操作**：执行预览操作
- **结果**：显示分解预览信息

## 🔍 **优化策略**

### 1. **性能优化**
- **批量查询**：一次性查询所有相关数据
- **缓存机制**：缓存物料信息和BOM结构
- **异步处理**：支持异步分解操作

### 2. **准确性优化**
- **数量计算**：精确的数量计算逻辑
- **状态检查**：严格的状态验证机制
- **数据验证**：完整的数据完整性检查

### 3. **用户体验优化**
- **预览功能**：分解前预览结果
- **状态反馈**：清晰的状态信息反馈
- **错误提示**：详细的错误信息提示

## 🧪 **测试方案**

### 1. **单元测试**
- **状态验证测试**：测试各种状态下的操作权限
- **分解逻辑测试**：测试BOM分解的核心逻辑
- **撤销逻辑测试**：测试工单撤销的逻辑

### 2. **集成测试**
- **完整流程测试**：测试完整的分解和撤销流程
- **API接口测试**：测试所有API接口的功能
- **数据一致性测试**：测试数据的一致性

### 3. **性能测试**
- **大数据量测试**：测试大量数据的分解性能
- **并发测试**：测试并发操作的性能
- **响应时间测试**：测试接口响应时间

## 📋 **使用示例**

### 1. **分解生产计划**
```csharp
// 创建分解服务
var decompositionService = new BomDecompositionService(
    productionPlanRepository, 
    productionOrderRepository, 
    bomItemRepository, 
    bomInfoRepository, 
    db);

// 执行分解
var result = await decompositionService.DecomposeProductionPlan(productionPlanId);

// 处理结果
if (result.isSuc)
{
    Console.WriteLine($"成功分解，生成了 {result.data.TotalOrders} 个生产工单");
}
```

### 2. **撤销分解**
```csharp
// 执行撤销
var undoResult = await decompositionService.UndoDecomposition(productionPlanId);

// 处理结果
if (undoResult.isSuc)
{
    Console.WriteLine($"成功撤销，删除了 {undoResult.data.TotalOrders} 个生产工单");
}
```

### 3. **预览分解**
```csharp
// 执行预览
var previewResult = await decompositionService.PreviewDecomposition(productionPlanId);

// 处理结果
if (previewResult.isSuc)
{
    Console.WriteLine($"预估生成 {previewResult.data.EstimatedOrders} 个生产工单");
    Console.WriteLine($"可以分解: {previewResult.data.CanDecompose}");
}
```

## 🚀 **扩展功能**

### 1. **智能分解**
- **历史数据分析**：基于历史数据优化分解策略
- **机器学习**：使用机器学习算法优化分解结果
- **动态调整**：根据实际情况动态调整分解参数

### 2. **高级功能**
- **批量分解**：支持多个生产计划的批量分解
- **分解模板**：支持分解模板的保存和复用
- **分解规则**：支持自定义分解规则

### 3. **监控和报告**
- **分解监控**：实时监控分解过程
- **分解报告**：生成详细的分解报告
- **性能分析**：分析分解性能指标

## 📝 **总结**

BOM分解成生产工单是一个复杂的系统工程，需要考虑：

1. **状态控制**：严格的状态验证和控制机制
2. **数据完整性**：确保分解和撤销操作的数据一致性
3. **用户体验**：提供预览和状态反馈功能
4. **性能优化**：支持大数据量和并发操作
5. **扩展性**：支持未来功能扩展和优化

通过本方案，可以实现从生产计划到生产工单的自动化转换，提高生产效率和管理水平，同时保证数据的安全性和一致性。 