using AuthService.Domain.Enums;
using System.ComponentModel.DataAnnotations;
using HttpMethod = AuthService.Domain.Enums.HttpMethod;

namespace AuthService.Api.Models;

/// <summary>
/// 创建动态API端点请求
/// </summary>
public class CreateEndpointRequest
{
    /// <summary>
    /// 端点名称
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 端点描述
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// HTTP方法
    /// </summary>
    [Required]
    public HttpMethod Method { get; set; }

    /// <summary>
    /// 路径模板
    /// </summary>
    [Required]
    [StringLength(500)]
    public string PathTemplate { get; set; } = string.Empty;

    /// <summary>
    /// 目标URL
    /// </summary>
    [Required]
    [StringLength(1000)]
    public string TargetUrl { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 是否需要认证
    /// </summary>
    public bool RequireAuthentication { get; set; } = false;

    /// <summary>
    /// 需要的角色
    /// </summary>
    public List<string>? RequiredRoles { get; set; }

    /// <summary>
    /// 需要的权限
    /// </summary>
    public List<string>? RequiredPermissions { get; set; }

    /// <summary>
    /// 每分钟速率限制
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// 超时时间（秒）
    /// </summary>
    public int? TimeoutSeconds { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int? RetryCount { get; set; }

    /// <summary>
    /// 缓存时间（秒）
    /// </summary>
    public int? CacheSeconds { get; set; }

    /// <summary>
    /// 请求头
    /// </summary>
    public Dictionary<string, string>? Headers { get; set; }

    /// <summary>
    /// 查询参数
    /// </summary>
    public Dictionary<string, string>? QueryParameters { get; set; }

    /// <summary>
    /// 请求转换规则
    /// </summary>
    public string? RequestTransformation { get; set; }

    /// <summary>
    /// 响应转换规则
    /// </summary>
    public string? ResponseTransformation { get; set; }

    /// <summary>
    /// 版本
    /// </summary>
    [StringLength(50)]
    public string? Version { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 租户ID
    /// </summary>
    public string? TenantId { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 元数据
    /// </summary>
    public string? Metadata { get; set; }
}

/// <summary>
/// 更新动态API端点请求
/// </summary>
public class UpdateEndpointRequest
{
    /// <summary>
    /// 端点名称
    /// </summary>
    [StringLength(200)]
    public string? Name { get; set; }

    /// <summary>
    /// 端点描述
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// HTTP方法
    /// </summary>
    public HttpMethod? Method { get; set; }

    /// <summary>
    /// 路径模板
    /// </summary>
    [StringLength(500)]
    public string? PathTemplate { get; set; }

    /// <summary>
    /// 目标URL
    /// </summary>
    [StringLength(1000)]
    public string? TargetUrl { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }

    /// <summary>
    /// 是否需要认证
    /// </summary>
    public bool? RequireAuthentication { get; set; }

    /// <summary>
    /// 需要的角色
    /// </summary>
    public List<string>? RequiredRoles { get; set; }

    /// <summary>
    /// 需要的权限
    /// </summary>
    public List<string>? RequiredPermissions { get; set; }

    /// <summary>
    /// 每分钟速率限制
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// 超时时间（秒）
    /// </summary>
    public int? TimeoutSeconds { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int? RetryCount { get; set; }

    /// <summary>
    /// 缓存时间（秒）
    /// </summary>
    public int? CacheSeconds { get; set; }

    /// <summary>
    /// 请求头
    /// </summary>
    public Dictionary<string, string>? Headers { get; set; }

    /// <summary>
    /// 查询参数
    /// </summary>
    public Dictionary<string, string>? QueryParameters { get; set; }

    /// <summary>
    /// 请求转换规则
    /// </summary>
    public string? RequestTransformation { get; set; }

    /// <summary>
    /// 响应转换规则
    /// </summary>
    public string? ResponseTransformation { get; set; }

    /// <summary>
    /// 版本
    /// </summary>
    [StringLength(50)]
    public string? Version { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 租户ID
    /// </summary>
    public string? TenantId { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int? Priority { get; set; }

    /// <summary>
    /// 元数据
    /// </summary>
    public string? Metadata { get; set; }
}

/// <summary>
/// API端点响应
/// </summary>
public class EndpointResponse
{
    /// <summary>
    /// 端点ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 端点名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 端点描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// HTTP方法
    /// </summary>
    public string Method { get; set; } = string.Empty;

    /// <summary>
    /// 路径模板
    /// </summary>
    public string PathTemplate { get; set; } = string.Empty;

    /// <summary>
    /// 目标URL
    /// </summary>
    public string TargetUrl { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 是否需要认证
    /// </summary>
    public bool RequireAuthentication { get; set; }

    /// <summary>
    /// 需要的角色
    /// </summary>
    public List<string> RequiredRoles { get; set; } = new();

    /// <summary>
    /// 需要的权限
    /// </summary>
    public List<string> RequiredPermissions { get; set; } = new();

    /// <summary>
    /// 每分钟速率限制
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// 超时时间（秒）
    /// </summary>
    public int? TimeoutSeconds { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int? RetryCount { get; set; }

    /// <summary>
    /// 缓存时间（秒）
    /// </summary>
    public int? CacheSeconds { get; set; }

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 租户ID
    /// </summary>
    public string? TenantId { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}
