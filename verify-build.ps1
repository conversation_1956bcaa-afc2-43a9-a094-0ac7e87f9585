# 简单的编译验证脚本
Write-Host "=== EmployeeService 编译验证 ===" -ForegroundColor Green

try {
    # 编译整个解决方案
    Write-Host "正在编译解决方案..." -ForegroundColor Yellow
    $result = dotnet build --configuration Debug --verbosity minimal 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 编译成功！" -ForegroundColor Green
        
        # 检查关键程序集是否生成
        $assemblies = @(
            "AuthService.Api\bin\Debug\net6.0\AuthService.Api.dll",
            "SqlsugarService.API\bin\Debug\net6.0\SqlsugarService.API.dll"
        )
        
        Write-Host "`n检查生成的程序集:" -ForegroundColor Cyan
        foreach ($assembly in $assemblies) {
            if (Test-Path $assembly) {
                Write-Host "  ✅ $assembly" -ForegroundColor Green
            } else {
                Write-Host "  ❌ $assembly 未找到" -ForegroundColor Red
            }
        }
        
        Write-Host "`n🎉 编译验证完成！项目可以正常编译。" -ForegroundColor Green
        
    } else {
        Write-Host "❌ 编译失败" -ForegroundColor Red
        Write-Host "错误信息:" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "❌ 验证过程出错: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
