﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.DTOs.WorkReportInspectionDto
{
    public class GetWorkReportInspectionDto
    {
        public Guid Id { get; set; } = Guid.NewGuid();

        #region 基础信息
        /// <summary>
        /// 检验单号
        /// </summary>
        public string InspectionCode { get; set; }

        /// <summary>
        /// 检验单名称
        /// </summary>
        public string InspectionName { get; set; }

        /// <summary>
        /// 检验类型 (如：首检, 巡检, 末检)
        /// </summary>
        public string InspectionType { get; set; }

        /// <summary>
        /// 状态 (如: 未质检, 已质检)
        /// </summary>
        public string Status { get; set; }
        #endregion

        #region 关联外键
        /// <summary>
        /// 产品Id
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 工序Id
        /// </summary>
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 站点Id
        /// </summary>
        public Guid StationId { get; set; }

        /// <summary>
        /// 班组Id
        /// </summary>
        public Guid TeamId { get; set; }

        /// <summary>
        /// 报工人员Id
        /// </summary>
        public Guid ReporterId { get; set; }

        /// <summary>
        /// 检验人员Id
        /// </summary>
        public Guid InspectorId { get; set; }
        #endregion

        #region 报工信息
        /// <summary>
        /// 报工数量
        /// </summary>
        public int ReportedQuantity { get; set; }

        /// <summary>
        /// 报工时间
        /// </summary>
        public DateTime ReportTime { get; set; }
        #endregion

        #region 质检信息
        /// <summary>
        /// 检验时间
        /// </summary>
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        public string InspectionDepartment { get; set; }

        /// <summary>
        /// 检测数量
        /// </summary>
        public int? TestedQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public int? QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public int? UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 检测结果 (如: 合格, 不合格)
        /// </summary>
        public string OverallResult { get; set; }
        #endregion

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
