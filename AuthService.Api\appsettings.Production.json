{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "AuthService": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Host=您的生产环境RDS地址;Port=5432;Database=authservice_prod;Username=生产用户名;Password=************;SSL Mode=Require;Trust Server Certificate=true;"}, "Consul": {"ConsulAddress": "http://您的Consul地址:8500", "ServiceName": "auth-service", "ServiceAddress": "您的服务器内网IP", "ServicePort": 80, "ServiceTags": ["auth", "api", "microservice", "production"], "ServiceMeta": {"version": "1.0.0", "environment": "production"}, "HealthCheckPath": "/health", "HealthCheckInterval": 30, "HealthCheckTimeout": 10, "DeregisterCriticalServiceAfter": 60}, "Jwt": {"Authority": "https://您的认证服务器", "Audience": "auth-api", "RequireHttpsMetadata": true}, "Cors": {"AllowAnyOrigin": false, "AllowedOrigins": ["https://您的前端域名.com"], "AllowAnyMethod": false, "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowAnyHeader": false, "AllowedHeaders": ["Content-Type", "Authorization", "X-Tenant-Id"], "AllowCredentials": true}}