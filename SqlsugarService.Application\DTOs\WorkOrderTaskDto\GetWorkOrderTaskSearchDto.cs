using SqlsugarService.Application.DTOs.Common;
using System;

namespace SqlsugarService.Application.DTOs.WorkOrderTaskDto
{
    /// <summary>
    /// 工单任务查询条件DTO
    /// </summary>
    public class GetWorkOrderTaskSearchDto : PageSearchDto
    {
        /// <summary>
        /// 任务编号
        /// </summary>
        public string? TaskNumber { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 生产工单ID
        /// </summary>
        public Guid? ProductionOrderId { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string? StationName { get; set; }

        /// <summary>
        /// 工艺编号
        /// </summary>
        public string? ProcessCode { get; set; }

        /// <summary>
        /// 工艺名称
        /// </summary>
        public string? ProcessName { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int? Priority { get; set; }

        /// <summary>
        /// 计划开工时间-开始
        /// </summary>
        public DateTime? PlanStartTimeBegin { get; set; }

        /// <summary>
        /// 计划开工时间-结束
        /// </summary>
        public DateTime? PlanStartTimeEnd { get; set; }

        /// <summary>
        /// 计划完工时间-开始
        /// </summary>
        public DateTime? PlanEndTimeBegin { get; set; }

        /// <summary>
        /// 计划完工时间-结束
        /// </summary>
        public DateTime? PlanEndTimeEnd { get; set; }
    }
}
