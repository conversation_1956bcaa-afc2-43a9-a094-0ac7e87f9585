# BOM树形下拉列表查询问题诊断与解决方案

## 问题描述

BOM树形下拉列表查询不到数据，包括：
1. 获取树形下拉列表（根据BOM组成界面优化）
2. 根据BOM ID获取树形下拉列表（根据BOM组成界面优化）
3. **查询不到子BOM** - 子BOM没有正确展开显示
4. **只返回一个节点** - 获取树形下拉列表只返回根节点，没有子节点

## 问题分析

### 1. 编译错误问题

#### 问题：接口方法未声明
**错误信息：**
```
"IProductionPlanService"未包含"CheckBomDataExists"的定义
"IProductionPlanService"未包含"TestBomStructure"的定义
```

**问题分析：**
- 在 `ProductionPlanService` 类中实现了 `TestBomStructure` 和 `CheckBomDataExists` 方法
- 但在 `IProductionPlanService` 接口中没有声明这些方法
- 导致控制器无法调用这些方法

**解决方案：**
在 `IProductionPlanService` 接口中添加方法声明：
```csharp
public interface IProductionPlanService
{
    // ... 现有方法 ...
    
    /// <summary>
    /// 测试BOM数据结构（调试用）
    /// </summary>
    /// <param name="bomId">BOM ID</param>
    /// <returns>测试结果</returns>
    Task<ApiResult<string>> TestBomStructure(Guid bomId);
    
    /// <summary>
    /// 检查BOM数据是否存在（调试用）
    /// </summary>
    /// <returns>数据检查结果</returns>
    Task<ApiResult<string>> CheckBomDataExists();
    
    /// <summary>
    /// 详细分析BOM结构（调试用）
    /// </summary>
    /// <param name="bomId">BOM ID</param>
    /// <returns>详细分析结果</returns>
    Task<ApiResult<string>> AnalyzeBomStructure(Guid bomId);
}
```

### 2. 只返回一个节点问题

#### 问题：GetBomTreeDropdownSimple只返回根节点
**问题分析：**
- 原来的 `GetBomTreeDropdownSimple` 方法只返回每个BOM的根节点
- 没有包含子节点和子BOM的展开
- 导致树形结构不完整

**解决方案：**
1. **重构查询逻辑**：查询所有BOM项目，而不是只查询BOM信息
2. **完整树形构建**：使用 `BuildCompleteBomTree` 方法构建完整的树形结构
3. **递归展开**：支持子BOM的递归展开

### 3. 子BOM查询问题

#### 问题：子BOM没有正确展开
**问题分析：**
- 当前的BOM树形构建逻辑只考虑了同一BOM内的父子关系
- 没有处理子BOM的递归查询
- 当BOM项目中包含子BOM时，没有递归查询子BOM的内容

**解决方案：**
1. **递归查询子BOM**：创建 `BuildBomTreeWithSubBoms` 方法
2. **识别子BOM**：检查物料的 `MaterialType` 是否为 `Product`
3. **递归展开**：当发现子BOM时，递归查询子BOM的内容

### 4. 代码逻辑问题

#### 问题1：字符串比较错误
**原代码问题：**
```csharp
// 错误的字符串比较
var hasChildren = bomItems.Any(child => child.ParentItemId == item.Id.toString());
```

**问题分析：**
- `item.Id` 是 `Guid` 类型
- `item.ParentItemId` 是 `string?` 类型
- 直接比较会导致类型不匹配

**解决方案：**
```csharp
// 修复后的字符串比较
var hasChildren = bomItems.Any(child => child.ParentItemId == item.Id.ToString());
```

#### 问题2：父子关系判断逻辑错误
**原代码问题：**
```csharp
// 错误的父子关系判断
var currentLevelItems = bomItems.Where(item => item.ParentItemId == parentId).ToList();
```

**问题分析：**
- 当 `parentId` 为 `null` 时，应该查找根节点（`ParentItemId` 为 `null` 或空字符串）
- 当 `parentId` 不为 `null` 时，应该查找子节点

**解决方案：**
```csharp
// 修复后的父子关系判断
var currentLevelItems = bomItems.Where(item => 
    (parentId == null && string.IsNullOrEmpty(item.ParentItemId)) ||
    (parentId != null && item.ParentItemId == parentId)
).ToList();
```

### 5. 数据问题

#### 可能的原因：
1. **数据库中没有BOM数据**
2. **BOM数据结构不完整**
3. **软删除标记问题**
4. **外键关联问题**
5. **子BOM关联关系错误**

## 解决方案

### 1. 编译错误修复

**步骤1：更新接口声明**
在 `SqlsugarService.Application/IService/Plan/IProductionPlanService.cs` 中添加缺失的方法声明。

**步骤2：重新编译项目**
```bash
dotnet build
```

### 2. 只返回一个节点问题修复

#### 修复的核心方法：

1. **`GetBomTreeDropdownSimple()`** - 重构为查询所有BOM项目并构建完整树形
2. **`BuildCompleteBomTree()`** - 构建完整的BOM树形结构
3. **`BuildCompleteTreeNode()`** - 构建完整的树节点，支持子BOM展开

#### 修复逻辑：
```csharp
// 查询所有BOM项目
var allBomItems = await bomItemBase.AsQueryable().ToListAsync();

// 按BOM分组构建树形结构
foreach (var bomInfo in allBomInfos)
{
    var bomItems = allBomItems.Where(bi => bi.BomId == bomInfo.Id).ToList();
    var bomTree = await BuildCompleteBomTree(bomItems, bomInfo, sequence, 0, 10);
    result.AddRange(bomTree);
}
```

### 3. 子BOM功能实现

#### 新增的核心方法：

1. **`BuildBomTreeWithSubBoms()`** - 递归构建BOM树，支持子BOM展开
2. **`BuildTreeNodeWithSubBom()`** - 构建单个树节点，支持子BOM展开

#### 子BOM识别逻辑：
```csharp
// 检查物料是否是子BOM（产品类型且有对应的BOM）
var isSubBom = item.Material?.MaterialType == MaterialTypeEnum.Product;
if (isSubBom && item.MaterialId != Guid.Empty)
{
    // 查询该物料是否有对应的BOM
    var subBomInfo = await _db.Queryable<BomInfo>()
        .Where(bi => bi.ProductId == item.MaterialId)
        .FirstAsync();
    
    if (subBomInfo != null)
    {
        // 递归查询子BOM的内容
        subBomChildren = await BuildBomTreeWithSubBoms(subBomInfo.Id, currentDepth + 1, maxDepth);
    }
}
```

### 4. 代码修复

已修复的方法：
- `BuildOptimizedTreeFixed()` - 修复版本的树形构建方法
- `GetBomTreeDropdownSimple()` - 重构为返回完整树形结构，支持子BOM
- `GetBomTreeDropdownByBomId()` - 使用修复后的树形构建，支持子BOM

### 5. 数据检查

#### 新增的调试接口：

1. **检查BOM数据是否存在**
   ```
   GET /api/ProductionPlans/check-bom-data
   ```

2. **测试BOM结构**
   ```
   GET /api/ProductionPlans/test-bom-structure/{bomId}
   ```

3. **详细分析BOM结构**
   ```
   GET /api/ProductionPlans/analyze-bom-structure/{bomId}
   ```

### 6. 测试数据

#### 插入基础测试数据：
```bash
# 执行基础测试数据插入脚本
.\scripts\insert-test-bom-data.ps1
```

#### 插入子BOM测试数据：
```bash
# 执行子BOM测试数据插入脚本
.\scripts\insert-sub-bom-data.ps1
```

#### 测试数据说明：
- **主BOM ID**: `aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa`
- **子BOM ID**: `bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb`
- **主产品**: 主产品A (PROD001)
- **子产品**: 子产品B (PROD002)
- **基础物料**: 基础物料1-4 (MAT001-MAT004)
- **树形结构**: 
  - 主BOM：主产品A -> 基础物料1、基础物料2、子产品B
  - 子BOM：子产品B -> 基础物料3、基础物料4

## 测试步骤

### 1. 修复编译错误
```bash
# 确保接口已更新
# 重新编译项目
dotnet build
```

### 2. 插入测试数据
```bash
# 执行基础测试数据插入脚本
.\scripts\insert-test-bom-data.ps1

# 执行子BOM测试数据插入脚本
.\scripts\insert-sub-bom-data.ps1
```

### 3. 启动应用程序
```bash
dotnet run --project SqlsugarService.API
```

### 4. 测试API接口
```bash
# 使用修复后的测试脚本
.\scripts\test-fixed-bom-tree.ps1

# 使用基础测试脚本
.\scripts\test-bom-api.ps1

# 使用子BOM测试脚本
.\scripts\test-sub-bom-api.ps1

# 或手动测试
curl http://localhost:5000/api/ProductionPlans/check-bom-data
curl http://localhost:5000/api/ProductionPlans/bom-tree
curl http://localhost:5000/api/ProductionPlans/bom-tree/aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa
curl http://localhost:5000/api/ProductionPlans/bom-tree/bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb
```

**预期结果：**
- 编译成功，无错误
- 数据检查接口返回各表的记录数
- BOM树形接口返回完整的树形结构，包含子BOM展开
- 主BOM树形应显示：主产品A -> 基础物料1、基础物料2、子产品B（展开显示基础物料3、基础物料4）
- 子BOM树形应显示：子产品B -> 基础物料3、基础物料4
- **不再只返回一个节点，而是返回完整的树形结构**

## 常见问题排查

### 1. 编译错误
**可能原因：**
- 接口方法未声明
- 命名空间引用错误
- 类型不匹配

**排查方法：**
1. 检查接口声明是否完整
2. 确认命名空间引用正确
3. 验证类型匹配

### 2. 只返回一个节点
**可能原因：**
- 查询逻辑只返回根节点
- 没有正确处理子节点
- 树形构建方法有问题

**排查方法：**
1. 使用修复后的 `GetBomTreeDropdownSimple` 方法
2. 检查 `BuildCompleteBomTree` 方法是否正确
3. 验证数据查询逻辑

### 3. 子BOM没有展开
**可能原因：**
- 物料类型不是Product
- 子BOM关联关系错误
- 递归深度限制

**排查方法：**
1. 检查物料的MaterialType是否为Product
2. 验证BomInfo中的ProductId是否正确关联
3. 检查递归深度设置

### 4. 返回空数据
**可能原因：**
- 数据库中没有BOM数据
- 软删除标记为true
- 外键关联的物料不存在

**排查方法：**
1. 执行数据检查接口
2. 检查数据库中的实际数据
3. 验证外键关联

### 5. 树形结构不完整
**可能原因：**
- ParentItemId设置错误
- 父子关系数据不一致

**排查方法：**
1. 执行BOM结构测试接口
2. 检查ParentItemId的值
3. 验证父子关系的完整性

### 6. 性能问题
**可能原因：**
- 数据量过大
- 查询没有优化
- 递归深度过深

**解决方案：**
1. 添加分页查询
2. 优化SQL查询
3. 使用缓存机制
4. 限制递归深度

## 修复后的API接口

### 1. 获取所有BOM树形下拉列表（支持子BOM，返回完整树形）
```
GET /api/ProductionPlans/bom-tree
```

### 2. 根据BOM ID获取树形下拉列表（支持子BOM）
```
GET /api/ProductionPlans/bom-tree/{bomId}
```

### 3. 检查BOM数据是否存在（调试用）
```
GET /api/ProductionPlans/check-bom-data
```

### 4. 测试BOM结构（调试用）
```
GET /api/ProductionPlans/test-bom-structure/{bomId}
```

### 5. 详细分析BOM结构（调试用）
```
GET /api/ProductionPlans/analyze-bom-structure/{bomId}
```

## 数据结构说明

### TreeNodeDto
```csharp
public class TreeNodeDto
{
    public Guid Id { get; set; }                    // 节点ID
    public int Sequence { get; set; }               // 序号
    public string Name { get; set; }                // 节点名称
    public string NodeType { get; set; }            // 节点类型
    public string? ProductName { get; set; }        // 产品名称
    public string? ProductNumber { get; set; }      // 产品编号
    public string? MaterialName { get; set; }       // 物料名称
    public string? MaterialNumber { get; set; }     // 物料编号
    public decimal? Quantity { get; set; }          // 用量
    public string? Unit { get; set; }               // 单位
    public string? BomNumber { get; set; }          // BOM编号
    public string? BomVersion { get; set; }         // BOM版本
    public decimal? UsageQuantity { get; set; }     // 使用量
    public string? UsageRatio { get; set; }         // 使用比例
    public string? InOutType { get; set; }          // 投入产出类型
    public bool IsExpandable { get; set; }          // 是否可展开
    public int Level { get; set; }                  // 层级
    public string? ParentId { get; set; }           // 父节点ID
    public List<TreeNodeDto> Children { get; set; } // 子节点列表
}
```

### 子BOM数据结构
```
主BOM (BOM001)
├── 主产品A
    ├── 基础物料1
    ├── 基础物料2
    └── 子产品B (子BOM)
        ├── 基础物料3
        └── 基础物料4
```

## 总结

通过以上修复和测试，BOM树形下拉列表查询功能应该能够正常工作，包括子BOM的递归展开和完整的树形结构。主要修复了：

1. **编译错误** - 在接口中添加了缺失的方法声明
2. **只返回一个节点问题** - 重构了 `GetBomTreeDropdownSimple` 方法，现在返回完整的树形结构
3. **子BOM功能** - 实现了子BOM的递归查询和展开
4. **字符串比较逻辑** - 修复了Guid和字符串的比较问题
5. **父子关系判断** - 修复了根节点和子节点的判断逻辑
6. **数据检查功能** - 添加了调试接口来检查数据状态
7. **测试数据** - 提供了完整的测试数据插入脚本，包括子BOM测试数据

如果问题仍然存在，请按照测试步骤逐一排查，确保数据完整性和代码逻辑正确性。 