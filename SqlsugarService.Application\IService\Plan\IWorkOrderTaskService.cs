using SqlsugarService.Application.DTOs.Common;
using SqlsugarService.Application.DTOs.WorkOrderTaskDto;
using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService.Plan
{
    /// <summary>
    /// 工单任务服务接口
    /// </summary>
    public interface IWorkOrderTaskService
    {
        /// <summary>
        /// 获取工单任务分页列表
        /// </summary>
        /// <param name="searchDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<ApiResult<PageResult<List<GetWorkOrderTaskDto>>>> GetWorkOrderTaskListAsync(GetWorkOrderTaskSearchDto searchDto);

        /// <summary>
        /// 根据ID获取工单任务详情
        /// </summary>
        /// <param name="id">工单任务ID</param>
        /// <returns>工单任务详情</returns>
        Task<ApiResult<GetWorkOrderTaskDto>> GetWorkOrderTaskByIdAsync(Guid id);

        /// <summary>
        /// 工单任务报工
        /// </summary>
        /// <param name="workReportDto">报工信息</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> WorkReportAsync(WorkReportDto workReportDto);

        /// <summary>
        /// 批量工单任务报工
        /// </summary>
        /// <param name="workReportDtos">批量报工信息</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> BatchWorkReportAsync(List<WorkReportDto> workReportDtos);

        /// <summary>
        /// 获取工单任务的报工记录
        /// </summary>
        /// <param name="workOrderTaskId">工单任务ID</param>
        /// <returns>报工记录列表</returns>
        Task<ApiResult<List<object>>> GetWorkReportsByTaskIdAsync(Guid workOrderTaskId);

        /// <summary>
        /// 获取所有工单任务（用于调试）
        /// </summary>
        /// <returns>所有工单任务</returns>
        Task<ApiResult<List<GetWorkOrderTaskDto>>> GetAllWorkOrderTasksAsync();

        /// <summary>
        /// 批量派工（支持单个和批量）
        /// </summary>
        /// <param name="dispatchWorkDtos">派工信息列表</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> BatchDispatchWorkAsync(List<DispatchWorkDto> dispatchWorkDtos);
    }
}
