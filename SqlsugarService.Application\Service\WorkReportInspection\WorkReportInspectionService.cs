﻿using SqlsugarService.Application.DTOs.WorkReportInspectionDto;
using SqlsugarService.Application.IService.WorkReportInspection;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.QualityInspection;
using SqlsugarService.Infrastructure.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.Service.WorkReportInspection
{
    public class WorkReportInspectionService: IWorkReportInspectionService
    {
        private readonly IBaseRepository<WorkReportInspectionEntity> workbase;

        public WorkReportInspectionService(IBaseRepository<WorkReportInspectionEntity> workbase)
        {
            this.workbase = workbase;
        }
        ///// <summary>
        ///// 获取列表
        ///// </summary>
        ///// <param name="seach"></param>
        //public async Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetListAsync(Seach seach)
        //{
           
        //}
    }
}
