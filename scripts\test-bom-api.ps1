# 测试BOM API接口的PowerShell脚本
# 用于验证BOM树形下拉列表功能是否正常工作

param(
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== 开始测试BOM API接口 ===" -ForegroundColor Green

# 测试数据
$testBomId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"

# 测试函数
function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$Description
    )
    
    Write-Host "`n测试: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method Get -ContentType "application/json" -TimeoutSec 30
        
        if ($response.isSuc) {
            Write-Host "✅ 成功" -ForegroundColor Green
            if ($response.data -and $response.data.Count -gt 0) {
                Write-Host "返回数据数量: $($response.data.Count)" -ForegroundColor Cyan
            } else {
                Write-Host "返回空数据" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ 失败: $($response.message)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试各个接口
try {
    # 1. 检查BOM数据是否存在
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/check-bom-data" -Description "检查BOM数据是否存在"
    
    # 2. 获取所有BOM树形
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/bom-tree" -Description "获取所有BOM树形"
    
    # 3. 根据BOM ID获取树形
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/bom-tree/$testBomId" -Description "根据BOM ID获取树形"
    
    # 4. 测试BOM结构
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/test-bom-structure/$testBomId" -Description "测试BOM结构"
    
} catch {
    Write-Host "测试过程中发生错误：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== BOM API接口测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "如果所有测试都成功，说明BOM树形下拉列表功能已正常工作。" -ForegroundColor Cyan
Write-Host "如果有测试失败，请检查：" -ForegroundColor Yellow
Write-Host "1. 应用程序是否正在运行" -ForegroundColor White
Write-Host "2. 数据库连接是否正常" -ForegroundColor White
Write-Host "3. 测试数据是否已插入" -ForegroundColor White
Write-Host "4. 网络连接是否正常" -ForegroundColor White 