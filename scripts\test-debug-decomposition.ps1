# 调试BOM分解功能

param(
    [string]$BaseUrl = "http://localhost:5000",
    [string]$ProductionPlanId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"
)

Write-Host "=== 调试BOM分解功能 ===" -ForegroundColor Green
Write-Host "生产计划ID: $ProductionPlanId" -ForegroundColor Yellow

# 测试函数
function Test-Decomposition {
    param(
        [string]$Url,
        [string]$Method,
        [string]$Description,
        [object]$Body = $null
    )
    
    Write-Host "`n测试: $Description" -ForegroundColor Cyan
    Write-Host "URL: $Url" -ForegroundColor Gray
    Write-Host "方法: $Method" -ForegroundColor Gray
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Method -eq "POST") {
            $response = Invoke-RestMethod -Uri $Url -Method Post -Headers $headers -Body ($Body | ConvertTo-Json -Depth 10) -TimeoutSec 60
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method Get -Headers $headers -TimeoutSec 30
        }
        
        if ($response.isSuc) {
            Write-Host "✅ 成功" -ForegroundColor Green
            Show-Debug-Result $response.data
        } else {
            Write-Host "❌ 失败: $($response.message)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示调试结果
function Show-Debug-Result {
    param(
        [object]$Data
    )
    
    if ($Data.Steps -and $Data.Steps.Count -gt 0) {
        Write-Host "调试步骤分析：" -ForegroundColor Yellow
        foreach ($step in $Data.Steps) {
            Write-Host "  $($step.Step): $($step.Status)" -ForegroundColor $(if($step.Status -eq "成功") { "Green" } else { "Red" })
            
            if ($step.Data) {
                Write-Host "    数据详情:" -ForegroundColor White
                $step.Data.PSObject.Properties | ForEach-Object {
                    Write-Host "      $($_.Name): $($_.Value)" -ForegroundColor Gray
                }
            }
        }
    }
    
    if ($Data.TotalOrders) {
        Write-Host "生成工单数: $($Data.TotalOrders)" -ForegroundColor White
    }
    
    if ($Data.Orders -and $Data.Orders.Count -gt 0) {
        Write-Host "生成的工单：" -ForegroundColor Yellow
        foreach ($order in $Data.Orders) {
            Write-Host "  - $($order.OrderName) (编号: $($order.OrderNumber))" -ForegroundColor White
            Write-Host "    生产计划ID: $($order.ProductionPlanId)" -ForegroundColor Gray
            Write-Host "    产品: $($order.ProductName), 数量: $($order.PlanQuantity) $($order.Unit)" -ForegroundColor Gray
        }
    }
}

# 执行测试
try {
    Write-Host "=== 开始调试分解功能 ===" -ForegroundColor Cyan
    
    # 1. 调试分解过程
    Test-Decomposition -Url "$BaseUrl/api/BomDecomposition/debug/$ProductionPlanId" -Method "GET" -Description "调试分解过程"
    
    # 2. 执行分解
    Test-Decomposition -Url "$BaseUrl/api/BomDecomposition/decompose/$ProductionPlanId" -Method "POST" -Description "执行分解"
    
} catch {
    Write-Host "测试过程中发生错误：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 调试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "调试说明：" -ForegroundColor Yellow
Write-Host "1. 查看每个步骤的详细数据" -ForegroundColor White
Write-Host "2. 检查字段值是否正确设置" -ForegroundColor White
Write-Host "3. 验证数据库约束问题" -ForegroundColor White 