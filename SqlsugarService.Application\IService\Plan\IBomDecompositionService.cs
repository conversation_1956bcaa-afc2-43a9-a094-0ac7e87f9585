﻿using SqlsugarService.Application.Service.Plan;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.Plan;

namespace SqlsugarService.Application.IService.Plan
{
    public interface IBomDecompositionService
    {
        Task<ApiResult<BomDecompositionResult>> DecomposeProductionPlan(Guid productionPlanId);
        Task<ApiResult<List<ProductionOrder>>> GetDeletedProductionOrders(Guid productionPlanId);
        Task<ApiResult<BomDecompositionPreview>> PreviewDecomposition(Guid productionPlanId);
        Task<ApiResult<BomDecompositionResult>> RestoreDeletedOrders(Guid productionPlanId, List<Guid> orderIds);
        Task<ApiResult<BomDecompositionResult>> UndoDecomposition(Guid productionPlanId);
        Task<ApiResult<BomDecompositionDetail>> GetBomDecompositionDetail(Guid productionPlanId);
    }
}