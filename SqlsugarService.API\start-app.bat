@echo off
echo === SqlsugarService.API 启动脚本 ===

REM 检查发布目录
if not exist "bin\Release\net6.0\publish" (
    echo 错误: 发布目录不存在
    echo 请先运行: dotnet publish --configuration Release
    pause
    exit /b 1
)

REM 设置环境变量
set ASPNETCORE_ENVIRONMENT=Development
set ASPNETCORE_URLS=http://localhost:8080

echo 环境设置:
echo   环境: %ASPNETCORE_ENVIRONMENT%
echo   URL: %ASPNETCORE_URLS%
echo.

REM 进入发布目录
cd /d "bin\Release\net6.0\publish"

echo 启动应用程序...
echo 按 Ctrl+C 停止应用
echo 访问地址: http://localhost:8080
echo Swagger文档: http://localhost:8080/swagger
echo 健康检查: http://localhost:8080/health
echo.

REM 启动浏览器（可选）
start http://localhost:8080

REM 启动应用
dotnet SqlsugarService.API.dll

echo.
echo 应用已停止
pause
