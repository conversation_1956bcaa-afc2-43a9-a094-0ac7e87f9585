graph TB
    %% 外部系统
    Client[🖥️ 客户端应用]
    Kong[🚪 Kong API Gateway<br/>端口: 8000]
    Consul[🔍 Consul Service Discovery<br/>端口: 8500]
    PostgreSQL[🗄️ PostgreSQL Database<br/>端口: 5432]
    
    %% AuthService 应用层次
    subgraph "AuthService 微服务架构"
        subgraph "表示层 (AuthService.Api)"
            Controllers[📋 Controllers<br/>• UserController<br/>• DynamicApiController<br/>• HealthController]
            Middleware[⚙️ Middleware<br/>• DynamicRoutingMiddleware<br/>• Authentication<br/>• CORS]
            Program[🚀 Program.cs<br/>• 应用启动<br/>• 服务配置<br/>• 中间件管道]
        end
        
        subgraph "应用层 (AuthService.Application)"
            Services[🔧 Application Services<br/>• DynamicApiService<br/>• 业务逻辑处理]
            AppMiddleware[🔄 Application Middleware<br/>• 动态路由处理<br/>• 请求转换]
        end
        
        subgraph "基础设施层 (AuthService.Infrastructure)"
            Repositories[📚 Repositories<br/>• BaseRepository<br/>• UserRepository<br/>• DynamicApiEndpointRepository]
            Database[💾 Database Layer<br/>• DatabaseInitializer<br/>• MigrationRunner<br/>• FluentMigrator]
            Extensions[🔌 Extensions<br/>• ServiceCollection<br/>• 依赖注入配置]
        end
        
        subgraph "领域层 (AuthService.Domain)"
            Entities[📦 Domain Entities<br/>• User<br/>• DynamicApiEndpoint]
            Interfaces[🔗 Repository Interfaces<br/>• IUserRepository<br/>• IDynamicApiEndpointRepository]
        end
    end
    
    %% 请求流程
    Client -->|1. HTTP Request| Kong
    Kong -->|2. Route to Service| Controllers
    Controllers -->|3. Business Logic| Services
    Services -->|4. Data Access| Repositories
    Repositories -->|5. Database Query<br/>Dapper ORM| PostgreSQL
    
    %% 服务发现流程
    Program -->|Service Registration| Consul
    Kong -->|Service Discovery| Consul
    Consul -->|Health Check| Controllers
    
    %% 数据库迁移流程
    Program -->|Startup Migration| Database
    Database -->|FluentMigrator| PostgreSQL
    
    %% 认证流程
    Controllers -->|JWT Validation| Middleware
    Middleware -->|User Authentication| Services
    Services -->|User Data| Repositories
    
    %% 动态API流程
    Controllers -->|Dynamic Routing| AppMiddleware
    AppMiddleware -->|Route Configuration| Services
    Services -->|Endpoint Data| Repositories
    
    %% 响应流程
    PostgreSQL -->|6. Query Result| Repositories
    Repositories -->|7. Domain Objects| Services
    Services -->|8. Business Result| Controllers
    Controllers -->|9. HTTP Response| Kong
    Kong -->|10. Final Response| Client
    
    %% 样式定义
    classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef application fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef infrastructure fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef domain fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class Client,Kong,Consul,PostgreSQL external
    class Controllers,Middleware,Program api
    class Services,AppMiddleware application
    class Repositories,Database,Extensions infrastructure
    class Entities,Interfaces domain
