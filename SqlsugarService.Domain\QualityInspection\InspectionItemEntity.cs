using SqlSugar;
using SqlsugarService.Domain.Common;
using System;

namespace SqlsugarService.Domain.QualityInspection
{
    /// <summary>
    /// 检测项目实体类
    /// </summary>
    public class InspectionItemEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 检测项目名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 检测项目编码
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 检测类型 (如: 目测, 功能测试)
        /// </summary>
        public string InspectionType { get; set; }

        /// <summary>
        /// 检测工具
        /// </summary>
        public string InspectionTool { get; set; }

        /// <summary>
        /// 状态 (如: 启用, 禁用)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
