﻿using SqlsugarService.Application.DTOs.PlanDto;
using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService.Plan
{
    public interface IProductionPlanService
    {
        Task<ApiResult> AddProductionPlan(InsertupdateproductionplanDto dto);
        Task<ApiResult> UpdateProductionPlan(InsertupdateproductionplanDto dto);
        Task<ApiResult<PageResult<List<GetproductionplanDto>>>> GetProductionPlanList(GetproductionplanSearchDto seach);
        Task<ApiResult> DeleteProductionPlan(Guid id);
        Task<ApiResult<List<TreeNodeDto>>> GetBomTreeDropdownSimple();
        Task<ApiResult<List<BomTreeDto>>> GetBomTreeDropdownByBomId(Guid bomId);
        Task<ApiResult<List<BomTreeStructureDto>>> GetBomTreeStructure(Guid bomId);
        Task<ApiResult<string>> TestBomStructure(Guid bomId);
        Task<ApiResult<string>> CheckBomDataExists();

        // 为 MES 工具服务提供的异步方法别名
        Task<ApiResult<PageResult<List<GetproductionplanDto>>>> GetProductionPlanListAsync(GetproductionplanSearchDto seach);
        Task<bool> InsertProductionPlanAsync(InsertupdateproductionplanDto dto);
        Task<bool> UpdateProductionPlanAsync(InsertupdateproductionplanDto dto);
    }
}
