﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.PlanDto;
using SqlsugarService.Application.IService.Plan;
using SqlsugarService.Application.Until;

namespace SqlsugarService.API.Controllers.PlanController
{
    /// <summary>
    /// 生产计划
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ProductionPlansController : ControllerBase
    {
        private readonly IProductionPlanService _productionPlanService;

        public ProductionPlansController(IProductionPlanService productionPlanService)
        {
            _productionPlanService = productionPlanService;
        }
        /// <summary>
        /// 新增生产计划
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ApiResult> AddProductionPlan(InsertupdateproductionplanDto dto)
        {
            return await _productionPlanService.AddProductionPlan(dto);
        }

        /// <summary>
        /// 显示生产计划列表(分页)
        /// </summary>
        /// <param name="seach">分页查询参数</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<GetproductionplanDto>>>> GetProductionPlanList([FromQuery] GetproductionplanSearchDto seach)
        {
            return await _productionPlanService.GetProductionPlanList(seach);
        }
        /// <summary>
        /// 修改生产计划
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPut("UpdateProductionPlan")]
        public async Task<ApiResult> UpdateProductionPlan(InsertupdateproductionplanDto dto)
        {
            return await _productionPlanService.UpdateProductionPlan(dto);
        }

        /// <summary>
        /// 获取BOM树形下拉列表
        /// </summary>
        /// <returns>树形结构的BOM下拉列表</returns>
        [HttpGet("bom-tree")]
        public async Task<ApiResult<List<TreeNodeDto>>> GetBomTreeDropdown()
        {
            return await _productionPlanService.GetBomTreeDropdownSimple();
        }

        /// <summary>
        /// 根据BOM ID获取树形下拉列表（根据BOM组成界面优化）
        /// </summary>
        /// <param name="bomId">BOM ID</param>
        /// <returns>树形结构的下拉列表</returns>
        [HttpGet("bom-tree/{bomId}")]
        public async Task<ApiResult<List<BomTreeDto>>> GetBomTreeDropdownByBomId(Guid bomId)
        {
            return await _productionPlanService.GetBomTreeDropdownByBomId(bomId);
        }

        /// <summary>
        /// 获取BOM树形结构（关联BomInfo、ProductEntity、MaterialEntity表）
        /// </summary>
        /// <param name="bomId">BOM ID</param>
        /// <returns>BOM树形结构</returns>
        [HttpGet("bom-structure/{bomId}")]
        public async Task<ApiResult<List<BomTreeStructureDto>>> GetBomTreeStructure(Guid bomId)
        {
            return await _productionPlanService.GetBomTreeStructure(bomId);
        }

        /// <summary>
        /// 测试BOM数据结构（调试用）
        /// </summary>
        /// <param name="bomId">BOM ID</param>
        /// <returns>BOM结构测试结果</returns>
        [HttpGet("test-bom-structure/{bomId}")]
        public async Task<ApiResult<string>> TestBomStructure(Guid bomId)
        {
            return await _productionPlanService.TestBomStructure(bomId);
        }

        /// <summary>
        /// 检查BOM数据是否存在（调试用）
        /// </summary>
        /// <returns>数据检查结果</returns>
        [HttpGet("check-bom-data")]
        public async Task<ApiResult<string>> CheckBomDataExists()
        {
            return await _productionPlanService.CheckBomDataExists();
        }

        
    }
}
