﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.SalesDto;
using SqlsugarService.Application.IService.Sales;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.InventoryChange;

namespace SqlsugarService.API.Controllers.PlanController
{
    /// <summary>
    /// 销售出库单
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class SalesController : ControllerBase
    {
        private readonly ISalesService salesService;

        public SalesController(ISalesService salesService)
        {
            this.salesService = salesService;
        }
        /// <summary>
        /// 获取销售订单列表
        /// </summary>
        /// <param name="seach"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<getsalesorderDto>>>> GetSalesOrderList([FromQuery] GetsalesorderSearchDto seach)
        {
            return await salesService.GetSalesOrderList(seach);
        }
        /// <summary>
        /// 新增销售订单
        /// </summary>
        [HttpPost]
        public async Task<ApiResult> AddSalesOrder(insertupdatesalesorderDto salesoutbounddto)
        {
            return await salesService.AddSalesOrder(salesoutbounddto);
        }
    }
}
