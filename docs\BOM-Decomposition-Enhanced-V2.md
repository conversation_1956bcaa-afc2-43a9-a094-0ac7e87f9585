# BOM分解功能增强 V2

## 概述

基于新的BOM树形结构（`BomTreeStructureDto`）实现的增强版生产计划分解功能，能够更准确地识别和分解BOM中的产品节点，生成多个包含完整产品信息的生产工单。

## 主要改进

### 1. 使用新的BOM树形结构
- **原来**：使用 `GetBomTreeDropdownByBomId` 方法获取简化的BOM树
- **现在**：使用 `GetBomTreeStructure` 方法获取完整的BOM树形结构
- **优势**：包含更详细的产品和物料信息，支持复杂的父子关系

### 2. 增强的查询逻辑
```csharp
// 查询条件扩展：包含当前BOM的项目 + parentitemid指向当前BOM的项目
.Where(bi => (bi.BomId == bomId || bi.ParentItemId == bomId.ToString()) && !bi.IsDeleted)
```

### 3. 改进的父子关系处理
```csharp
// 支持两种父子关系模式：
// 1. 直接ID匹配：parentitemid = 某个BOM项目的ID
// 2. BOM ID匹配：parentitemid = 某个BOM的ID
```

### 4. 完整的产品信息获取
- **产品识别**：支持多种产品识别方式（BOM编号、产品类型、产品名称）
- **信息获取**：从BOM树中获取完整的产品信息（名称、编号、规格、BOM信息等）
- **工单生成**：生成包含完整产品信息的生产工单

## 核心方法

### 1. DecomposeProductionPlan
**功能**：分解生产计划为生产工单
**流程**：
1. 验证生产计划状态
2. 获取BOM树形结构（使用 `GetBomTreeStructure`）
3. 分析BOM结构，识别产品节点
4. 生成生产工单
5. 计算工单时间安排
6. 保存生产工单
7. 更新生产计划状态为"已分解"

### 2. AnalyzeBomTreeStructureForDecomposition
**功能**：分析BOM树形结构，识别需要生成工单的产品
**逻辑**：
- 遍历所有BOM树节点
- 识别有BOM编号的产品节点
- 计算实际需求量
- 生成分解项目

### 3. GenerateProductionOrdersFromBomTreeStructure
**功能**：从BOM树生成生产工单
**特点**：
- 只为产品类型的节点生成工单
- 自动生成工单编号
- 设置完整的工单信息

## 数据结构

### BomTreeStructureDto
```csharp
public class BomTreeStructureDto
{
    // BOM项目信息
    public Guid Id { get; set; }
    public Guid BomId { get; set; }
    public Guid MaterialId { get; set; }
    public string ParentItemId { get; set; }
    public decimal Quantity { get; set; }
    public string Unit { get; set; }
    
    // BOM信息
    public string BomNumber { get; set; }
    public string BomVersion { get; set; }
    
    // 产品信息
    public string ProductName { get; set; }
    public string ProductNumber { get; set; }
    public string ProductSpecification { get; set; }
    
    // 物料信息
    public string MaterialName { get; set; }
    public string MaterialNumber { get; set; }
    public string MaterialSpecification { get; set; }
    public MaterialTypeEnum MaterialType { get; set; }
    
    // 计算字段
    public decimal UsageQuantity { get; set; }
    public string UsageRatio { get; set; }
    
    // 树形结构
    public int Sequence { get; set; }
    public string DisplayName { get; set; }
    public bool IsExpandable { get; set; }
    public int Level { get; set; }
    public List<BomTreeStructureDto> Children { get; set; }
}
```

### DecompositionItem（增强版）
```csharp
public class DecompositionItem
{
    // 基本信息
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; }
    public string MaterialNumber { get; set; }
    public decimal RequiredQuantity { get; set; }
    public string Unit { get; set; }
    public int Level { get; set; }
    public bool IsProduct { get; set; }
    public string ParentNodeId { get; set; }
    public MaterialTypeEnum MaterialType { get; set; }
    
    // 新增产品详细信息字段
    public string ProductSpecification { get; set; }
    public string BomNumber { get; set; }
    public string BomVersion { get; set; }
    public string DisplayName { get; set; }
    public string DisplayProductNumber { get; set; }
    public string DisplaySpecification { get; set; }
}
```

## 产品识别逻辑

### 1. 产品节点识别
```csharp
// 判断是否为产品节点
var isProduct = (!string.IsNullOrEmpty(node.BomNumber) && node.UsageQuantity > 0) ||
               (node.MaterialType == MaterialTypeEnum.Product) ||
               (!string.IsNullOrEmpty(node.ProductName) && node.UsageQuantity > 0);
```

### 2. 产品信息获取
```csharp
// 获取产品ID（优先使用MaterialId，其次尝试解析ProductNumber）
var productId = node.MaterialId != Guid.Empty ? node.MaterialId : GetMaterialIdFromProductNumber(node.ProductNumber);

// 获取产品名称（优先使用ProductName，其次使用MaterialName）
var productName = !string.IsNullOrEmpty(node.ProductName) ? node.ProductName : node.MaterialName;

// 获取产品编号（优先使用ProductNumber，其次使用MaterialNumber）
var productNumber = !string.IsNullOrEmpty(node.ProductNumber) ? node.ProductNumber : node.MaterialNumber;

// 获取规格信息（优先使用ProductSpecification，其次使用MaterialSpecification）
var specification = !string.IsNullOrEmpty(node.ProductSpecification) ? node.ProductSpecification : node.MaterialSpecification;
```

### 3. 工单信息填充
```csharp
var productionOrder = new ProductionOrder
{
    OrderName = $"{item.DisplayName}生产工单",
    ProductName = item.DisplayName ?? item.MaterialName ?? "未知产品",
    ProductNumber = item.DisplayProductNumber ?? item.MaterialNumber ?? "未知编号",
    Specification = item.DisplaySpecification ?? item.ProductSpecification ?? "未知规格",
    // ... 其他字段
};
```

## API接口

### 1. 分解生产计划
```
POST /api/BomDecomposition/decompose/{productionPlanId}
```

### 2. 预览分解结果
```
GET /api/BomDecomposition/preview/{productionPlanId}
```

### 3. 获取分解详情
```
GET /api/BomDecomposition/detail/{productionPlanId}
```

### 4. 获取BOM树形结构
```
GET /api/ProductionPlans/bom-structure/{bomId}
```

## 测试脚本

### 1. BOM查询修复测试
```powershell
.\scripts\test-bom-query-fix.ps1 -BomId "6529fcc0-7c05-42f9-a27a-6b968c95956b"
```

### 2. BOM分解增强测试 V2
```powershell
.\scripts\test-bom-decomposition-enhanced-v2.ps1 -ProductionPlanId "your-production-plan-id"
```

### 3. BOM分解产品信息测试
```powershell
.\scripts\test-bom-decomposition-with-product-info.ps1 -ProductionPlanId "your-production-plan-id"
```

## 分解逻辑

### 1. 产品识别
- 有BOM编号的节点被视为产品
- MaterialType为Product的节点被视为产品
- 有产品名称的节点被视为产品
- 产品节点会生成生产工单
- 物料节点不生成工单

### 2. 数量计算
```csharp
// 实际需求量 = 节点用量 × 父级数量
var actualQuantity = node.UsageQuantity * parentQuantity;
```

### 3. 工单生成
- 每个产品节点生成一个生产工单
- 工单包含完整的产品信息（名称、编号、规格、BOM信息等）
- 自动设置时间安排

### 4. 状态管理
- 分解前：状态 = 0（未分解）
- 分解后：状态 = 1（已分解）

## 优势

1. **更准确的BOM结构**：支持复杂的父子关系
2. **完整的产品信息**：包含详细的产品和物料数据
3. **灵活的查询逻辑**：支持跨BOM的关联查询
4. **完善的分解流程**：从预览到执行的全流程支持
5. **详细的调试信息**：便于问题排查和优化
6. **完整的产品信息**：工单包含所有必要的产品详细信息

## 注意事项

1. **数据完整性**：确保BOM数据完整，包含必要的父子关系
2. **状态检查**：只有未分解状态的生产计划才能进行分解
3. **工单编号**：系统自动生成唯一的工单编号
4. **时间安排**：工单时间基于生产计划的时间范围进行分配
5. **错误处理**：完善的异常处理和错误提示机制
6. **产品信息**：确保BOM树中包含完整的产品信息字段 