# 测试子BOM API接口的PowerShell脚本
# 用于验证BOM树形下拉列表的子BOM展开功能

param(
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== 开始测试子BOM API接口 ===" -ForegroundColor Green

# 测试数据
$mainBomId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"  # 主BOM ID
$subBomId = "bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"    # 子BOM ID

# 测试函数
function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$Description
    )
    
    Write-Host "`n测试: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method Get -ContentType "application/json" -TimeoutSec 30
        
        if ($response.isSuc) {
            Write-Host "✅ 成功" -ForegroundColor Green
            if ($response.data -and $response.data.Count -gt 0) {
                Write-Host "返回数据数量: $($response.data.Count)" -ForegroundColor Cyan
                # 显示树形结构概览
                Show-TreeStructure $response.data
            } else {
                Write-Host "返回空数据" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ 失败: $($response.message)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示树形结构概览
function Show-TreeStructure {
    param(
        [array]$Nodes,
        [int]$Level = 0
    )
    
    foreach ($node in $Nodes) {
        $indent = "  " * $Level
        $nodeInfo = "$($node.name) (类型: $($node.nodeType), 层级: $($node.level))"
        if ($node.children -and $node.children.Count -gt 0) {
            $nodeInfo += " [子节点: $($node.children.Count)]"
        }
        Write-Host "$indent- $nodeInfo" -ForegroundColor White
        
        if ($node.children -and $node.children.Count -gt 0) {
            Show-TreeStructure $node.children ($Level + 1)
        }
    }
}

# 测试各个接口
try {
    Write-Host "=== 基础数据检查 ===" -ForegroundColor Cyan
    # 1. 检查BOM数据是否存在
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/check-bom-data" -Description "检查BOM数据是否存在"
    
    Write-Host "`n=== 子BOM功能测试 ===" -ForegroundColor Cyan
    # 2. 获取所有BOM树形（包含子BOM）
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/bom-tree" -Description "获取所有BOM树形（包含子BOM）"
    
    # 3. 根据主BOM ID获取树形
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/bom-tree/$mainBomId" -Description "根据主BOM ID获取树形"
    
    # 4. 根据子BOM ID获取树形
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/bom-tree/$subBomId" -Description "根据子BOM ID获取树形"
    
    Write-Host "`n=== 结构测试 ===" -ForegroundColor Cyan
    # 5. 测试主BOM结构
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/test-bom-structure/$mainBomId" -Description "测试主BOM结构"
    
    # 6. 测试子BOM结构
    Test-ApiEndpoint -Url "$BaseUrl/api/ProductionPlans/test-bom-structure/$subBomId" -Description "测试子BOM结构"
    
} catch {
    Write-Host "测试过程中发生错误：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 子BOM API接口测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "预期结果说明：" -ForegroundColor Cyan
Write-Host "1. 主BOM树形应包含：主产品A -> 基础物料1、基础物料2、子产品B" -ForegroundColor White
Write-Host "2. 子产品B应展开显示：子产品B -> 基础物料3、基础物料4" -ForegroundColor White
Write-Host "3. 子BOM树形应包含：子产品B -> 基础物料3、基础物料4" -ForegroundColor White
Write-Host ""
Write-Host "如果测试成功，说明子BOM递归展开功能正常工作。" -ForegroundColor Cyan
Write-Host "如果有测试失败，请检查：" -ForegroundColor Yellow
Write-Host "1. 应用程序是否正在运行" -ForegroundColor White
Write-Host "2. 数据库连接是否正常" -ForegroundColor White
Write-Host "3. 子BOM测试数据是否已插入" -ForegroundColor White
Write-Host "4. 网络连接是否正常" -ForegroundColor White 