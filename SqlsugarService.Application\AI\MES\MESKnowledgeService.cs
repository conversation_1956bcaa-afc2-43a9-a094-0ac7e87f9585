using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SqlsugarService.Application.AI.MES
{
    /// <summary>
    /// MES知识库服务接口
    /// </summary>
    public interface IMESKnowledgeService
    {
        /// <summary>
        /// 搜索知识库
        /// </summary>
        /// <param name="query">查询关键词</param>
        /// <param name="category">知识分类</param>
        /// <returns>相关知识条目</returns>
        Task<List<MESKnowledgeItemDto>> SearchKnowledgeAsync(string query, string? category = null);

        /// <summary>
        /// 获取常见问题
        /// </summary>
        /// <param name="category">问题分类</param>
        /// <returns>常见问题列表</returns>
        Task<List<MESFAQDto>> GetFAQsAsync(string? category = null);

        /// <summary>
        /// 获取操作指南
        /// </summary>
        /// <param name="operation">操作类型</param>
        /// <returns>操作指南</returns>
        Task<MESOperationGuideDto?> GetOperationGuideAsync(string operation);

        /// <summary>
        /// 获取术语解释
        /// </summary>
        /// <param name="term">术语</param>
        /// <returns>术语解释</returns>
        Task<MESTermDefinitionDto?> GetTermDefinitionAsync(string term);

        /// <summary>
        /// 获取系统帮助信息
        /// </summary>
        /// <param name="module">系统模块</param>
        /// <returns>帮助信息</returns>
        Task<MESHelpInfoDto?> GetHelpInfoAsync(string module);
    }

    /// <summary>
    /// MES知识库服务实现
    /// </summary>
    public class MESKnowledgeService : IMESKnowledgeService
    {
        private readonly ILogger<MESKnowledgeService> _logger;
        private readonly List<MESKnowledgeItemDto> _knowledgeBase;
        private readonly List<MESFAQDto> _faqs;
        private readonly Dictionary<string, MESOperationGuideDto> _operationGuides;
        private readonly Dictionary<string, MESTermDefinitionDto> _termDefinitions;
        private readonly Dictionary<string, MESHelpInfoDto> _helpInfos;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public MESKnowledgeService(ILogger<MESKnowledgeService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 初始化知识库数据
            _knowledgeBase = InitializeKnowledgeBase();
            _faqs = InitializeFAQs();
            _operationGuides = InitializeOperationGuides();
            _termDefinitions = InitializeTermDefinitions();
            _helpInfos = InitializeHelpInfos();
        }

        /// <summary>
        /// 搜索知识库
        /// </summary>
        /// <param name="query">查询关键词</param>
        /// <param name="category">知识分类</param>
        /// <returns>相关知识条目</returns>
        public async Task<List<MESKnowledgeItemDto>> SearchKnowledgeAsync(string query, string? category = null)
        {
            await Task.Delay(50); // 模拟异步操作

            try
            {
                _logger.LogInformation($"搜索MES知识库，关键词: {query}, 分类: {category}");

                var results = _knowledgeBase.AsQueryable();

                // 按分类过滤
                if (!string.IsNullOrEmpty(category))
                {
                    results = results.Where(k => k.Category.Equals(category, StringComparison.OrdinalIgnoreCase));
                }

                // 按关键词搜索
                if (!string.IsNullOrEmpty(query))
                {
                    var keywords = query.Split(new[] { ' ', '，', ',' }, StringSplitOptions.RemoveEmptyEntries);
                    results = results.Where(k =>
                        keywords.Any(keyword =>
                            k.Title.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                            k.Content.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                            k.Keywords.Any(kw => kw.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                        )
                    );
                }

                // 按相关度排序
                var resultList = results.ToList();
                if (!string.IsNullOrEmpty(query))
                {
                    resultList = resultList.OrderByDescending(k => CalculateRelevance(k, query)).ToList();
                }

                var finalResults = resultList.Take(10).ToList();

                _logger.LogInformation($"知识库搜索完成，找到 {finalResults.Count} 条相关内容");

                return finalResults;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索知识库时发生错误");
                return new List<MESKnowledgeItemDto>();
            }
        }

        /// <summary>
        /// 获取常见问题
        /// </summary>
        /// <param name="category">问题分类</param>
        /// <returns>常见问题列表</returns>
        public async Task<List<MESFAQDto>> GetFAQsAsync(string? category = null)
        {
            await Task.Delay(50);

            try
            {
                _logger.LogInformation($"获取MES常见问题，分类: {category}");

                var results = _faqs.AsQueryable();

                if (!string.IsNullOrEmpty(category))
                {
                    results = results.Where(f => f.Category.Equals(category, StringComparison.OrdinalIgnoreCase));
                }

                var finalResults = results.OrderBy(f => f.Order).ToList();

                _logger.LogInformation($"获取常见问题完成，共 {finalResults.Count} 个问题");

                return finalResults;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取常见问题时发生错误");
                return new List<MESFAQDto>();
            }
        }

        /// <summary>
        /// 获取操作指南
        /// </summary>
        /// <param name="operation">操作类型</param>
        /// <returns>操作指南</returns>
        public async Task<MESOperationGuideDto?> GetOperationGuideAsync(string operation)
        {
            await Task.Delay(50);

            try
            {
                _logger.LogInformation($"获取MES操作指南: {operation}");

                if (_operationGuides.TryGetValue(operation.ToLower(), out var guide))
                {
                    _logger.LogInformation($"找到操作指南: {guide.Title}");
                    return guide;
                }

                _logger.LogWarning($"未找到操作指南: {operation}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取操作指南时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 获取术语解释
        /// </summary>
        /// <param name="term">术语</param>
        /// <returns>术语解释</returns>
        public async Task<MESTermDefinitionDto?> GetTermDefinitionAsync(string term)
        {
            await Task.Delay(50);

            try
            {
                _logger.LogInformation($"获取MES术语解释: {term}");

                if (_termDefinitions.TryGetValue(term.ToLower(), out var definition))
                {
                    _logger.LogInformation($"找到术语解释: {definition.Term}");
                    return definition;
                }

                _logger.LogWarning($"未找到术语解释: {term}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取术语解释时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 获取系统帮助信息
        /// </summary>
        /// <param name="module">系统模块</param>
        /// <returns>帮助信息</returns>
        public async Task<MESHelpInfoDto?> GetHelpInfoAsync(string module)
        {
            await Task.Delay(50);

            try
            {
                _logger.LogInformation($"获取MES系统帮助: {module}");

                if (_helpInfos.TryGetValue(module.ToLower(), out var helpInfo))
                {
                    _logger.LogInformation($"找到系统帮助: {helpInfo.ModuleName}");
                    return helpInfo;
                }

                _logger.LogWarning($"未找到系统帮助: {module}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统帮助时发生错误");
                return null;
            }
        }

        #region 私有方法

        /// <summary>
        /// 计算内容相关度
        /// </summary>
        /// <param name="item">知识条目</param>
        /// <param name="query">查询词</param>
        /// <returns>相关度分数</returns>
        private double CalculateRelevance(MESKnowledgeItemDto item, string query)
        {
            double score = 0;
            var keywords = query.Split(new[] { ' ', '，', ',' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var keyword in keywords)
            {
                // 标题匹配权重更高
                if (item.Title.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                    score += 3;

                // 关键词匹配
                if (item.Keywords.Any(kw => kw.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
                    score += 2;

                // 内容匹配
                if (item.Content.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                    score += 1;
            }

            return score;
        }

        /// <summary>
        /// 初始化知识库
        /// </summary>
        /// <returns>知识库条目列表</returns>
        private List<MESKnowledgeItemDto> InitializeKnowledgeBase()
        {
            return new List<MESKnowledgeItemDto>
            {
                new MESKnowledgeItemDto
                {
                    Id = "KB001",
                    Title = "生产订单管理",
                    Category = "生产管理",
                    Content = "生产订单是MES系统中的核心概念，用于指导和跟踪产品的生产过程。包括订单创建、状态更新、进度跟踪等功能。",
                    Keywords = new List<string> { "生产订单", "订单管理", "生产计划", "进度跟踪" },
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow.AddDays(-5)
                },
                new MESKnowledgeItemDto
                {
                    Id = "KB002",
                    Title = "物料库存查询",
                    Category = "库存管理",
                    Content = "通过MES系统可以实时查询物料的库存情况，包括当前库存量、安全库存、物料位置等信息。支持按物料编码、名称等条件进行查询。",
                    Keywords = new List<string> { "物料", "库存", "查询", "库存管理" },
                    CreatedAt = DateTime.UtcNow.AddDays(-25),
                    UpdatedAt = DateTime.UtcNow.AddDays(-3)
                },
                new MESKnowledgeItemDto
                {
                    Id = "KB003",
                    Title = "BOM物料清单",
                    Category = "工艺管理",
                    Content = "BOM（Bill of Materials）是产品的物料清单，定义了生产某个产品所需的所有物料、数量和层级关系。BOM是生产计划和物料需求计划的基础。",
                    Keywords = new List<string> { "BOM", "物料清单", "工艺", "产品结构" },
                    CreatedAt = DateTime.UtcNow.AddDays(-20),
                    UpdatedAt = DateTime.UtcNow.AddDays(-2)
                },
                new MESKnowledgeItemDto
                {
                    Id = "KB004",
                    Title = "质量检验管理",
                    Category = "质量管理",
                    Content = "质量检验是确保产品质量的重要环节，包括进料检验、过程检验、成品检验等。系统支持检验计划制定、检验结果记录、不良品处理等功能。",
                    Keywords = new List<string> { "质量检验", "质量管理", "检验计划", "不良品" },
                    CreatedAt = DateTime.UtcNow.AddDays(-18),
                    UpdatedAt = DateTime.UtcNow.AddDays(-1)
                },
                new MESKnowledgeItemDto
                {
                    Id = "KB005",
                    Title = "设备管理",
                    Category = "设备管理",
                    Content = "设备管理包括设备档案管理、设备状态监控、维护计划制定、故障处理等功能。通过设备管理可以提高设备利用率，减少停机时间。",
                    Keywords = new List<string> { "设备管理", "设备监控", "维护计划", "故障处理" },
                    CreatedAt = DateTime.UtcNow.AddDays(-15),
                    UpdatedAt = DateTime.UtcNow
                }
            };
        }

        /// <summary>
        /// 初始化常见问题
        /// </summary>
        /// <returns>常见问题列表</returns>
        private List<MESFAQDto> InitializeFAQs()
        {
            return new List<MESFAQDto>
            {
                new MESFAQDto
                {
                    Id = "FAQ001",
                    Question = "如何创建新的生产订单？",
                    Answer = "创建生产订单的步骤：1. 进入生产管理模块；2. 点击\"新建订单\"按钮；3. 填写订单基本信息（产品、数量、交期等）；4. 选择生产工艺路线；5. 确认信息后提交。",
                    Category = "生产管理",
                    Order = 1,
                    CreatedAt = DateTime.UtcNow.AddDays(-10)
                },
                new MESFAQDto
                {
                    Id = "FAQ002",
                    Question = "库存不足时如何处理？",
                    Answer = "库存不足处理方法：1. 检查安全库存设置是否合理；2. 查看是否有在途采购订单；3. 联系采购部门紧急采购；4. 考虑使用替代物料；5. 调整生产计划优先级。",
                    Category = "库存管理",
                    Order = 2,
                    CreatedAt = DateTime.UtcNow.AddDays(-8)
                },
                new MESFAQDto
                {
                    Id = "FAQ003",
                    Question = "如何查看产品的BOM结构？",
                    Answer = "查看BOM结构的方法：1. 进入工艺管理模块；2. 选择\"BOM管理\"功能；3. 输入产品编码或名称进行搜索；4. 点击查看详情，可以看到树形结构的BOM；5. 支持展开/收起各级物料。",
                    Category = "工艺管理",
                    Order = 3,
                    CreatedAt = DateTime.UtcNow.AddDays(-6)
                },
                new MESFAQDto
                {
                    Id = "FAQ004",
                    Question = "设备故障如何快速处理？",
                    Answer = "设备故障处理流程：1. 立即停机确保安全；2. 在系统中报告故障；3. 通知维修人员；4. 记录故障现象和可能原因；5. 维修完成后进行设备验证；6. 更新设备状态。",
                    Category = "设备管理",
                    Order = 4,
                    CreatedAt = DateTime.UtcNow.AddDays(-4)
                },
                new MESFAQDto
                {
                    Id = "FAQ005",
                    Question = "如何生成生产报表？",
                    Answer = "生成生产报表步骤：1. 进入报表管理模块；2. 选择报表类型（日报、周报、月报等）；3. 设置查询条件（时间范围、产品、车间等）；4. 点击生成报表；5. 可以导出为Excel或PDF格式。",
                    Category = "报表管理",
                    Order = 5,
                    CreatedAt = DateTime.UtcNow.AddDays(-2)
                }
            };
        }

        /// <summary>
        /// 初始化操作指南
        /// </summary>
        /// <returns>操作指南字典</returns>
        private Dictionary<string, MESOperationGuideDto> InitializeOperationGuides()
        {
            return new Dictionary<string, MESOperationGuideDto>
            {
                ["create_production_plan"] = new MESOperationGuideDto
                {
                    Operation = "create_production_plan",
                    Title = "创建生产计划操作指南",
                    Description = "详细介绍如何在MES系统中创建新的生产计划",
                    Steps = new List<MESOperationStepDto>
                    {
                        new MESOperationStepDto { Step = 1, Description = "登录MES系统，进入生产管理模块", Action = "点击主菜单中的\"生产管理\"" },
                        new MESOperationStepDto { Step = 2, Description = "选择生产计划功能", Action = "在子菜单中选择\"生产计划管理\"" },
                        new MESOperationStepDto { Step = 3, Description = "点击新建按钮", Action = "点击页面右上角的\"新建计划\"按钮" },
                        new MESOperationStepDto { Step = 4, Description = "填写计划基本信息", Action = "输入计划名称、产品信息、数量、开始时间、结束时间等" },
                        new MESOperationStepDto { Step = 5, Description = "设置计划优先级", Action = "根据实际情况选择优先级（高、中、低）" },
                        new MESOperationStepDto { Step = 6, Description = "保存并提交", Action = "检查信息无误后点击\"保存\"按钮" }
                    },
                    Prerequisites = new List<string> { "拥有生产计划管理权限", "产品信息已录入系统", "了解生产工艺流程" },
                    Notes = new List<string> { "计划时间不能冲突", "确保物料库存充足", "考虑设备产能限制" },
                    CreatedAt = DateTime.UtcNow.AddDays(-15)
                },
                ["query_inventory"] = new MESOperationGuideDto
                {
                    Operation = "query_inventory",
                    Title = "库存查询操作指南",
                    Description = "如何查询物料库存信息",
                    Steps = new List<MESOperationStepDto>
                    {
                        new MESOperationStepDto { Step = 1, Description = "进入库存管理模块", Action = "点击主菜单中的\"库存管理\"" },
                        new MESOperationStepDto { Step = 2, Description = "选择库存查询功能", Action = "在子菜单中选择\"库存查询\"" },
                        new MESOperationStepDto { Step = 3, Description = "设置查询条件", Action = "可按物料编码、名称、类别等条件查询" },
                        new MESOperationStepDto { Step = 4, Description = "执行查询", Action = "点击\"查询\"按钮获取结果" },
                        new MESOperationStepDto { Step = 5, Description = "查看结果", Action = "查看库存量、安全库存、可用库存等信息" }
                    },
                    Prerequisites = new List<string> { "拥有库存查询权限", "物料编码已标准化" },
                    Notes = new List<string> { "库存数据实时更新", "注意安全库存预警", "可导出查询结果" },
                    CreatedAt = DateTime.UtcNow.AddDays(-10)
                }
            };
        }

        /// <summary>
        /// 初始化术语定义
        /// </summary>
        /// <returns>术语定义字典</returns>
        private Dictionary<string, MESTermDefinitionDto> InitializeTermDefinitions()
        {
            return new Dictionary<string, MESTermDefinitionDto>
            {
                ["mes"] = new MESTermDefinitionDto
                {
                    Term = "MES",
                    FullName = "Manufacturing Execution System",
                    ChineseName = "制造执行系统",
                    Definition = "MES是位于上层计划管理系统与底层工业控制之间的面向车间层的管理信息系统，为操作人员/管理人员提供计划的执行、跟踪以及所有资源的当前状态。",
                    Category = "系统概念",
                    Examples = new List<string> { "生产订单管理", "工艺路线管理", "质量管理", "设备管理" },
                    RelatedTerms = new List<string> { "ERP", "WMS", "SCM" },
                    CreatedAt = DateTime.UtcNow.AddDays(-30)
                },
                ["bom"] = new MESTermDefinitionDto
                {
                    Term = "BOM",
                    FullName = "Bill of Materials",
                    ChineseName = "物料清单",
                    Definition = "BOM是描述产品结构的技术文件，它列出了制造某个产品所需的所有原材料、零部件、子装配件等的清单，包括每种物料的用量、规格等信息。",
                    Category = "工艺管理",
                    Examples = new List<string> { "设计BOM", "制造BOM", "工程BOM" },
                    RelatedTerms = new List<string> { "工艺路线", "物料需求", "成本核算" },
                    CreatedAt = DateTime.UtcNow.AddDays(-25)
                },
                ["wip"] = new MESTermDefinitionDto
                {
                    Term = "WIP",
                    FullName = "Work In Process",
                    ChineseName = "在制品",
                    Definition = "WIP指在生产过程中尚未完成的产品，包括已投入生产但还没有完成的半成品、正在加工的产品等。",
                    Category = "生产管理",
                    Examples = new List<string> { "半成品", "在制件", "工序间产品" },
                    RelatedTerms = new List<string> { "产成品", "原材料", "生产周期" },
                    CreatedAt = DateTime.UtcNow.AddDays(-20)
                }
            };
        }

        /// <summary>
        /// 初始化帮助信息
        /// </summary>
        /// <returns>帮助信息字典</returns>
        private Dictionary<string, MESHelpInfoDto> InitializeHelpInfos()
        {
            return new Dictionary<string, MESHelpInfoDto>
            {
                ["production"] = new MESHelpInfoDto
                {
                    ModuleName = "生产管理",
                    Description = "生产管理模块是MES系统的核心模块，负责生产计划的制定、执行、监控和优化。",
                    MainFeatures = new List<string>
                    {
                        "生产计划制定和调整",
                        "生产订单管理和跟踪",
                        "工序进度监控",
                        "生产异常处理",
                        "产能分析和优化"
                    },
                    CommonOperations = new List<string>
                    {
                        "创建生产计划",
                        "下达生产订单",
                        "更新生产进度",
                        "处理生产异常",
                        "查询生产报表"
                    },
                    TroubleShooting = new List<MESHelpTipDto>
                    {
                        new MESHelpTipDto { Problem = "无法创建生产计划", Solution = "检查权限设置和产品BOM是否完整" },
                        new MESHelpTipDto { Problem = "生产进度无法更新", Solution = "确认工序状态和操作员权限" },
                        new MESHelpTipDto { Problem = "报表数据不准确", Solution = "检查数据采集点和时间范围设置" }
                    },
                    ContactInfo = "如需进一步帮助，请联系系统管理员或拨打技术支持热线：400-123-4567",
                    CreatedAt = DateTime.UtcNow.AddDays(-15)
                },
                ["inventory"] = new MESHelpInfoDto
                {
                    ModuleName = "库存管理",
                    Description = "库存管理模块提供全面的物料库存管理功能，包括入库、出库、盘点、调拨等操作。",
                    MainFeatures = new List<string>
                    {
                        "实时库存查询",
                        "库存预警管理",
                        "出入库管理",
                        "库存盘点",
                        "物料追溯"
                    },
                    CommonOperations = new List<string>
                    {
                        "查询物料库存",
                        "执行出入库操作",
                        "设置安全库存",
                        "处理库存预警",
                        "执行库存盘点"
                    },
                    TroubleShooting = new List<MESHelpTipDto>
                    {
                        new MESHelpTipDto { Problem = "库存数据不准", Solution = "执行库存盘点并调整差异" },
                        new MESHelpTipDto { Problem = "出库失败", Solution = "检查库存数量和物料状态" },
                        new MESHelpTipDto { Problem = "库存预警不及时", Solution = "检查安全库存设置和预警规则" }
                    },
                    ContactInfo = "库存管理相关问题请联系仓储部门：ext-2001",
                    CreatedAt = DateTime.UtcNow.AddDays(-12)
                }
            };
        }

        #endregion
    }

    #region 数据传输对象

    /// <summary>
    /// MES知识条目DTO
    /// </summary>
    public class MESKnowledgeItemDto
    {
        /// <summary>
        /// 知识条目ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 分类
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 关键词
        /// </summary>
        public List<string> Keywords { get; set; } = new List<string>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// MES常见问题DTO
    /// </summary>
    public class MESFAQDto
    {
        /// <summary>
        /// 问题ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 问题
        /// </summary>
        public string Question { get; set; } = string.Empty;

        /// <summary>
        /// 答案
        /// </summary>
        public string Answer { get; set; } = string.Empty;

        /// <summary>
        /// 分类
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 排序号
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// MES操作指南DTO
    /// </summary>
    public class MESOperationGuideDto
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 操作步骤
        /// </summary>
        public List<MESOperationStepDto> Steps { get; set; } = new List<MESOperationStepDto>();

        /// <summary>
        /// 前置条件
        /// </summary>
        public List<string> Prerequisites { get; set; } = new List<string>();

        /// <summary>
        /// 注意事项
        /// </summary>
        public List<string> Notes { get; set; } = new List<string>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// MES操作步骤DTO
    /// </summary>
    public class MESOperationStepDto
    {
        /// <summary>
        /// 步骤号
        /// </summary>
        public int Step { get; set; }

        /// <summary>
        /// 步骤描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 具体操作
        /// </summary>
        public string Action { get; set; } = string.Empty;
    }

    /// <summary>
    /// MES术语定义DTO
    /// </summary>
    public class MESTermDefinitionDto
    {
        /// <summary>
        /// 术语
        /// </summary>
        public string Term { get; set; } = string.Empty;

        /// <summary>
        /// 全称
        /// </summary>
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// 中文名称
        /// </summary>
        public string ChineseName { get; set; } = string.Empty;

        /// <summary>
        /// 定义
        /// </summary>
        public string Definition { get; set; } = string.Empty;

        /// <summary>
        /// 分类
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 示例
        /// </summary>
        public List<string> Examples { get; set; } = new List<string>();

        /// <summary>
        /// 相关术语
        /// </summary>
        public List<string> RelatedTerms { get; set; } = new List<string>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// MES系统帮助信息DTO
    /// </summary>
    public class MESHelpInfoDto
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName { get; set; } = string.Empty;

        /// <summary>
        /// 模块描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 主要功能
        /// </summary>
        public List<string> MainFeatures { get; set; } = new List<string>();

        /// <summary>
        /// 常用操作
        /// </summary>
        public List<string> CommonOperations { get; set; } = new List<string>();

        /// <summary>
        /// 故障排除
        /// </summary>
        public List<MESHelpTipDto> TroubleShooting { get; set; } = new List<MESHelpTipDto>();

        /// <summary>
        /// 联系信息
        /// </summary>
        public string ContactInfo { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// MES帮助提示DTO
    /// </summary>
    public class MESHelpTipDto
    {
        /// <summary>
        /// 问题描述
        /// </summary>
        public string Problem { get; set; } = string.Empty;

        /// <summary>
        /// 解决方案
        /// </summary>
        public string Solution { get; set; } = string.Empty;
    }

    #endregion
}