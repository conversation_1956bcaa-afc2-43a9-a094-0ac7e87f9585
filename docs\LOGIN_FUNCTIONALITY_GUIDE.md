# 🔐 登录功能实现指南

## 📋 概述

已在服务层添加了完整的用户登录功能，为后续实现JWT双token认证做好准备。

## 🏗️ 实现架构

### 服务层 (AuthService.Application)

#### **IUserService 接口扩展**
```csharp
/// <summary>
/// 用户登录
/// </summary>
Task<LoginResult> LoginAsync(LoginRequest loginRequest, CancellationToken cancellationToken = default);
```

#### **UserService 实现**
- ✅ 支持用户名或邮箱登录
- ✅ 密码验证和安全检查
- ✅ 账户状态验证（激活、锁定、删除）
- ✅ 失败次数控制和自动锁定
- ✅ 登录信息记录（IP、时间等）
- ✅ 详细的日志记录

### 控制器层 (AuthService.Api)

#### **AuthController**
```csharp
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册（预留）
POST /api/auth/refresh        # 刷新令牌（预留）
POST /api/auth/logout         # 用户登出（预留）
```

## 🔧 核心功能

### 1. 登录验证流程

```mermaid
graph TD
    A[接收登录请求] --> B[验证输入参数]
    B --> C[判断用户名/邮箱]
    C --> D[查找用户]
    D --> E{用户存在?}
    E -->|否| F[返回错误]
    E -->|是| G[检查用户状态]
    G --> H{账户正常?}
    H -->|否| I[返回状态错误]
    H -->|是| J[验证密码]
    J --> K{密码正确?}
    K -->|否| L[增加失败次数]
    L --> M{超过限制?}
    M -->|是| N[锁定账户]
    M -->|否| O[返回密码错误]
    K -->|是| P[更新登录信息]
    P --> Q[返回登录成功]
```

### 2. 安全特性

#### **账户保护**
- 🔒 **失败次数限制**：5次失败后自动锁定30分钟
- 🔒 **状态检查**：验证账户激活、删除、锁定状态
- 🔒 **IP记录**：记录登录IP地址和用户代理
- 🔒 **时间记录**：记录最后登录时间

#### **输入验证**
- ✅ 用户名/邮箱格式验证
- ✅ 密码非空验证
- ✅ 自动识别邮箱格式

#### **错误处理**
- 🛡️ 统一的错误响应格式
- 🛡️ 不泄露敏感信息的错误消息
- 🛡️ 详细的服务端日志记录

### 3. 数据模型

#### **LoginRequest**
```csharp
public class LoginRequest
{
    public string UsernameOrEmail { get; set; }    // 用户名或邮箱
    public string Password { get; set; }           // 密码
    public bool RememberMe { get; set; }           // 记住登录
    public string? ClientIp { get; set; }          // 客户端IP
    public string? UserAgent { get; set; }         // 用户代理
    public string? TenantId { get; set; }          // 租户ID
}
```

#### **LoginResult**
```csharp
public class LoginResult
{
    public bool Success { get; set; }              // 是否成功
    public string? ErrorMessage { get; set; }      // 错误消息
    public string? ErrorCode { get; set; }         // 错误代码
    public User? User { get; set; }                // 用户信息
    public string? AccessToken { get; set; }       // 访问令牌（预留）
    public string? RefreshToken { get; set; }      // 刷新令牌（预留）
    public DateTime? ExpiresAt { get; set; }       // 过期时间
    public UserPermissionInfo? Permissions { get; set; }  // 权限信息
}
```

## 🚀 API 使用示例

### 登录请求
```bash
POST /api/auth/login
Content-Type: application/json

{
  "usernameOrEmail": "<EMAIL>",
  "password": "your_password",
  "rememberMe": true,
  "tenantId": "tenant_001"
}
```

### 成功响应
```json
{
  "success": true,
  "message": "登录成功",
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "username": "admin",
    "email": "<EMAIL>",
    "displayName": "管理员",
    "isEmailVerified": true,
    "lastLoginAt": "2024-12-15T10:30:00Z",
    "tenantId": "tenant_001"
  },
  "permissions": {
    "roles": ["Admin", "User"],
    "permissions": ["user.read", "user.write"],
    "tenantId": "tenant_001",
    "isSuperAdmin": true
  },
  "loginTime": "2024-12-15T10:30:00Z",
  "sessionId": "sess_123456789",
  "accessToken": null,
  "refreshToken": null,
  "expiresAt": null
}
```

### 错误响应
```json
{
  "errorCode": "INVALID_PASSWORD",
  "errorMessage": "用户名或密码错误"
}
```

## 🔑 错误代码说明

| 错误代码 | HTTP状态码 | 说明 |
|---------|-----------|------|
| `INVALID_INPUT` | 400 | 输入参数错误 |
| `USER_NOT_FOUND` | 401 | 用户不存在 |
| `INVALID_PASSWORD` | 401 | 密码错误 |
| `USER_DISABLED` | 401 | 账户已禁用 |
| `USER_DELETED` | 401 | 账户已删除 |
| `ACCOUNT_LOCKED` | 423 | 账户已锁定 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |

## 🧪 测试用例

### 1. 正常登录测试
```bash
# 使用用户名登录
curl -X POST "http://localhost:5143/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "usernameOrEmail": "admin",
    "password": "Admin123!",
    "rememberMe": true
  }'

# 使用邮箱登录
curl -X POST "http://localhost:5143/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "usernameOrEmail": "<EMAIL>",
    "password": "Admin123!",
    "rememberMe": false
  }'
```

### 2. 错误场景测试
```bash
# 密码错误
curl -X POST "http://localhost:5143/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "usernameOrEmail": "admin",
    "password": "wrong_password"
  }'

# 用户不存在
curl -X POST "http://localhost:5143/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "usernameOrEmail": "nonexistent",
    "password": "any_password"
  }'
```

## 🔮 后续扩展计划

### 1. JWT Token 实现
- ✅ 预留了 `AccessToken` 字段
- ✅ 预留了 `RefreshToken` 字段
- ✅ 预留了 `ExpiresAt` 字段

### 2. 双Token认证
- 🔄 Access Token（短期，15-30分钟）
- 🔄 Refresh Token（长期，7-30天）
- 🔄 Token刷新机制

### 3. 会话管理
- 🔄 会话存储（Redis/内存）
- 🔄 会话过期管理
- 🔄 多设备登录控制

### 4. 安全增强
- 🔄 验证码功能
- 🔄 双因素认证（2FA）
- 🔄 设备指纹识别
- 🔄 异常登录检测

## 📊 动态API集成

登录功能已自动集成到动态API系统中：

```bash
# 动态生成的登录相关端点
POST /api/dynamic/users/validateuser     # 验证用户（基础版本）
POST /api/dynamic/users/loginasync       # 登录方法（自动生成）
```

## 🛠️ 配置说明

### 安全配置
```json
{
  "Security": {
    "MaxFailedLoginAttempts": 5,
    "LockoutDurationMinutes": 30,
    "PasswordPolicy": {
      "MinLength": 8,
      "RequireUppercase": true,
      "RequireLowercase": true,
      "RequireDigit": true,
      "RequireSpecialChar": true
    }
  }
}
```

### 日志配置
```json
{
  "Logging": {
    "LogLevel": {
      "AuthService.Application.Services.UserService": "Information",
      "AuthService.Api.Controllers.AuthController": "Information"
    }
  }
}
```

## 🎯 最佳实践

### 1. 客户端集成
- 使用HTTPS进行所有认证请求
- 妥善存储会话信息
- 实现自动登出机制
- 处理各种错误场景

### 2. 安全建议
- 定期更新密码策略
- 监控异常登录行为
- 实施IP白名单（如需要）
- 定期审计登录日志

### 3. 性能优化
- 使用连接池优化数据库访问
- 考虑缓存用户信息
- 实施请求限流
- 监控响应时间

通过这个登录功能实现，您已经有了一个完整、安全的用户认证基础，可以在此基础上轻松扩展JWT双token认证系统。
