using System;

namespace SqlsugarService.Application.AI.KouZi_AI.Dtos
{
    /// <summary>
    /// 扣子空间流式响应数据DTO - 实时流式事件的数据载体
    /// 
    /// 此DTO封装了扣子AI流式响应中的单个事件数据：
    /// 
    /// 流式响应概念：
    /// - Server-Sent Events (SSE)：基于SSE协议的实时数据推送
    /// - 增量传输：AI生成内容时实时推送给客户端
    /// - 用户体验：用户可以看到AI"思考"和"打字"的过程
    /// - 网络效率：边生成边传输，减少总体延迟感知
    /// 
    /// 事件驱动模式：
    /// - 事件类型：不同的事件类型表示不同的处理阶段
    /// - 状态机：事件序列构成AI处理的完整状态机
    /// - 可恢复性：客户端可以基于事件重建完整的响应
    /// - 实时性：事件按发生顺序实时推送
    /// 
    /// 数据结构设计：
    /// - 轻量级：单个事件数据量小，适合高频传输
    /// - 自包含：每个事件包含必要的上下文信息
    /// - 可聚合：多个事件可以聚合成完整的响应
    /// - 容错性：单个事件丢失不影响整体功能
    /// 
    /// 使用场景：
    /// - 实时聊天：提供类似真人对话的体验
    /// - 长文本生成：实时显示AI生成的长篇内容
    /// - 进度提示：显示AI处理的实时进度
    /// - 状态监控：监控AI处理的各个阶段
    /// 
    /// 技术特性：
    /// - 时间排序：通过Timestamp确保事件的正确顺序
    /// - 类型安全：使用强类型确保数据一致性
    /// - JSON友好：支持高效的JSON序列化/反序列化
    /// - 内存友好：单个事件占用内存小
    /// 
    /// 性能考虑：
    /// - 高频传输：适合高频率的事件推送
    /// - 低延迟：最小化事件处理的延迟
    /// - 带宽优化：事件数据结构紧凑
    /// - 缓存友好：支持客户端事件缓存和重放
    /// </summary>
    public class KouZiAIStreamResponseDto
    {
        /// <summary>
        /// 流式事件类型标识符 - 事件分类的关键字段
        /// 
        /// 主要事件类型详解：
        /// 
        /// 1. "conversation.message.delta" - 增量消息内容
        ///    - 用途：AI生成内容的实时片段
        ///    - 频率：高频率，每个文本片段一个事件
        ///    - 内容：Content字段包含增量文本内容
        ///    - 特点：需要客户端进行内容合并
        ///    - 示例：AI生成"人工智能是一门科学"，可能分为"人工"、"智能"、"是一门"、"科学"等多个delta事件
        /// 
        /// 2. "conversation.message.completed" - 消息完成事件
        ///    - 用途：标志AI回复内容生成完成
        ///    - 频率：每个完整回复一个事件
        ///    - 内容：Content字段包含完整的回复内容
        ///    - 特点：客户端可以进行最终的内容验证和格式化
        ///    - 处理：通常标志一轮对话的结束
        /// 
        /// 3. "conversation.chat.created" - 会话创建事件
        ///    - 用途：新对话会话创建的通知
        ///    - 频率：每个新会话一个事件
        ///    - 内容：包含会话ID等元数据信息
        ///    - 特点：为后续对话提供会话上下文
        ///    - 重要性：维护对话连续性的关键事件
        /// 
        /// 4. "conversation.chat.completed" - 会话完成事件
        ///    - 用途：整个对话会话处理完成
        ///    - 频率：每个会话完成一个事件
        ///    - 内容：会话的最终状态和统计信息
        ///    - 特点：可以包含token使用量等统计数据
        ///    - 应用：触发会话后处理和资源清理
        /// 
        /// 5. "error" - 错误事件
        ///    - 用途：处理过程中发生的错误
        ///    - 频率：按错误发生频率
        ///    - 内容：错误描述和可能的解决建议
        ///    - 特点：需要客户端进行错误处理
        ///    - 重要性：保证用户体验的关键事件
        /// 
        /// 6. "done" - 流结束事件
        ///    - 用途：标志流式响应的彻底结束
        ///    - 频率：每个流式响应一个事件
        ///    - 内容：通常包含总结信息
        ///    - 特点：客户端可以清理流式处理资源
        ///    - 应用：触发最终的UI更新和状态清理
        /// 
        /// 事件处理策略：
        /// - 顺序处理：严格按照Timestamp顺序处理事件
        /// - 类型路由：根据Event类型路由到不同的处理逻辑
        /// - 错误恢复：error事件的优雅处理和恢复
        /// - 状态维护：维护客户端的实时状态
        /// 
        /// 扩展性考虑：
        /// - 新事件类型：预留支持未来新增的事件类型
        /// - 版本兼容：保持与旧版本客户端的兼容性
        /// - 自定义事件：支持特定业务场景的自定义事件
        /// - 标准化：遵循业界标准的事件命名规范
        /// </summary>
        public string Event { get; set; }
        
        /// <summary>
        /// 事件携带的内容数据 - 事件的核心载荷
        /// 
        /// 内容特征与事件类型的关系：
        /// 
        /// Delta事件内容：
        /// - 片段性：包含AI生成内容的一个小片段
        /// - 连续性：多个delta事件的内容需要连续拼接
        /// - 实时性：内容生成后立即推送
        /// - 可变长：片段长度根据AI生成速度而变化
        /// - 示例："人工"、"智能"、"技术"等文本片段
        /// 
        /// Completed事件内容：
        /// - 完整性：包含AI回复的完整文本内容
        /// - 最终性：这是AI生成的最终版本内容
        /// - 验证性：客户端可以用来验证拼接结果的正确性
        /// - 格式化：可能包含最终的格式化信息
        /// - 示例：完整的段落、文章或回答
        /// 
        /// Error事件内容：
        /// - 描述性：详细描述发生的错误情况
        /// - 用户友好：使用易于理解的错误描述
        /// - 解决导向：尽可能提供解决问题的建议
        /// - 分级处理：不同严重程度的错误有不同描述
        /// - 示例："网络连接中断，请稍后重试"
        /// 
        /// Done事件内容：
        /// - 总结性：可能包含整个流程的总结信息
        /// - 统计性：可能包含处理统计数据
        /// - 确认性：确认流式响应的完全结束
        /// - 清理性：提示可以进行资源清理
        /// - 示例："[DONE]"或统计信息JSON
        /// 
        /// 内容处理最佳实践：
        /// - 编码安全：确保内容的UTF-8编码正确性
        /// - XSS防护：对内容进行必要的安全过滤
        /// - 长度控制：对异常长的内容进行适当处理
        /// - 格式保持：保持原始内容的格式信息
        /// - 压缩优化：对大内容考虑压缩传输
        /// 
        /// 客户端处理策略：
        /// - 增量显示：delta内容的实时增量显示
        /// - 缓冲管理：合理管理内容缓冲区大小
        /// - 渲染优化：避免频繁的DOM操作影响性能
        /// - 内存管理：及时清理不需要的历史内容
        /// - 用户体验：提供流畅的内容显示效果
        /// </summary>
        public string Content { get; set; }
        
        /// <summary>
        /// 事件完成状态标志 - 流式处理的关键状态指示器
        /// 
        /// 完成状态的业务含义：
        /// 
        /// IsCompleted = false（进行中状态）：
        /// - Delta事件：表示这是增量内容，还有后续内容
        /// - 处理状态：AI仍在生成内容，客户端应继续等待
        /// - UI表现：可以显示"正在输入..."或类似的加载状态
        /// - 资源管理：保持连接和资源，准备接收后续事件
        /// - 用户期望：告知用户内容尚未完成，需要耐心等待
        /// 
        /// IsCompleted = true（完成状态）：
        /// - Completed事件：表示内容生成完成，这是最终结果
        /// - Error事件：表示错误处理完成，不会有后续相关事件
        /// - Done事件：表示整个流式响应彻底结束
        /// - 资源清理：客户端可以清理相关的临时资源
        /// - UI更新：可以隐藏加载状态，显示最终结果
        /// 
        /// 状态机设计：
        /// - 状态转换：从false到true的单向转换
        /// - 一致性：同一事件序列中的状态应该保持逻辑一致
        /// - 可预测性：客户端可以根据此状态预测后续行为
        /// - 容错性：即使状态不准确，也应该有容错处理
        /// 
        /// 客户端处理逻辑：
        /// - 条件渲染：根据完成状态决定UI渲染方式
        /// - 事件监听：完成状态变化时触发相应的事件处理
        /// - 资源管理：完成时的资源清理和释放
        /// - 用户反馈：向用户提供明确的状态反馈
        /// - 性能优化：避免在未完成状态下的重复处理
        /// 
        /// 技术实现细节：
        /// - 布尔简单性：使用布尔值确保状态的明确性
        /// - 默认值：合理的默认值设置（通常为false）
        /// - 序列化：JSON序列化时的正确处理
        /// - 线程安全：多线程环境下的状态访问安全
        /// 
        /// 监控和调试：
        /// - 状态追踪：记录状态变化用于问题诊断
        /// - 异常检测：检测异常的状态转换模式
        /// - 性能分析：分析状态转换的时间分布
        /// - 用户体验：优化基于状态的用户体验
        /// </summary>
        public bool IsCompleted { get; set; }
        
        /// <summary>
        /// 对话上下文标识符 - 流式事件的会话归属
        /// 
        /// 在流式响应中的重要作用：
        /// 
        /// 上下文关联：
        /// - 事件归属：将单个流式事件关联到特定的对话会话
        /// - 并发处理：支持多个并发会话的事件正确分发
        /// - 状态维护：维护特定会话的处理状态和上下文
        /// - 数据完整性：确保事件数据的完整性和一致性
        /// 
        /// 会话管理：
        /// - 事件路由：将事件路由到正确的会话处理器
        /// - 状态同步：保持客户端和服务端的会话状态同步
        /// - 资源隔离：不同会话的资源和状态相互隔离
        /// - 错误隔离：单个会话的错误不影响其他会话
        /// 
        /// 客户端应用：
        /// - 事件分发：客户端根据ConversationId分发事件到对应的UI组件
        /// - 状态管理：维护每个会话的独立状态
        /// - 用户界面：在多标签或多窗口环境中正确显示事件
        /// - 缓存策略：基于会话ID的事件缓存和管理
        /// 
        /// 数据一致性：
        /// - 标识符验证：验证事件是否属于预期的会话
        /// - 顺序保证：确保同一会话的事件按正确顺序处理
        /// - 完整性检查：检查会话事件序列的完整性
        /// - 容错恢复：处理会话ID异常或丢失的情况
        /// 
        /// 技术考虑：
        /// - 可空设计：允许某些事件类型不包含会话ID
        /// - 格式验证：验证会话ID的格式正确性
        /// - 生命周期：会话ID在事件流中的生命周期管理
        /// - 安全性：防止会话ID的恶意伪造或劫持
        /// 
        /// 性能优化：
        /// - 索引友好：会话ID适合用作索引键值
        /// - 内存效率：紧凑的ID格式减少内存占用
        /// - 查找速度：支持快速的会话查找和定位
        /// - 缓存键值：作为缓存系统的主键使用
        /// </summary>
        public string? ConversationId { get; set; }
        
        /// <summary>
        /// 聊天会话标识符 - 流式事件的会话细分标识
        /// 
        /// 与ConversationId的层次关系：
        /// 
        /// 标识符层次：
        /// - ConversationId：更高层次的对话会话标识
        /// - ChatId：具体的聊天交互标识，通常对应一次问答
        /// - 包含关系：一个Conversation可能包含多个Chat
        /// - 生命周期：ChatId的生命周期通常短于ConversationId
        /// 
        /// 业务语义：
        /// - 交互单元：ChatId代表一次完整的用户-AI交互
        /// - 请求响应：通常对应一次用户提问和AI回复的完整周期
        /// - 状态跟踪：跟踪单次交互的处理状态和结果
        /// - 性能监控：监控单次交互的处理时间和质量
        /// 
        /// 在流式响应中的作用：
        /// - 事件分组：将属于同一次交互的事件进行分组
        /// - 进度跟踪：跟踪单次交互的流式处理进度
        /// - 结果汇总：将分散的流式事件汇总为完整的交互结果
        /// - 错误关联：将错误事件关联到具体的交互请求
        /// 
        /// 客户端处理：
        /// - UI组织：在用户界面中按ChatId组织显示内容
        /// - 状态显示：显示特定交互的处理状态（如"AI正在思考..."）
        /// - 结果展示：将流式片段合并为完整的交互结果展示
        /// - 重试机制：基于ChatId实现交互的重试功能
        /// 
        /// 数据管理：
        /// - 事件聚合：将同一ChatId的事件聚合处理
        /// - 状态维护：维护每个ChatId的处理状态
        /// - 缓存组织：按ChatId组织事件缓存结构
        /// - 清理策略：基于ChatId的过期数据清理
        /// 
        /// 技术实现：
        /// - 可空性：某些全局事件可能不关联特定的ChatId
        /// - 唯一性：在一定范围内保证ChatId的唯一性
        /// - 格式一致：与ConversationId保持一致的格式规范
        /// - 关联验证：验证ChatId与ConversationId的关联关系
        /// 
        /// 应用场景：
        /// - 进度显示：显示特定问题的AI处理进度
        /// - 结果定位：快速定位特定交互的结果
        /// - 问题排查：基于ChatId的问题追踪和诊断
        /// - 质量评估：评估单次交互的质量和效果
        /// </summary>
        public string? ChatId { get; set; }
        
        /// <summary>
        /// 事件时间戳 - 流式事件的精确时间标记
        /// 
        /// 时间戳在流式处理中的关键作用：
        /// 
        /// 事件排序：
        /// - 时间顺序：确保事件按照发生的真实时间顺序处理
        /// - 排序基准：作为事件排序的主要基准
        /// - 一致性保证：保证分布式环境下的事件时间一致性
        /// - 重放支持：支持事件的准确重放和回溯
        /// 
        /// 性能分析：
        /// - 延迟测量：计算事件从生成到客户端接收的延迟
        /// - 吞吐量分析：分析单位时间内的事件处理能力
        /// - 响应速度：评估AI生成内容的实时性
        /// - 网络性能：分析网络传输的性能表现
        /// 
        /// 用户体验：
        /// - 实时感知：让用户感受到AI处理的实时性
        /// - 进度展示：基于时间展示处理进度
        /// - 超时处理：基于时间实现超时检测和处理
        /// - 响应预期：帮助用户建立合理的响应时间预期
        /// 
        /// 数据完整性：
        /// - 事件去重：基于时间戳和其他字段实现事件去重
        /// - 顺序验证：验证事件序列的时间逻辑正确性
        /// - 丢失检测：检测可能丢失的事件
        /// - 同步机制：支持多端数据同步和一致性检查
        /// 
        /// 技术规范：
        /// - UTC标准：统一使用UTC时间避免时区问题
        /// - 高精度：毫秒级精度支持高频事件的准确排序
        /// - ISO格式：JSON序列化时使用ISO 8601标准格式
        /// - 单调性：在单个流中时间戳应该单调递增
        /// 
        /// 默认值设计：
        /// - 自动设置：默认使用当前UTC时间自动设置
        /// - 创建时机：在事件对象创建时立即设置
        /// - 不可变性：一旦设置应该保持不变
        /// - 精确性：使用系统最高精度的时间源
        /// 
        /// 监控和调试：
        /// - 性能监控：监控事件处理的时间分布
        /// - 异常检测：检测异常的时间戳模式
        /// - 调试支持：为问题排查提供精确的时间信息
        /// - 报告生成：生成基于时间的统计报告
        /// 
        /// 客户端应用：
        /// - 排序显示：按时间顺序显示事件内容
        /// - 动画效果：基于时间间隔实现流畅的动画效果
        /// - 缓存策略：基于时间的缓存过期和清理
        /// - 用户反馈：显示事件的相对时间信息
        /// 
        /// 安全考虑：
        /// - 时间同步：确保时间戳的准确性和一致性
        /// - 防篡改：防止时间戳被恶意修改
        /// - 隐私保护：时间戳本身不包含敏感信息
        /// - 合规要求：满足相关法规对时间记录的要求
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}