<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="15.0.1" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.198" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SqlsugarService.Application\SqlsugarService.Application.csproj" />
    <ProjectReference Include="..\SqlsugarService.Domain\SqlsugarService.Domain.csproj" />
    <ProjectReference Include="..\SqlsugarService.Infrastructure\SqlsugarService.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\Upfile\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="web.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
