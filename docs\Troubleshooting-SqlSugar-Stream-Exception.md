# SqlSugar流读取异常故障排除指南

## 问题描述

遇到错误：`SqlSugar.SqlSugarException: Exception while reading from stream`

## 可能的原因

### 1. 数据库连接问题
- 网络连接不稳定
- 数据库服务器负载过高
- 连接字符串配置错误
- 数据库权限不足

### 2. 导航属性配置问题
- 复杂的导航属性查询导致内存溢出
- 循环引用导致的无限递归
- 导航属性配置不正确

### 3. SqlSugar版本兼容性
- 版本不兼容
- 配置方式过时
- 方法调用错误

### 4. 查询语句过于复杂
- 一次性加载过多数据
- 复杂的JOIN查询
- 内存不足

## 解决方案

### 1. 优化查询策略

#### 原始问题代码
```csharp
// 问题：一次性加载所有导航属性
var bomItems = await bomItemBase.AsQueryable()
    .Includes(bi => bi.Bom)
    .Includes(bi => bi.Material)
    .Includes(bi => bi.ChildItems)
    .ToListAsync();
```

#### 优化后的代码
```csharp
// 解决方案：分步查询，避免复杂的导航属性查询
// 步骤1：查询基本信息
var bomItems = await bomItemBase.AsQueryable()
    .Select(bi => new
    {
        bi.Id,
        bi.BomId,
        bi.MaterialId,
        bi.ParentItemId,
        bi.Quantity,
        bi.Unit,
        bi.InOutType
    })
    .ToListAsync();

// 步骤2：分别查询关联数据
var bomIds = bomItems.Select(bi => bi.BomId).Distinct().ToList();
var bomInfos = await bomItemBase.AsQueryable()
    .Includes(bi => bi.Bom)
    .Where(bi => bomIds.Contains(bi.BomId))
    .Select(bi => bi.Bom)
    .Distinct()
    .ToListAsync();

// 步骤3：手动组装数据
var bomItemsWithNav = bomItems.Select(bi => new BomItem
{
    Id = bi.Id,
    BomId = bi.BomId,
    MaterialId = bi.MaterialId,
    ParentItemId = bi.ParentItemId,
    Quantity = bi.Quantity,
    Unit = bi.Unit,
    InOutType = bi.InOutType,
    Bom = bomInfos.FirstOrDefault(b => b?.Id == bi.BomId),
    Material = materials.FirstOrDefault(m => m?.Id == bi.MaterialId)
}).ToList();
```

### 2. 添加连接测试

```csharp
/// <summary>
/// 测试数据库连接
/// </summary>
private async Task<ApiResult> TestDatabaseConnection()
{
    try
    {
        // 执行简单的查询测试连接
        var testResult = await bomItemBase.AsQueryable()
            .Select(bi => bi.Id)
            .Take(1)
            .ToListAsync();
        
        return ApiResult.Success(ResultCode.Success);
    }
    catch (Exception ex)
    {
        return ApiResult.Fail($"数据库连接测试失败：{ex.Message}", ResultCode.Error);
    }
}
```

### 3. 分页查询

```csharp
// 使用分页避免一次性加载过多数据
var pageSize = 100;
var pageIndex = 1;
var bomItems = await bomItemBase.AsQueryable()
    .Skip((pageIndex - 1) * pageSize)
    .Take(pageSize)
    .ToListAsync();
```

### 4. 简化查询

```csharp
// 只查询必要的字段
var simpleItems = await bomItemBase.AsQueryable()
    .Select(bi => new
    {
        bi.Id,
        bi.BomId,
        bi.MaterialId,
        bi.ParentItemId
    })
    .Take(10) // 限制数量
    .ToListAsync();
```

## 诊断步骤

### 1. 测试数据库连接

访问测试端点：
```
GET /api/ProductionPlans/test/simple-bom-items
```

### 2. 检查数据库配置

确认 `appsettings.json` 中的连接字符串：
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=*************;Port=5432;Database=sqlsugardata;Username=****;Password=****;SearchPath=public"
  }
}
```

### 3. 检查网络连接

```bash
# 测试数据库服务器连接
telnet ************* 5432

# 或者使用psql测试
psql -h ************* -p 5432 -U **** -d sqlsugardata
```

### 4. 检查数据库权限

```sql
-- 检查用户权限
SELECT * FROM information_schema.table_privileges 
WHERE grantee = '****';

-- 检查表是否存在
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name LIKE '%bom%';
```

## 预防措施

### 1. 配置连接池

```csharp
var connectionString = "Host=*************;Port=5432;Database=sqlsugardata;Username=****;Password=****;SearchPath=public;Pooling=true;MinPoolSize=5;MaxPoolSize=20;";
```

### 2. 设置超时时间

```csharp
var connectionString = "Host=*************;Port=5432;Database=sqlsugardata;Username=****;Password=****;SearchPath=public;CommandTimeout=30;";
```

### 3. 启用详细日志

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "SqlsugarService": "Debug",
      "SqlSugar": "Debug"
    }
  }
}
```

### 4. 使用健康检查

```csharp
// 在Program.cs中添加健康检查
builder.Services.AddHealthChecks()
    .AddCheck<DatabaseHealthCheck>("database");
```

## 常见错误及解决方案

### 错误1：连接超时
```
System.TimeoutException: The operation has timed out
```
**解决方案**：增加连接超时时间，检查网络连接

### 错误2：内存不足
```
System.OutOfMemoryException
```
**解决方案**：使用分页查询，限制查询数量

### 错误3：权限不足
```
System.Data.SqlClient.SqlException: Login failed for user
```
**解决方案**：检查数据库用户权限

### 错误4：表不存在
```
System.Data.SqlClient.SqlException: Invalid object name
```
**解决方案**：检查表名是否正确，确认数据库初始化

## 测试工具

### 1. 简单查询测试
```
GET /api/ProductionPlans/test/simple-bom-items
```

### 2. 数据库连接测试
```
GET /api/test/connection
```

### 3. 数据库状态检查
```
GET /api/database/status
```

## 性能优化建议

1. **使用索引**：为常用查询字段添加数据库索引
2. **分页查询**：避免一次性加载大量数据
3. **缓存策略**：对不经常变化的数据使用缓存
4. **连接池**：配置适当的连接池大小
5. **查询优化**：只查询必要的字段，避免SELECT *

## 联系支持

如果问题仍然存在，请提供以下信息：

1. 完整的错误堆栈跟踪
2. 数据库连接字符串（隐藏敏感信息）
3. SqlSugar版本信息
4. 数据库类型和版本
5. 应用程序日志
6. 网络环境信息 