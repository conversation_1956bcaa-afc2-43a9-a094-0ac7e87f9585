using SqlsugarService.Application.DTOs.PlanDto;
using System;
using System.Collections.Generic;

namespace SqlsugarService.Application.DTOs.ProductionOrderDto
{
    /// <summary>
    /// 包含物料信息的生产工单DTO
    /// </summary>
    public class GetProductionOrderWithMaterialsDto
    {
        /// <summary>
        /// 工单ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 工单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 工单名称
        /// </summary>
        public string OrderName { get; set; }

        /// <summary>
        /// 生产计划ID
        /// </summary>
        public Guid ProductionPlanId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        public string ProductNumber { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际数量
        /// </summary>
        public decimal ActualQuantity { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime PlanStartTime { get; set; }

        /// <summary>
        /// 计划结束时间
        /// </summary>
        public DateTime PlanEndTime { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdatedBy { get; set; }

        /// <summary>
        /// BOM ID
        /// </summary>
        public Guid BomId { get; set; }

        /// <summary>
        /// 生产计划名称
        /// </summary>
        public string PlanName { get; set; }

        /// <summary>
        /// 生产计划编号
        /// </summary>
        public string PlanNumber { get; set; }

        /// <summary>
        /// 物料信息列表
        /// </summary>
        public List<MaterialListItemDto> Materials { get; set; } = new List<MaterialListItemDto>();

        /// <summary>
        /// 物料总量
        /// </summary>
        public decimal TotalMaterialQuantity { get; set; }
    }

    /// <summary>
    /// 物料清单项目DTO
    /// </summary>
    public class MaterialListItemDto
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int Sequence { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string MaterialNumber { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 预计使用量
        /// </summary>
        public decimal EstimatedUsage { get; set; }

        /// <summary>
        /// 用料比例
        /// </summary>
        public string MaterialRatio { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        public Guid MaterialId { get; set; }

        /// <summary>
        /// 层级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 父级项目ID
        /// </summary>
        public string ParentItemId { get; set; }
    }
} 