# 增强版BOM分解测试脚本
# 用于测试使用GetBomTreeDropdownByBomId方法的分解功能

param(
    [Parameter(Mandatory=$true)]
    [string]$ProductionPlanId,
    
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== 增强版BOM分解测试 ===" -ForegroundColor Green
Write-Host "生产计划ID: $ProductionPlanId" -ForegroundColor Yellow
Write-Host "API地址: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

try {
    # 1. 获取生产计划信息
    Write-Host "1. 获取生产计划信息..." -ForegroundColor White
    $planResponse = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/$ProductionPlanId" -Method Get
    if ($planResponse.isSuc) {
        $plan = $planResponse.data
        Write-Host "   计划名称: $($plan.planName)" -ForegroundColor Gray
        Write-Host "   BOM ID: $($plan.bomId)" -ForegroundColor Gray
        Write-Host "   计划数量: $($plan.planQuantity)" -ForegroundColor Gray
        Write-Host "   当前状态: $($plan.status)" -ForegroundColor Gray
    } else {
        Write-Host "   获取生产计划失败: $($planResponse.msg)" -ForegroundColor Red
        exit 1
    }

    # 2. 获取BOM树形结构
    Write-Host "`n2. 获取BOM树形结构..." -ForegroundColor White
    $bomTreeResponse = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/bom-tree/$($plan.bomId)" -Method Get
    if ($bomTreeResponse.isSuc) {
        $bomTree = $bomTreeResponse.data
        Write-Host "   BOM树节点数: $($bomTree.Count)" -ForegroundColor Gray
        
        # 显示BOM树结构
        function Show-BomTree {
            param($nodes, $level = 0)
            foreach ($node in $nodes) {
                $indent = "  " * $level
                Write-Host "$indent- $($node.productName) ($($node.productNumber))" -ForegroundColor Gray
                if ($node.children -and $node.children.Count -gt 0) {
                    Show-BomTree -nodes $node.children -level ($level + 1)
                }
            }
        }
        Show-BomTree -nodes $bomTree
    } else {
        Write-Host "   获取BOM树失败: $($bomTreeResponse.msg)" -ForegroundColor Red
        exit 1
    }

    # 3. 预览分解结果
    Write-Host "`n3. 预览分解结果..." -ForegroundColor White
    $previewResponse = Invoke-RestMethod -Uri "$BaseUrl/api/BomDecomposition/preview/$ProductionPlanId" -Method Get
    if ($previewResponse.isSuc) {
        $preview = $previewResponse.data
        Write-Host "   预估工单数: $($preview.estimatedOrders)" -ForegroundColor Gray
        Write-Host "   可以分解: $($preview.canDecompose)" -ForegroundColor Gray
        Write-Host "   当前状态: $($preview.currentStatus)" -ForegroundColor Gray
        
        if ($preview.decompositionItems) {
            Write-Host "   分解项目:" -ForegroundColor Gray
            foreach ($item in $preview.decompositionItems) {
                Write-Host "     - $($item.materialName): $($item.requiredQuantity) $($item.unit)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "   预览失败: $($previewResponse.msg)" -ForegroundColor Red
        exit 1
    }

    # 4. 执行分解
    Write-Host "`n4. 执行分解..." -ForegroundColor White
    $decomposeResponse = Invoke-RestMethod -Uri "$BaseUrl/api/BomDecomposition/decompose/$ProductionPlanId" -Method Post
    if ($decomposeResponse.isSuc) {
        $result = $decomposeResponse.data
        Write-Host "   分解成功!" -ForegroundColor Green
        Write-Host "   生成工单数: $($result.totalOrders)" -ForegroundColor Gray
        Write-Host "   分解时间: $($result.decompositionTime)" -ForegroundColor Gray
        
        if ($result.orders) {
            Write-Host "   生成的工单:" -ForegroundColor Gray
            foreach ($order in $result.orders) {
                Write-Host "     - $($order.orderName): $($order.planQuantity) $($order.unit)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "   分解失败: $($decomposeResponse.msg)" -ForegroundColor Red
        exit 1
    }

    # 5. 验证分解结果
    Write-Host "`n5. 验证分解结果..." -ForegroundColor White
    $detailResponse = Invoke-RestMethod -Uri "$BaseUrl/api/BomDecomposition/detail/$ProductionPlanId" -Method Get
    if ($detailResponse.isSuc) {
        $detail = $detailResponse.data
        Write-Host "   生产计划状态: $($detail.statusDescription)" -ForegroundColor Gray
        Write-Host "   BOM树节点数: $($detail.bomTreeNodes)" -ForegroundColor Gray
        Write-Host "   分解项目数: $($detail.totalDecompositionItems)" -ForegroundColor Gray
        Write-Host "   产品项目数: $($detail.productCount)" -ForegroundColor Gray
        Write-Host "   物料项目数: $($detail.materialCount)" -ForegroundColor Gray
        Write-Host "   将生成工单数: $($detail.willGenerateOrders)" -ForegroundColor Gray
        Write-Host "   现有工单数: $($detail.existingOrderCount)" -ForegroundColor Gray
    } else {
        Write-Host "   获取详情失败: $($detailResponse.msg)" -ForegroundColor Red
    }

    Write-Host "`n=== 测试完成 ===" -ForegroundColor Green

} catch {
    Write-Host "测试过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误信息: $($_.Exception)" -ForegroundColor Red
} 