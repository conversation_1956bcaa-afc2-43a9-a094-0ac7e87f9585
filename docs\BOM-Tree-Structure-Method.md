# BOM树形结构方法说明

## 📋 **概述**

本文档说明了新实现的 `GetBomTreeStructure` 方法，该方法用于构建BOM的树形结构，关联BomInfo、ProductEntity、MaterialEntity表，并通过ParentItemId构建层级关系。

## 🎯 **功能特性**

### 1. **多表关联**
- 通过BomItem表的BomId关联BomInfo表
- 通过BomItem表的MaterialId关联ProductEntity表
- 通过BomItem表的MaterialId关联MaterialEntity表
- 通过BomItem表的ParentItemId构建父子关系

### 2. **智能显示**
- 优先显示产品名称，其次显示物料名称
- 优先使用产品编号，其次使用物料编号
- 优先使用产品规格，其次使用物料规格

### 3. **树形结构**
- 通过ParentItemId构建层级关系
- 自动设置层级和可展开性
- 自动生成序号

## 🔧 **实现细节**

### 1. **数据库查询**
```csharp
var bomItems = await _db.Queryable<BomItem>()
    .LeftJoin<ProductEntity>((bi, pe) => bi.MaterialId == pe.Id)
    .LeftJoin<MaterialEntity>((bi, pe, me) => bi.MaterialId == me.Id)
    .Where((bi, pe, me) => bi.BomId == bomId && !bi.IsDeleted)
    .Select((bi, pe, me) => new BomTreeStructureDto
    {
        // BOM项目信息
        Id = bi.Id,
        BomId = bi.BomId,
        MaterialId = bi.MaterialId,
        ParentItemId = bi.ParentItemId,
        Quantity = bi.Quantity,
        Unit = bi.Unit,
        
        // BOM信息
        BomNumber = bomInfo.BomNumber,
        BomVersion = bomInfo.Version,
        
        // 产品信息（来自ProductEntity）
        ProductName = pe.MaterialName,        // 注意：ProductEntity使用MaterialName字段
        ProductNumber = pe.MaterialNumber,    // 注意：ProductEntity使用MaterialNumber字段
        ProductSpecification = pe.SpecificationModel, // 注意：ProductEntity使用SpecificationModel字段
        ProductType = pe.MaterialType,
        
        // 物料信息（来自MaterialEntity）
        MaterialName = me.MaterialName,
        MaterialNumber = me.MaterialNumber,
        MaterialSpecification = me.SpecificationModel,
        MaterialType = me.MaterialType,
        
        // 计算字段
        UsageQuantity = bi.Quantity,
        UsageRatio = "20%",
        Children = new List<BomTreeStructureDto>()
    })
    .ToListAsync();
```

### 2. **字段映射说明**
**重要提示**：ProductEntity和MaterialEntity使用相同的字段命名规范：
- `MaterialName` - 名称字段
- `MaterialNumber` - 编号字段  
- `SpecificationModel` - 规格型号字段
- `MaterialType` - 类型字段

这种设计使得ProductEntity和MaterialEntity可以共享相同的字段结构，便于统一处理。

### 3. **树形结构构建**
```csharp
private List<BomTreeStructureDto> BuildBomTreeStructure(List<BomTreeStructureDto> bomItems)
{
    var result = new List<BomTreeStructureDto>();
    var itemDict = new Dictionary<string, BomTreeStructureDto>();

    // 第一步：创建所有节点并设置显示字段
    foreach (var item in bomItems)
    {
        // 设置显示名称（优先显示产品名称，其次显示物料名称）
        item.DisplayName = !string.IsNullOrEmpty(item.ProductName) ? item.ProductName : item.MaterialName;
        
        // 设置产品编号（优先使用产品编号，其次使用物料编号）
        item.DisplayProductNumber = !string.IsNullOrEmpty(item.ProductNumber) ? item.ProductNumber : item.MaterialNumber;
        
        // 设置规格型号（优先使用产品规格，其次使用物料规格）
        item.DisplaySpecification = !string.IsNullOrEmpty(item.ProductSpecification) ? item.ProductSpecification : item.MaterialSpecification;

        itemDict[item.Id.ToString()] = item;
    }

    // 第二步：建立父子关系
    foreach (var item in bomItems)
    {
        var node = itemDict[item.Id.ToString()];

        if (!string.IsNullOrEmpty(item.ParentItemId) && itemDict.ContainsKey(item.ParentItemId))
        {
            // 有父节点，添加到父节点的子节点列表中
            var parentNode = itemDict[item.ParentItemId];
            parentNode.Children.Add(node);
            parentNode.IsExpandable = true;
            node.Level = parentNode.Level + 1;
        }
        else
        {
            // 没有父节点，作为根节点
            result.Add(node);
            node.Level = 0;
        }
    }

    return result;
}
```

## 📊 **数据结构**

### 1. **BomTreeStructureDto字段说明**
```csharp
public class BomTreeStructureDto
{
    // 基础信息
    public Guid Id { get; set; }                    // BOM项目ID
    public Guid BomId { get; set; }                 // BOM ID
    public Guid MaterialId { get; set; }            // 物料ID
    public string ParentItemId { get; set; }        // 父级项目ID
    
    // BOM项目信息
    public decimal Quantity { get; set; }           // 用量
    public string Unit { get; set; }                // 单位
    public decimal LossRate { get; set; }           // 损耗率
    public MaterialRelationType InOutType { get; set; } // 投入产出类型
    public string Remark { get; set; }              // 备注
    
    // BOM信息
    public string BomNumber { get; set; }           // BOM编号
    public string BomVersion { get; set; }          // BOM版本
    
    // 产品信息（来自ProductEntity）
    public string ProductName { get; set; }         // 产品名称（实际来自MaterialName字段）
    public string ProductNumber { get; set; }       // 产品编号（实际来自MaterialNumber字段）
    public string ProductSpecification { get; set; } // 产品规格（实际来自SpecificationModel字段）
    public MaterialTypeEnum ProductType { get; set; } // 产品类型（实际来自MaterialType字段）
    
    // 物料信息（来自MaterialEntity）
    public string MaterialName { get; set; }        // 物料名称
    public string MaterialNumber { get; set; }      // 物料编号
    public string MaterialSpecification { get; set; } // 物料规格
    public MaterialTypeEnum? MaterialType { get; set; } // 物料类型
    
    // 显示字段（智能选择）
    public string DisplayName { get; set; }         // 显示名称
    public string DisplayProductNumber { get; set; } // 显示产品编号
    public string DisplaySpecification { get; set; } // 显示规格型号
    
    // 计算字段
    public decimal UsageQuantity { get; set; }      // 使用量
    public string UsageRatio { get; set; }          // 使用比例
    
    // 树形结构字段
    public int Sequence { get; set; }               // 序号
    public bool IsExpandable { get; set; }          // 是否可展开
    public int Level { get; set; }                  // 层级
    public List<BomTreeStructureDto> Children { get; set; } // 子节点列表
}
```

## 🚀 **使用示例**

### 1. **API调用**
```bash
# 获取BOM树形结构
GET /api/ProductionPlans/bom-structure/{bomId}
```

### 2. **测试脚本**
```powershell
# 运行BOM树形结构测试脚本（修复版）
.\scripts\test-bom-tree-structure-fixed.ps1 -BomId "your-bom-id"
```

### 3. **返回数据示例**
```json
{
  "isSuc": true,
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "guid-1",
      "bomId": "bom-guid",
      "materialId": "material-guid",
      "parentItemId": null,
      "quantity": 50,
      "unit": "个",
      "bomNumber": "BOM00001",
      "bomVersion": "1.0",
      "productName": "新产品0215",
      "productNumber": "WL025",
      "productSpecification": "蓝色",
      "displayName": "新产品0215",
      "displayProductNumber": "WL025",
      "displaySpecification": "蓝色",
      "usageQuantity": 50,
      "usageRatio": "20%",
      "sequence": 1,
      "isExpandable": true,
      "level": 0,
      "children": [
        {
          "id": "guid-2",
          "parentItemId": "guid-1",
          "displayName": "产品二",
          "displayProductNumber": "WL025",
          "displaySpecification": "蓝色",
          "usageQuantity": 50,
          "usageRatio": "20%",
          "sequence": 2,
          "isExpandable": false,
          "level": 1,
          "children": []
        }
      ]
    }
  ]
}
```

## 🔍 **关键特性**

### 1. **智能字段选择**
- **显示名称**：优先显示产品名称，如果没有则显示物料名称
- **产品编号**：优先使用产品编号，如果没有则使用物料编号
- **规格型号**：优先使用产品规格，如果没有则使用物料规格

### 2. **层级关系**
- 通过ParentItemId建立父子关系
- 自动计算层级深度
- 自动设置可展开性

### 3. **序号生成**
- 按树形结构顺序生成序号
- 支持多层级序号

### 4. **数据完整性**
- 关联多个表获取完整信息
- 处理空值和默认值
- 支持软删除过滤

## 📈 **业务流程**

### 1. **数据查询流程**
```
BomId → 查询BomInfo → 查询BomItem → 关联ProductEntity → 关联MaterialEntity → 构建DTO
```

### 2. **树形构建流程**
```
BOM项目列表 → 创建节点字典 → 建立父子关系 → 设置层级 → 设置可展开性 → 生成序号
```

### 3. **显示字段设置流程**
```
产品信息 → 物料信息 → 智能选择显示字段 → 设置默认值
```

## 🎯 **应用场景**

### 1. **BOM结构展示**
- 在BOM管理界面显示树形结构
- 支持展开/折叠操作
- 显示完整的BOM信息

### 2. **生产计划分解**
- 为生产计划分解提供BOM结构
- 识别产品和物料节点
- 计算用量和比例

### 3. **物料需求分析**
- 分析BOM中的物料需求
- 计算各层级的用量
- 支持多层级物料清单

## 🔧 **配置要求**

### 1. **数据库表关系**
- BomItem表包含BomId、MaterialId、ParentItemId字段
- BomInfo表包含BomNumber、Version字段
- ProductEntity表包含MaterialName、MaterialNumber、SpecificationModel、MaterialType字段
- MaterialEntity表包含MaterialName、MaterialNumber、SpecificationModel、MaterialType字段

### 2. **依赖注入**
确保以下服务已正确注册：
```csharp
builder.Services.AddScoped<IProductionPlanService, ProductionPlanService>();
```

## 📝 **注意事项**

1. **数据关联**：使用LeftJoin确保即使某些关联数据不存在也能正常返回
2. **层级限制**：建议设置最大层级限制，防止无限递归
3. **性能优化**：对于大型BOM结构，考虑分页或懒加载
4. **数据一致性**：确保ParentItemId的正确性，避免循环引用
5. **字段映射**：ProductEntity和MaterialEntity使用相同的字段命名规范

## 🚀 **未来优化**

1. **缓存机制**：对BOM树形结构进行缓存，提高查询性能
2. **懒加载**：支持子节点的懒加载，减少初始查询时间
3. **搜索功能**：在BOM树中支持搜索和过滤功能
4. **版本控制**：支持BOM版本比较和差异显示 