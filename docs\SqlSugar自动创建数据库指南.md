# SqlSugar自动创建数据库指南

## 概述

本指南详细说明了如何使用SqlSugar ORM框架自动创建数据库、表和初始化数据。

## 功能特性

- ✅ 自动创建数据库（如果不存在）
- ✅ 自动创建表结构（基于实体类）
- ✅ 自动初始化种子数据
- ✅ 支持软删除
- ✅ 完整的审计字段（创建时间、更新时间等）
- ✅ 详细的日志记录
- ✅ RESTful API接口
- ✅ 数据库连接测试
- ✅ 完整的错误处理

## 项目结构

```
SqlsugarService/
├── SqlsugarService.API/           # Web API层
│   ├── Controllers/               # 控制器
│   │   ├── DatabaseController.cs  # 数据库管理控制器
│   │   ├── UsersController.cs     # 用户管理控制器
│   │   └── TestController.cs      # 测试控制器
│   ├── Program.cs                 # 应用启动配置
│   └── appsettings.json          # 配置文件
├── SqlsugarService.Domain/        # 领域层
│   ├── Users.cs                   # 用户实体
│   └── Common/
│       └── BaseEntity.cs          # 基础实体类
├── SqlsugarService.Infrastructure/ # 基础设施层
│   ├── DbContext/
│   │   └── SqlsugarDbContext.cs   # SqlSugar数据库上下文
└── SqlsugarService.Application/   # 应用层
```

## 核心组件

### 1. SqlSugarDbContext

数据库上下文类，负责配置SqlSugar连接和自动创建数据库：

```csharp
public class SqlSugarDbContext : IDisposable
{
    private readonly SqlSugarClient _db;

    public SqlSugarDbContext(IConfiguration configuration, ILogger<SqlSugarDbContext> logger)
    {
        _db = new SqlSugarClient(new ConnectionConfig
        {
            ConnectionString = connectionString,
            DbType = DbType.PostgreSQL,
            IsAutoCloseConnection = true,
            InitKeyType = InitKeyType.Attribute,
            // 其他配置...
        });
    }

    // 自动创建数据库和表
    public async Task InitializeDatabaseAsync()
    {
        // 1. 创建数据库（如果不存在）
        await CreateDatabaseIfNotExistsAsync();
        
        // 2. 创建表（如果不存在）
        await CreateTablesIfNotExistsAsync();
        
        // 3. 初始化种子数据
        await InitializeSeedDataAsync();
    }
}
```

### 2. 实体类配置

使用SqlSugar特性来配置实体类：

```csharp
[SugarTable("user")] // 指定表名
public class Users : BaseEntity
{
    [SugarColumn(IsPrimaryKey = true)]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    // 其他属性...
}
```

## 配置步骤

### 1. 数据库连接配置

在 `appsettings.json` 中配置数据库连接字符串：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=sqlsugarservice;Username=admin;Password=******;SearchPath=public"
  }
}
```

### 2. 依赖注入配置

在 `Program.cs` 中注册服务：

```csharp
// 注册数据库相关服务
builder.Services.AddScoped<SqlSugarDbContext>();

// 应用启动时自动初始化数据库
using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<SqlSugarDbContext>();
    await dbContext.InitializeDatabaseAsync();
}
```

### 3. 实体类定义

创建实体类并继承 `BaseEntity`：

```csharp
[SugarTable("your_table_name")]
public class YourEntity : BaseEntity
{
    [SugarColumn(IsPrimaryKey = true)]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    // 其他属性...
}
```

## API接口

### 数据库管理接口

- `POST /api/database/initialize` - 初始化数据库
- `POST /api/database/reset` - 重置数据库（谨慎使用）
- `GET /api/database/status` - 获取数据库状态

### 用户管理接口

- `GET /api/users` - 获取所有用户
- `GET /api/users/{id}` - 根据ID获取用户
- `POST /api/users` - 创建新用户
- `PUT /api/users/{id}` - 更新用户信息
- `DELETE /api/users/{id}` - 删除用户（软删除）

### 测试接口

- `GET /api/test/connection` - 测试数据库连接
- `POST /api/test/create-user` - 测试创建用户
- `GET /api/test/users` - 测试查询用户
- `GET /api/test/db-info` - 获取数据库信息

## 使用方法

### 1. 启动应用

```bash
dotnet run --project SqlsugarService.API
```

应用启动时会自动：
- 检查数据库是否存在，不存在则创建
- 检查表是否存在，不存在则创建
- 初始化种子数据

### 2. 测试数据库连接

```bash
curl http://localhost:5000/api/test/connection
```

### 3. 手动初始化数据库

```bash
curl -X POST http://localhost:5000/api/database/initialize
```

### 4. 检查数据库状态

```bash
curl http://localhost:5000/api/database/status
```

### 5. 测试创建用户

```bash
curl -X POST http://localhost:5000/api/test/create-user
```

### 6. 管理用户

```bash
# 获取所有用户
curl http://localhost:5000/api/users

# 创建用户
curl -X POST http://localhost:5000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "displayName": "测试用户"
  }'
```

## 高级配置

### 1. 自定义表名映射

```csharp
ConfigureExternalServices = new ConfigureExternalServices
{
    EntityNameService = (type, table) =>
    {
        // 自定义表名映射规则
        table.DbTableName = UtilMethods.ToUnderLine(table.DbTableName);
    }
}
```

### 2. 字段名映射

```csharp
ConfigureExternalServices = new ConfigureExternalServices
{
    EntityService = (prop, col) =>
    {
        // 驼峰转下划线
        col.DbColumnName = UtilMethods.ToUnderLine(col.DbColumnName);
    }
}
```

### 3. 添加更多实体类型

在 `SqlsugarDbContext.cs` 中添加新的实体类型：

```csharp
var entityTypes = new Type[]
{
    typeof(Users),
    typeof(YourNewEntity), // 添加新实体
    // 更多实体...
};
```

## 错误修复

### 修复的问题

1. **方法调用错误**：修复了SqlSugar 5.x版本的方法调用
   - `IsAnyDatabaseAsync()` → `IsAnyDatabase()`
   - `CreateDatabaseAsync()` → `CreateDatabase()`
   - `IsAnyTableAsync()` → `IsAnyTable()`
   - `InitTablesAsync()` → `InitTables()`

2. **异步方法问题**：将同步方法调用改为正确的同步调用

3. **错误处理**：完善了异常处理和日志记录

## 注意事项

1. **生产环境安全**：在生产环境中，建议禁用自动数据库创建功能，手动管理数据库结构。

2. **数据备份**：在执行数据库重置操作前，请确保已备份重要数据。

3. **权限配置**：确保数据库用户具有创建数据库和表的权限。

4. **连接字符串**：确保连接字符串中的数据库名称正确，且用户有相应权限。

5. **实体设计**：合理设计实体类，使用适当的SqlSugar特性来配置表结构。

## 故障排除

### 常见问题

1. **连接失败**：检查连接字符串和网络连接
2. **权限不足**：确保数据库用户有足够权限
3. **表已存在**：检查表名是否冲突
4. **实体配置错误**：检查SqlSugar特性配置

### 日志查看

应用会输出详细的日志信息，包括：
- SQL语句执行日志
- 数据库初始化过程
- 错误信息

## 测试验证

### 1. 数据库连接测试

```bash
curl http://localhost:5000/api/test/connection
```

### 2. 数据库信息查看

```bash
curl http://localhost:5000/api/test/db-info
```

### 3. 用户操作测试

```bash
# 测试创建用户
curl -X POST http://localhost:5000/api/test/create-user

# 测试查询用户
curl http://localhost:5000/api/test/users
```

## 扩展功能

### 1. 数据库迁移

可以扩展 `SqlSugarDbContext` 来支持数据库迁移功能。

### 2. 种子数据管理

可以创建专门的种子数据服务来管理不同类型的初始数据。

### 3. 多数据库支持

可以配置多个数据库连接，支持读写分离等高级功能。

## 总结

通过以上配置，您的应用现在具备了完整的SqlSugar自动创建数据库功能，包括：

- 自动数据库创建
- 自动表结构创建
- 种子数据初始化
- 完整的CRUD操作
- 软删除支持
- 审计字段管理
- 完整的测试接口
- 错误修复和优化

这个方案提供了灵活、可扩展的数据库管理解决方案，适合各种规模的项目使用。 