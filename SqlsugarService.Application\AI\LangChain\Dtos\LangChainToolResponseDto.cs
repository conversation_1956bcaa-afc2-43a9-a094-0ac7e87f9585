using System.Text.Json.Serialization;

namespace SqlsugarService.Application.AI.LangChain.Dtos
{
    /// <summary>
    /// LangChain工具调用响应数据传输对象
    /// </summary>
    public class LangChainToolResponseDto
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; } = true;

        /// <summary>
        /// AI生成的回复内容
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// 工具调用列表
        /// </summary>
        public List<ToolCall>? ToolCalls { get; set; }

        /// <summary>
        /// 会话ID，用于后续请求中的上下文关联
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 错误信息，仅当Success为false时有值
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 使用的模型名称
        /// </summary>
        public string? ModelName { get; set; }

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 令牌使用情况
        /// </summary>
        public TokenUsage? TokenUsage { get; set; }

        /// <summary>
        /// 是否需要提交工具执行结果
        /// </summary>
        public bool RequiresAction { get; set; }
    }

    /// <summary>
    /// 工具调用
    /// </summary>
    public class ToolCall
    {
        /// <summary>
        /// 工具调用ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 工具调用类型
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = "function";

        /// <summary>
        /// 函数调用
        /// </summary>
        [JsonPropertyName("function")]
        public FunctionCall Function { get; set; } = new FunctionCall();
    }

    /// <summary>
    /// 函数调用
    /// </summary>
    public class FunctionCall
    {
        /// <summary>
        /// 函数名称
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 函数参数（JSON字符串）
        /// </summary>
        [JsonPropertyName("arguments")]
        public string Arguments { get; set; } = string.Empty;
    }

    /// <summary>
    /// 工具执行结果提交请求
    /// </summary>
    public class ToolResultsRequest
    {
        /// <summary>
        /// 工具调用ID
        /// </summary>
        public string ToolCallId { get; set; } = string.Empty;

        /// <summary>
        /// 执行结果
        /// </summary>
        public string Output { get; set; } = string.Empty;

        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;
    }


}