using SqlSugar;
using SqlsugarService.Domain.Common;
using System;

namespace SqlsugarService.Domain.Warehouse
{
    /// <summary>
    /// 盘点单实体类
    /// </summary>
    public class InventoryOrderEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 盘点单号
        /// </summary>
        public string InventoryOrderCode { get; set; }

        /// <summary>
        /// 盘点日期
        /// </summary>
        public DateTime InventoryDate { get; set; }

        /// <summary>
        /// 审核人
        /// </summary>
        public string Auditor { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? AuditTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 审核状态（如：已审核、未审核）
        /// </summary>
        public string AuditStatus { get; set; }

        /// <summary>
        /// 所属仓库Id
        /// </summary>
        public Guid WarehouseId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
} 