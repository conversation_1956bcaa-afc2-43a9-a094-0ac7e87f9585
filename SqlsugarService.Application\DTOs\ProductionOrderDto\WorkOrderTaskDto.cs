using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic; // Added for List

namespace SqlsugarService.Application.DTOs.ProductionOrderDto
{
    /// <summary>
    /// 工单任务新增DTO
    /// </summary>
    public class AddWorkOrderTaskDto
    {
        /// <summary>
        /// 序号
        /// </summary>
        [Required(ErrorMessage = "序号不能为空")]
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        [Required(ErrorMessage = "任务编号不能为空")]
        [StringLength(50, ErrorMessage = "任务编号长度不能超过50个字符")]
        public string TaskNumber { get; set; } = string.Empty;

        /// <summary>
        /// 任务名称
        /// </summary>
        [Required(ErrorMessage = "任务名称不能为空")]
        [StringLength(200, ErrorMessage = "任务名称长度不能超过200个字符")]
        public string TaskName { get; set; } = string.Empty;

        /// <summary>
        /// 关联的生产工单ID
        /// </summary>
        [Required(ErrorMessage = "生产工单ID不能为空")]
        public Guid ProductionOrderId { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        [StringLength(100, ErrorMessage = "站点名称长度不能超过100个字符")]
        public string StationName { get; set; } = string.Empty;

        /// <summary>
        /// 工艺编号
        /// </summary>
        [StringLength(50, ErrorMessage = "工艺编号长度不能超过50个字符")]
        public string ProcessCode { get; set; } = string.Empty;

        /// <summary>
        /// 工艺名称
        /// </summary>
        [StringLength(200, ErrorMessage = "工艺名称长度不能超过200个字符")]
        public string ProcessName { get; set; } = string.Empty;

        /// <summary>
        /// 工艺流程
        /// </summary>
        [StringLength(500, ErrorMessage = "工艺流程长度不能超过500个字符")]
        public string ProcessFlow { get; set; } = string.Empty;

        /// <summary>
        /// 工艺类型
        /// </summary>
        [StringLength(50, ErrorMessage = "工艺类型长度不能超过50个字符")]
        public string ProcessType { get; set; } = string.Empty;

        /// <summary>
        /// 任务颜色（用于界面显示）
        /// </summary>
        [StringLength(20, ErrorMessage = "任务颜色长度不能超过20个字符")]
        public string TaskColor { get; set; } = string.Empty;

        /// <summary>
        /// 计划数量
        /// </summary>
        [Required(ErrorMessage = "计划数量不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "计划数量必须大于0")]
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        [Required(ErrorMessage = "计划开工时间不能为空")]
        public DateTime PlanStartTime { get; set; }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        [Required(ErrorMessage = "计划完工时间不能为空")]
        public DateTime PlanEndTime { get; set; }

        /// <summary>
        /// 计划用时（小时）
        /// </summary>
        [Required(ErrorMessage = "计划用时不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "计划用时必须大于0")]
        public decimal PlanDuration { get; set; }

        /// <summary>
        /// 任务状态（未开工、进行中、已完成、已暂停、已取消等）
        /// </summary>
        [StringLength(20, ErrorMessage = "任务状态长度不能超过20个字符")]
        public string Status { get; set; } = "未开工";

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public Guid? ProcessRouteId { get; set; }

        /// <summary>
        /// 工序ID
        /// </summary>
        public Guid? ProcessStepId { get; set; }

        /// <summary>
        /// 优先级（1-低，2-中，3-高，4-紧急）
        /// </summary>
        [Range(1, 4, ErrorMessage = "优先级必须在1-4之间")]
        public int Priority { get; set; } = 2;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 工单任务查询DTO
    /// </summary>
    public class GetWorkOrderTaskDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        public string TaskNumber { get; set; } = string.Empty;

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; } = string.Empty;

        /// <summary>
        /// 关联的生产工单ID
        /// </summary>
        public Guid ProductionOrderId { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string StationName { get; set; } = string.Empty;

        /// <summary>
        /// 工艺编号
        /// </summary>
        public string ProcessCode { get; set; } = string.Empty;

        /// <summary>
        /// 工艺名称
        /// </summary>
        public string ProcessName { get; set; } = string.Empty;

        /// <summary>
        /// 工艺流程
        /// </summary>
        public string ProcessFlow { get; set; } = string.Empty;

        /// <summary>
        /// 工艺类型
        /// </summary>
        public string ProcessType { get; set; } = string.Empty;

        /// <summary>
        /// 任务颜色（用于界面显示）
        /// </summary>
        public string TaskColor { get; set; } = string.Empty;

        /// <summary>
        /// 计划数量
        /// </summary>
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际生产数量
        /// </summary>
        public decimal ActualQuantity { get; set; }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        public DateTime PlanStartTime { get; set; }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        public DateTime PlanEndTime { get; set; }

        /// <summary>
        /// 实际开工时间
        /// </summary>
        public DateTime? ActualStartTime { get; set; }

        /// <summary>
        /// 实际完工时间
        /// </summary>
        public DateTime? ActualEndTime { get; set; }

        /// <summary>
        /// 计划用时（小时）
        /// </summary>
        public decimal PlanDuration { get; set; }

        /// <summary>
        /// 实际用时（小时）
        /// </summary>
        public decimal? ActualDuration { get; set; }

        /// <summary>
        /// 任务状态（未开工、进行中、已完成、已暂停、已取消等）
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public Guid? ProcessRouteId { get; set; }

        /// <summary>
        /// 工序ID
        /// </summary>
        public Guid? ProcessStepId { get; set; }

        /// <summary>
        /// 优先级（1-低，2-中，3-高，4-紧急）
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 工单任务搜索DTO
    /// </summary>
    public class WorkOrderTaskSearchDto
    {
        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 生产工单ID
        /// </summary>
        public Guid? ProductionOrderId { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        public string? TaskNumber { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 工艺名称
        /// </summary>
        public string? ProcessName { get; set; }
    }

    /// <summary>
    /// 批量新增工单任务DTO
    /// </summary>
    public class BatchAddWorkOrderTaskDto
    {
        /// <summary>
        /// 工单任务列表
        /// </summary>
        [Required(ErrorMessage = "工单任务列表不能为空")]
        public List<AddWorkOrderTaskDto> Tasks { get; set; } = new List<AddWorkOrderTaskDto>();
    }
} 