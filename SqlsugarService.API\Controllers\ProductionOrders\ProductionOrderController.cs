﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.ProductionOrderDto;
using SqlsugarService.Application.IService.ProductionOrders;
using SqlsugarService.Application.Until;
using SqlsugarService.Application.DTOs.Process;

namespace SqlsugarService.API.Controllers.ProductionOrders
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ProductionOrderController : ControllerBase
    {
        private readonly IProductionOrderService productorderserservice;

        public ProductionOrderController(IProductionOrderService productorderserservice)
        {
            this.productorderserservice = productorderserservice;
        }
        
        /// <summary>
        /// 获取生产工单分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<GetProductionOrderDto>>>> GetProductionOrderList([FromQuery]GetProductionOrderSearchDto search)
        {
            return await productorderserservice.GetProductionOrderList(search);
        }

        /// <summary>
        /// 根据生产工单列表获取物料信息
        /// </summary>
        /// <param name="search">搜索条件</param>
        /// <returns>包含物料信息的生产工单列表</returns>
        [HttpGet("with-materials")]
        public async Task<ApiResult<PageResult<List<GetProductionOrderWithMaterialsDto>>>> GetProductionOrderListWithMaterials([FromQuery]GetProductionOrderSearchDto search)
        {
            return await productorderserservice.GetProductionOrderListWithMaterials(search);
        }

        /// <summary>
        /// 获取生产工单的物料清单
        /// </summary>
        /// <param name="productionOrderId">生产工单ID</param>
        /// <returns>物料清单信息</returns>
        [HttpGet("material-list/{productionOrderId}")]
        public async Task<ApiResult<MaterialListResultDto>> GetProductionOrderMaterialList(Guid productionOrderId)
        {
            return await productorderserservice.GetProductionOrderMaterialList(productionOrderId);
        }

        /// <summary>
        /// 根据生产工单ID获取工艺路线信息
        /// </summary>
        /// <param name="productionOrderId">生产工单ID</param>
        /// <param name="includeDeleted">是否包含已删除的工单，默认true</param>
        /// <returns>工艺路线信息</returns>
        [HttpGet("routing/{productionOrderId}")]
        public async Task<ApiResult<GetRoutingDto>> GetProductionOrderRouting(Guid productionOrderId, [FromQuery] bool includeDeleted = true)
        {
            return await productorderserservice.GetProductionOrderRouting(productionOrderId, includeDeleted);
        }

        /// <summary>
        /// 根据生产工单ID获取工序信息
        /// </summary>
        /// <param name="productionOrderId">生产工单ID</param>
        /// <param name="includeDeleted">是否包含已删除的工单，默认true</param>
        /// <returns>工序信息列表</returns>
        [HttpGet("processes/{productionOrderId}")]
        public async Task<ApiResult<List<GetProcessDto>>> GetProductionOrderProcesses(Guid productionOrderId, [FromQuery] bool includeDeleted = true)
        {
            return await productorderserservice.GetProductionOrderProcesses(productionOrderId, includeDeleted);
        }

        /// <summary>
        /// 批量新增工单任务
        /// </summary>
        /// <param name="dto">批量工单任务信息</param>
        /// <returns>操作结果</returns>
        [HttpPost("work-order-tasks/batch")]
        public async Task<ApiResult> BatchAddWorkOrderTasks([FromBody] BatchAddWorkOrderTaskDto dto)
        {
            return await productorderserservice.BatchAddWorkOrderTasks(dto);
        }

        /// <summary>
        /// 修改生产工单状态
        /// </summary>
        /// <param name="dto">修改生产工单状态信息</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<ApiResult> UpdateProductionOrderStatus(UpdateProductionOrderStatusDto dto)
        {
            return await productorderserservice.UpdateProductionOrderStatus(dto);
        }
    }
}
