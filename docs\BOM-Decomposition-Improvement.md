# BOM分解功能完善说明

## 📋 **功能概述**

本次完善主要解决了BOM分解功能只能生成一个工单的问题，现在能够正确识别BOM树中的产品类型物料，并为每个产品生成独立的生产工单。

## 🔧 **主要改进**

### 1. **物料类型识别**
- **之前**：所有BOM节点都被标记为产品并生成工单
- **现在**：根据`MaterialTypeEnum`正确识别产品类型（Product）和物料类型（Material）
- **逻辑**：只有`MaterialTypeEnum.Product`类型的物料才会生成生产工单

### 2. **分解逻辑优化**
```csharp
// 新的分解逻辑
private void AnalyzeNodeForDecomposition(BomTreeNode node, decimal parentQuantity, List<DecompositionItem> decompositionItems)
{
    var actualQuantity = node.Quantity * parentQuantity;
    
    // 只有产品类型才需要生成生产工单
    var isProduct = node.MaterialType == MaterialTypeEnum.Product;
    
    var decompositionItem = new DecompositionItem
    {
        // ... 其他属性
        IsProduct = isProduct, // 只有产品类型才标记为需要生成工单
        MaterialType = node.MaterialType
    };
    
    decompositionItems.Add(decompositionItem);
    
    // 递归分析子节点
    foreach (var child in node.Children)
    {
        AnalyzeNodeForDecomposition(child, actualQuantity, decompositionItems);
    }
}
```

### 3. **工单生成优化**
```csharp
// 只处理产品类型的分解项目
var productItems = decompositionItems.Where(d => d.IsProduct && d.MaterialType == MaterialTypeEnum.Product).ToList();

if (!productItems.Any())
{
    // 如果没有产品类型的项目，返回空列表
    return productionOrders;
}

foreach (var item in productItems)
{
    // 为每个产品生成独立的工单
    var productionOrder = new ProductionOrder
    {
        OrderName = $"{item.MaterialName}生产工单",
        ProductName = item.MaterialName,
        PlanQuantity = item.RequiredQuantity,
        // ... 其他属性
    };
    
    productionOrders.Add(productionOrder);
}
```

### 4. **时间调度优化**
```csharp
// 改进的工单时间安排
private List<ProductionOrder> CalculateWorkOrderSchedule(List<ProductionOrder> productionOrders, ProductionPlan productionPlan)
{
    if (!productionOrders.Any())
        return productionOrders;

    // 按数量和产品名称排序
    var sortedOrders = productionOrders.OrderBy(o => o.PlanQuantity).ThenBy(o => o.ProductName).ToList();

    var totalDuration = productionPlan.PlanEndTime - productionPlan.PlanStartTime;
    var orderCount = sortedOrders.Count;
    
    // 如果只有一个工单，使用全部时间
    if (orderCount == 1)
    {
        var order = sortedOrders[0];
        order.PlanStartTime = productionPlan.PlanStartTime;
        order.PlanEndTime = productionPlan.PlanEndTime;
        return sortedOrders;
    }

    // 多个工单时，按比例分配时间
    var durationPerOrder = totalDuration.TotalHours / orderCount;
    var currentTime = productionPlan.PlanStartTime;

    foreach (var order in sortedOrders)
    {
        order.PlanStartTime = currentTime;
        order.PlanEndTime = currentTime.AddHours((double)durationPerOrder);
        currentTime = order.PlanEndTime;
    }

    return sortedOrders;
}
```

## 📊 **数据结构改进**

### 1. **DecompositionItem类增强**
```csharp
public class DecompositionItem
{
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; }
    public string MaterialNumber { get; set; }
    public decimal RequiredQuantity { get; set; }
    public string Unit { get; set; }
    public int Level { get; set; }
    public bool IsProduct { get; set; }
    public string ParentNodeId { get; set; }
    public MaterialTypeEnum MaterialType { get; set; } // 新增：物料类型
}
```

### 2. **新增BomDecompositionDetail类**
```csharp
public class BomDecompositionDetail
{
    public Guid ProductionPlanId { get; set; }
    public string ProductionPlanName { get; set; }
    public Guid BomId { get; set; }
    public decimal PlanQuantity { get; set; }
    public DateTime PlanStartTime { get; set; }
    public DateTime PlanEndTime { get; set; }
    public int Status { get; set; }
    public string StatusDescription { get; set; }
    
    // BOM结构信息
    public int BomTreeNodes { get; set; }
    public int TotalDecompositionItems { get; set; }
    
    // 产品信息
    public List<DecompositionItem> ProductItems { get; set; }
    public int ProductCount { get; set; }
    
    // 物料信息
    public List<DecompositionItem> MaterialItems { get; set; }
    public int MaterialCount { get; set; }
    
    // 工单信息
    public int WillGenerateOrders { get; set; }
    public List<ProductionOrder> ExistingOrders { get; set; }
    public int ExistingOrderCount { get; set; }
    
    // 分解状态
    public bool CanDecompose { get; set; }
    public bool IsDecomposed { get; set; }
    
    // 时间信息
    public DateTime AnalysisTime { get; set; }
}
```

## 🚀 **新增API接口**

### 1. **获取BOM分解详细信息**
```http
GET /api/BomDecomposition/detail/{productionPlanId}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "成功",
    "data": {
        "productionPlanId": "guid",
        "productionPlanName": "生产计划A",
        "bomId": "guid",
        "planQuantity": 100,
        "status": 0,
        "statusDescription": "未分解",
        "bomTreeNodes": 5,
        "totalDecompositionItems": 8,
        "productItems": [
            {
                "materialId": "guid",
                "materialName": "产品A",
                "materialType": "Product",
                "requiredQuantity": 100,
                "isProduct": true
            }
        ],
        "productCount": 2,
        "materialItems": [
            {
                "materialId": "guid",
                "materialName": "物料B",
                "materialType": "Material",
                "requiredQuantity": 200,
                "isProduct": false
            }
        ],
        "materialCount": 6,
        "willGenerateOrders": 2,
        "existingOrderCount": 0,
        "canDecompose": true,
        "isDecomposed": false
    }
}
```

## 🔍 **调试功能增强**

### 1. **调试信息优化**
- 正确显示产品类型和物料类型的统计
- 显示哪些项目会生成工单
- 提供更详细的分解过程信息

### 2. **预览功能优化**
- 正确计算预估工单数量
- 只统计产品类型的分解项目

## 📈 **使用场景示例**

### 场景1：简单BOM结构
```
产品A (Product)
├── 物料B (Material)
├── 产品C (Product)
│   ├── 物料D (Material)
│   └── 物料E (Material)
└── 物料F (Material)
```

**分解结果：**
- 生成2个工单：产品A、产品C
- 物料B、D、E、F不生成工单（作为原料）

### 场景2：复杂BOM结构
```
成品X (Product)
├── 半成品Y (Product)
│   ├── 原料A (Material)
│   └── 原料B (Material)
├── 半成品Z (Product)
│   ├── 原料C (Material)
│   └── 原料D (Material)
└── 包装材料 (Material)
```

**分解结果：**
- 生成3个工单：成品X、半成品Y、半成品Z
- 原料A、B、C、D和包装材料不生成工单

## ✅ **验证要点**

1. **物料类型识别**：确保只有Product类型生成工单
2. **数量计算**：确保BOM数量关系正确传递
3. **时间安排**：确保多个工单的时间分配合理
4. **状态控制**：确保分解和撤销的状态验证正确
5. **数据完整性**：确保工单信息完整保存

## 🎯 **总结**

通过本次完善，BOM分解功能现在能够：

1. ✅ 正确识别产品类型和物料类型
2. ✅ 为每个产品生成独立的生产工单
3. ✅ 提供详细的分解信息和分析
4. ✅ 支持多层级BOM结构的分解
5. ✅ 提供完善的调试和预览功能

这解决了之前只能生成一个工单的问题，现在能够根据BOM结构中的产品类型正确生成多个生产工单。 