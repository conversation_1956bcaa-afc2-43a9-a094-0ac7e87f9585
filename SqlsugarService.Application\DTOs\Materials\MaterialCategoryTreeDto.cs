using System;
using System.Collections.Generic;

namespace SqlsugarService.Application.DTOs.Materials
{
    /// <summary>
    /// 物料分类树形结构DTO
    /// </summary>
    public class MaterialCategoryTreeDto
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 父级分类ID
        /// </summary>
        public Guid? ParentId { get; set; }

        /// <summary>
        /// 子分类
        /// </summary>
        public List<MaterialCategoryTreeDto> Children { get; set; } = new List<MaterialCategoryTreeDto>();
    }
} 