# 🚀 MES 智能助手 - 5 分钟快速入门

## ⚡ 重要提示

**本系统已预配置扣子 AI 智能体，无需额外配置即可使用！**

- ✅ **推荐使用**：`/api/mesassistant/chat-kouzi` （扣子 AI，已配置）
- ⚠️ **可选功能**：`/api/mesassistant/chat` （LangChain，需要 OpenAI 密钥）

## 📋 前置条件

确保您的电脑已安装：

- ✅ .NET 6.0 SDK 或更高版本
- ✅ Visual Studio 2022 或 VS Code
- ✅ SQL Server (可选，有内置测试数据)

## 🎯 第一步：启动服务

### 1. 打开项目

```bash
# 进入项目目录
cd C:\Users\<USER>\Desktop\EmployeeService\EmployeeService
```

### 2. 启动主服务

```bash
# 启动MES API服务
cd SqlsugarService.API
dotnet run
```

看到这样的输出表示成功：

```
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: https://localhost:7001
      Now listening on: http://localhost:5001
```

## 🌐 第二步：访问 API 文档

1. **打开浏览器**，访问：

```
https://localhost:7001/swagger
```

2. **找到 MES 助手接口**：
   - 滚动到 `MESAssistant` 部分
   - 点击 `POST /api/mesassistant/chat` 展开

## 💬 第三步：第一次对话

### 🚀 新功能：智能工具调用

现在 MES 助手支持两种模式：

- **`POST /api/mesassistant/chat-kouzi`** - 使用扣子 AI 智能体（推荐，已配置）
- **`POST /api/mesassistant/chat`** - 使用 LangChain（需要配置 OpenAI 密钥）

### 测试 1：基础问候（扣子 AI 模式）

在 Swagger 界面中找到 `POST /api/mesassistant/chat-kouzi`：

1. 点击 **"Try it out"** 按钮
2. 在请求体中输入：

```json
{
  "message": "你好，请介绍一下你的功能",
  "userId": "test_user",
  "conversationId": "quick_start_001"
}
```

3. 点击 **"Execute"** 执行

**预期结果**：AI 会介绍自己的功能和能力

### 测试 2：智能查询功能（扣子 AI 智能体）

```json
{
  "message": "查询今天的生产订单",
  "userId": "test_user",
  "conversationId": "quick_start_001"
}
```

**预期结果**：

- 扣子 AI 会理解您的查询需求
- 提供专业的 MES 系统指导
- 给出相关的操作建议

### 测试 3：复杂查询（多工具协作）

```json
{
  "message": "查询物料A001的库存情况，如果库存不足请给出建议",
  "userId": "test_user",
  "conversationId": "quick_start_001"
}
```

**预期结果**：

- AI 会自动调用 QueryMaterialInventory 工具
- 分析库存数据
- 提供专业的补货建议

### 测试 4：获取帮助

```json
{
  "message": "如何创建生产计划？",
  "userId": "test_user",
  "conversationId": "quick_start_001"
}
```

**预期结果**：AI 会提供详细的操作指导

## 🔧 第四步：常用功能测试

### 📊 数据查询类

```json
// 查询物料库存
{
  "message": "物料A001的库存情况如何？",
  "userId": "warehouse_manager",
  "conversationId": "inventory_check"
}

// 查询销售订单
{
  "message": "查询本月的销售订单",
  "userId": "sales_manager",
  "conversationId": "sales_query"
}

// 查询BOM信息
{
  "message": "查询产品ID为1的BOM结构",
  "userId": "engineer",
  "conversationId": "bom_query"
}
```

### 📈 报表生成类

```json
// 生产统计报表
{
  "message": "生成本月的生产统计报表",
  "userId": "production_manager",
  "conversationId": "report_gen"
}

// 效率分析
{
  "message": "分析最近一周的生产效率",
  "userId": "analyst",
  "conversationId": "efficiency_analysis"
}
```

### 🎓 学习指导类

```json
// 操作指导
{
  "message": "如何更新订单状态？",
  "userId": "operator",
  "conversationId": "training"
}

// 故障处理
{
  "message": "设备出现故障应该怎么处理？",
  "userId": "technician",
  "conversationId": "troubleshooting"
}
```

## 🎪 第五步：高级功能体验

### 连续对话测试

使用相同的 `conversationId` 进行多轮对话：

**第 1 轮**：

```json
{
  "message": "查询生产订单PO001的详细信息",
  "userId": "manager",
  "conversationId": "continuous_chat"
}
```

**第 2 轮**：

```json
{
  "message": "这个订单的进度为什么这么慢？",
  "userId": "manager",
  "conversationId": "continuous_chat"
}
```

**第 3 轮**：

```json
{
  "message": "给我一些提升进度的建议",
  "userId": "manager",
  "conversationId": "continuous_chat"
}
```

AI 会记住上下文，知道"这个订单"指的是 PO001。

### 流式对话测试

使用 `POST /api/mesassistant/chat-stream` 接口体验实时响应。

## 📱 第六步：集成到您的应用

### JavaScript 示例

```javascript
// 发送消息到MES助手（使用扣子AI）
async function sendMessageToMES(message, userId, conversationId) {
  const response = await fetch(
    "https://localhost:7001/api/mesassistant/chat-kouzi",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        message: message,
        userId: userId,
        conversationId: conversationId,
      }),
    }
  );

  const result = await response.json();
  return result;
}

// 使用示例
sendMessageToMES("查询今天的生产订单", "user123", "conv456").then(
  (response) => {
    console.log("AI回复:", response.message);
    if (response.needsToolExecution) {
      console.log("需要执行的工具:", response.requiredActions);
    }
  }
);
```

### C# 示例

```csharp
public class MESChatClient
{
    private readonly HttpClient _httpClient;

    public MESChatClient()
    {
        _httpClient = new HttpClient();
        _httpClient.BaseAddress = new Uri("https://localhost:7001");
    }

    public async Task<MESAssistantResponseDto> SendMessageAsync(string message, string userId, string conversationId)
    {
        var request = new MESAssistantRequestDto
        {
            Message = message,
            UserId = userId,
            ConversationId = conversationId
        };

        var response = await _httpClient.PostAsJsonAsync("/api/mesassistant/chat", request);
        return await response.Content.ReadFromJsonAsync<MESAssistantResponseDto>();
    }
}

// 使用示例
var client = new MESChatClient();
var response = await client.SendMessageAsync("查询生产订单", "user123", "conv456");
Console.WriteLine($"AI回复: {response.Message}");
```

## 🔍 第七步：验证功能完整性

### 检查清单

- [ ] ✅ 基础对话功能正常
- [ ] ✅ 生产订单查询工作
- [ ] ✅ 物料库存查询工作
- [ ] ✅ 销售订单查询工作
- [ ] ✅ BOM 信息查询工作
- [ ] ✅ 报表生成功能正常
- [ ] ✅ 操作指导功能正常
- [ ] ✅ 连续对话记忆正常
- [ ] ✅ 错误处理机制正常

### 性能检查

- [ ] ✅ 响应时间 < 5 秒
- [ ] ✅ 并发请求处理正常
- [ ] ✅ 内存使用稳定
- [ ] ✅ 日志记录完整

## 🎉 恭喜！您已成功启动 MES 智能助手

现在您可以：

### 🎯 立即使用

- 通过 Swagger 界面进行各种测试
- 体验 AI 的智能对话能力
- 测试各种 MES 业务场景

### 🔧 进一步定制

- 修改系统提示词以适应您的业务
- 添加新的工具函数
- 集成到您现有的系统中

### 📚 深入学习

- 阅读完整的 `LangChain_MES_Assistant_Guide.md`
- 查看源代码了解实现细节
- 参考开发者指南进行扩展

## 🆘 遇到问题？

### 常见问题快速解决

**问题 1：服务启动失败**

```bash
# 检查端口是否被占用
netstat -ano | findstr :7001

# 如果被占用，结束进程或修改端口
```

**问题 2：AI 不回复**

- 检查网络连接
- 确认扣子 AI 配置正确（已预配置）
- 如使用 LangChain 模式，需要配置 OpenAI API 密钥
- 查看控制台错误日志

**问题 3：数据查询失败**

- 确认数据库连接正常
- 检查数据库中是否有测试数据
- 查看应用日志中的具体错误

### 获取帮助

- 📧 技术支持：<EMAIL>
- 📞 热线电话：400-123-4567
- 📖 完整文档：`LangChain_MES_Assistant_Guide.md`

---

**🎊 开始您的 MES 智能化之旅吧！**

_快速入门指南 v1.0 | 2025-01-31_
