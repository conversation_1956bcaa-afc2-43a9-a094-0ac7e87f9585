# BOM树形结构测试脚本（修复版）
# 用于测试GetBomTreeStructure方法

param(
    [Parameter(Mandatory=$true)]
    [string]$BomId,
    
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== BOM树形结构测试（修复版） ===" -ForegroundColor Green
Write-Host "BOM ID: $BomId" -ForegroundColor Yellow
Write-Host "API地址: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

try {
    # 1. 获取BOM树形结构
    Write-Host "1. 获取BOM树形结构..." -ForegroundColor White
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/bom-structure/$BomId" -Method Get
    
    if ($response.isSuc) {
        $bomTree = $response.data
        Write-Host "   获取成功!" -ForegroundColor Green
        Write-Host "   根节点数: $($bomTree.Count)" -ForegroundColor Gray
        
        # 分析父子关系
        Write-Host "`n2. 分析父子关系..." -ForegroundColor White
        function Analyze-ParentChildRelations {
            param($nodes, $level = 0)
            foreach ($node in $nodes) {
                $indent = "  " * $level
                Write-Host "$indent节点ID: $($node.id)" -ForegroundColor Yellow
                Write-Host "$indent父节点ID: $($node.parentItemId)" -ForegroundColor Yellow
                Write-Host "$indent显示名称: $($node.displayName)" -ForegroundColor Yellow
                Write-Host "$indent层级: $($node.level)" -ForegroundColor Yellow
                Write-Host "$indent子节点数: $($node.children.Count)" -ForegroundColor Yellow
                Write-Host ""
                
                if ($node.children -and $node.children.Count -gt 0) {
                    Analyze-ParentChildRelations -nodes $node.children -level ($level + 1)
                }
            }
        }
        
        Analyze-ParentChildRelations -nodes $bomTree
        
        # 显示BOM树结构
        Write-Host "`n3. 显示BOM树结构..." -ForegroundColor White
        function Show-BomTreeStructure {
            param($nodes, $level = 0)
            foreach ($node in $nodes) {
                $indent = "  " * $level
                $expandIcon = if ($node.isExpandable) { "▼" } else { "  " }
                Write-Host "$indent$expandIcon [$($node.sequence)] $($node.displayName)" -ForegroundColor Gray
                Write-Host "$indent    节点ID: $($node.id)" -ForegroundColor DarkGray
                Write-Host "$indent    父节点ID: $($node.parentItemId)" -ForegroundColor DarkGray
                Write-Host "$indent    产品编号: $($node.displayProductNumber)" -ForegroundColor DarkGray
                Write-Host "$indent    规格型号: $($node.displaySpecification)" -ForegroundColor DarkGray
                Write-Host "$indent    单位: $($node.unit)" -ForegroundColor DarkGray
                Write-Host "$indent    BOM编号: $($node.bomNumber)" -ForegroundColor DarkGray
                Write-Host "$indent    BOM版本: $($node.bomVersion)" -ForegroundColor DarkGray
                Write-Host "$indent    使用量: $($node.usageQuantity)" -ForegroundColor DarkGray
                Write-Host "$indent    使用比例: $($node.usageRatio)" -ForegroundColor DarkGray
                Write-Host "$indent    层级: $($node.level)" -ForegroundColor DarkGray
                Write-Host "$indent    可展开: $($node.isExpandable)" -ForegroundColor DarkGray
                
                # 显示产品信息
                if (![string]::IsNullOrEmpty($node.productName)) {
                    Write-Host "$indent    产品名称: $($node.productName)" -ForegroundColor Cyan
                    Write-Host "$indent    产品编号: $($node.productNumber)" -ForegroundColor Cyan
                    Write-Host "$indent    产品规格: $($node.productSpecification)" -ForegroundColor Cyan
                }
                
                # 显示物料信息
                if (![string]::IsNullOrEmpty($node.materialName)) {
                    Write-Host "$indent    物料名称: $($node.materialName)" -ForegroundColor Magenta
                    Write-Host "$indent    物料编号: $($node.materialNumber)" -ForegroundColor Magenta
                    Write-Host "$indent    物料规格: $($node.materialSpecification)" -ForegroundColor Magenta
                }
                
                if ($node.children -and $node.children.Count -gt 0) {
                    Write-Host "$indent    子节点数: $($node.children.Count)" -ForegroundColor DarkGray
                    Show-BomTreeStructure -nodes $node.children -level ($level + 1)
                }
                Write-Host ""
            }
        }
        
        Show-BomTreeStructure -nodes $bomTree
        
        # 统计信息
        Write-Host "`n4. 统计信息..." -ForegroundColor White
        function Count-Nodes {
            param($nodes)
            $count = $nodes.Count
            foreach ($node in $nodes) {
                if ($node.children -and $node.children.Count -gt 0) {
                    $count += Count-Nodes -nodes $node.children
                }
            }
            return $count
        }
        
        $totalNodes = Count-Nodes -nodes $bomTree
        Write-Host "   总节点数: $totalNodes" -ForegroundColor Gray
        
        # 分析节点类型
        function Analyze-NodeTypes {
            param($nodes)
            $productCount = 0
            $materialCount = 0
            
            foreach ($node in $nodes) {
                if (![string]::IsNullOrEmpty($node.productName)) {
                    $productCount++
                } elseif (![string]::IsNullOrEmpty($node.materialName)) {
                    $materialCount++
                }
                
                if ($node.children -and $node.children.Count -gt 0) {
                    $childCounts = Analyze-NodeTypes -nodes $node.children
                    $productCount += $childCounts.ProductCount
                    $materialCount += $childCounts.MaterialCount
                }
            }
            
            return @{
                ProductCount = $productCount
                MaterialCount = $materialCount
            }
        }
        
        $nodeTypes = Analyze-NodeTypes -nodes $bomTree
        Write-Host "   产品节点数: $($nodeTypes.ProductCount)" -ForegroundColor Gray
        Write-Host "   物料节点数: $($nodeTypes.MaterialCount)" -ForegroundColor Gray
        
    } else {
        Write-Host "   获取失败: $($response.msg)" -ForegroundColor Red
        exit 1
    }

    Write-Host "`n=== 测试完成 ===" -ForegroundColor Green

} catch {
    Write-Host "测试过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误信息: $($_.Exception)" -ForegroundColor Red
} 