﻿using AutoMapper;
using SqlsugarService.Application.DTOs.PlanDto;
using SqlsugarService.Application.DTOs.ProductionOrderDto;
using SqlsugarService.Application.DTOs.SalesDto;
using SqlsugarService.Application.DTOs.WorkOrderTaskDto;
using SqlsugarService.Domain.InventoryChange;
using SqlsugarService.Domain.Materials;
using SqlsugarService.Domain.Plan;
using SqlsugarService.Domain.QualityInspection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.API
{
    /// <summary>
    /// AutoMapper配置文件
    /// </summary>
    public class Mapperfiles : Profile
    {
        public Mapperfiles()
        {
            //销售订单新增修改
            CreateMap<insertupdatesalesorderDto, Salesorder>().ReverseMap();
            //销售订单列表
            CreateMap<getsalesorderDto, Salesorder>().ReverseMap();
            //产品列表
            CreateMap<GetProductDto, ProductEntity>().ReverseMap();
            //产品新增
            CreateMap<InsertupdateproductentityDto, ProductEntity>().ReverseMap();
            //生产计划 
            CreateMap<InsertupdateproductionplanDto, ProductionPlan>().ReverseMap();
            CreateMap<GetproductionplanDto, ProductionPlan>().ReverseMap();
            //生产工单
            CreateMap<GetProductionOrderDto, ProductionOrder>().ReverseMap();

            //工单任务
            CreateMap<Application.DTOs.WorkOrderTaskDto.GetWorkOrderTaskDto, WorkOrderTaskEntity>().ReverseMap();

            //报工质检
            CreateMap<WorkReportDto, WorkReportInspectionEntity>().ReverseMap();

        }
    }
}
