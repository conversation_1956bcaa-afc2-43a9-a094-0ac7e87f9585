using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace SqlsugarService.Application.AI.LangChain.Dtos
{
    /// <summary>
    /// LangChain工具调用请求数据传输对象
    /// </summary>
    public class LangChainToolRequestDto
    {
        /// <summary>
        /// 用户输入的消息内容
        /// </summary>
        [Required(ErrorMessage = "消息内容不能为空")]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID，用于区分不同用户的对话历史
        /// </summary>
        public string? UserId { get; set; } = "default_user";

        /// <summary>
        /// 会话ID，用于区分同一用户的不同会话
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 可用工具列表
        /// </summary>
        public List<ToolDefinition> Tools { get; set; } = new List<ToolDefinition>();

        /// <summary>
        /// 是否启用记忆功能，默认启用
        /// </summary>
        public bool EnableMemory { get; set; } = true;

        /// <summary>
        /// 模型名称，如果为空则使用配置中的默认模型
        /// </summary>
        public string? ModelName { get; set; }

        /// <summary>
        /// 温度参数，控制生成文本的随机性，取值范围0-1
        /// </summary>
        public float? Temperature { get; set; }

        /// <summary>
        /// 系统提示词，用于设置AI助手的行为和角色
        /// </summary>
        public string? SystemPrompt { get; set; }
    }

    /// <summary>
    /// 工具定义
    /// </summary>
    public class ToolDefinition
    {
        /// <summary>
        /// 工具类型
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = "function";

        /// <summary>
        /// 工具函数
        /// </summary>
        [JsonPropertyName("function")]
        public ToolFunction Function { get; set; } = new ToolFunction();
    }

    /// <summary>
    /// 工具函数定义
    /// </summary>
    public class ToolFunction
    {
        /// <summary>
        /// 函数名称
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 函数描述
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 参数定义
        /// </summary>
        [JsonPropertyName("parameters")]
        public ToolParameters Parameters { get; set; } = new ToolParameters();
    }

    /// <summary>
    /// 工具参数定义
    /// </summary>
    public class ToolParameters
    {
        /// <summary>
        /// 参数类型
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = "object";

        /// <summary>
        /// 属性定义
        /// </summary>
        [JsonPropertyName("properties")]
        public Dictionary<string, ToolProperty> Properties { get; set; } = new Dictionary<string, ToolProperty>();

        /// <summary>
        /// 必需属性
        /// </summary>
        [JsonPropertyName("required")]
        public List<string> Required { get; set; } = new List<string>();
    }

    /// <summary>
    /// 工具属性定义
    /// </summary>
    public class ToolProperty
    {
        /// <summary>
        /// 属性类型
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = "string";

        /// <summary>
        /// 属性描述
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 枚举值（可选）
        /// </summary>
        [JsonPropertyName("enum")]
        public List<string>? Enum { get; set; }
    }
} 