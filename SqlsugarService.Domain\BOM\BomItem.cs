﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Craftsmanship;
using SqlsugarService.Domain.Materials;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.BOM
{
    /// <summary>
    /// BOM产品物料明细表
    /// </summary>
    [SugarTable("BomItem")]
    public class BomItem : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// BOM主表Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public Guid BomId { get; set; }
       [SqlSugar.SugarColumn(IsNullable = true,Length =500)]
        public BomInfo? Bom { get; set; }

        /// <summary>
        /// 产品Id
        /// </summary>
       [SqlSugar.SugarColumn(IsNullable = true)]
        public Guid MaterialId { get; set; }
        [SqlSugar.SugarColumn(IsNullable = true, Length = 500)]
        public MaterialEntity? Material { get; set; }

        /// <summary>
        /// 父级明细Id
        /// </summary>
        [SqlSugar.SugarColumn(IsNullable = true)]
        public string? ParentItemId { get; set; }
        [SqlSugar.SugarColumn(IsNullable = true, Length = 500)]
        public BomItem? ParentItem { get; set; }

        /// <summary>
        /// 用量
        /// </summary>
       [SqlSugar.SugarColumn(IsNullable = true)]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 损耗率
        /// </summary>
       [SqlSugar.SugarColumn(IsNullable = true)]
        public decimal LossRate { get; set; }

        /// <summary>
        /// 投入产出类型
        /// </summary>
       [SqlSugar.SugarColumn(IsNullable = true)]
        public MaterialRelationType InOutType { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
       [SqlSugar.SugarColumn(IsNullable = true)]

        public string? Unit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
       [SugarColumn(IsNullable = true)]

        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 子级明细列表
        /// </summary>
       [SugarColumn(IsNullable = true, Length = 500)]
        public ICollection<BomItem> ChildItems { get; set; } = new List<BomItem>();
    }
}
