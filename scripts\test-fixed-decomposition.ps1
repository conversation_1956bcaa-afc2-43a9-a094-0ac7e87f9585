# 测试修复后的BOM分解功能

param(
    [string]$BaseUrl = "http://localhost:5000",
    [string]$ProductionPlanId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"
)

Write-Host "=== 测试修复后的BOM分解功能 ===" -ForegroundColor Green
Write-Host "生产计划ID: $ProductionPlanId" -ForegroundColor Yellow

# 测试函数
function Test-Decomposition {
    param(
        [string]$Url,
        [string]$Method,
        [string]$Description,
        [object]$Body = $null
    )
    
    Write-Host "`n测试: $Description" -ForegroundColor Cyan
    Write-Host "URL: $Url" -ForegroundColor Gray
    Write-Host "方法: $Method" -ForegroundColor Gray
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Method -eq "POST") {
            $response = Invoke-RestMethod -Uri $Url -Method Post -Headers $headers -Body ($Body | ConvertTo-Json -Depth 10) -TimeoutSec 60
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method Get -Headers $headers -TimeoutSec 30
        }
        
        if ($response.isSuc) {
            Write-Host "✅ 成功" -ForegroundColor Green
            Show-Result $response.data
        } else {
            Write-Host "❌ 失败: $($response.message)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示结果
function Show-Result {
    param(
        [object]$Data
    )
    
    if ($Data.TotalOrders) {
        Write-Host "生成工单数: $($Data.TotalOrders)" -ForegroundColor White
    }
    
    if ($Data.Orders -and $Data.Orders.Count -gt 0) {
        Write-Host "生成的工单：" -ForegroundColor Yellow
        foreach ($order in $Data.Orders) {
            Write-Host "  - $($order.OrderName) (编号: $($order.OrderNumber))" -ForegroundColor White
            Write-Host "    生产计划ID: $($order.ProductionPlanId)" -ForegroundColor Gray
            Write-Host "    产品: $($order.ProductName), 数量: $($order.PlanQuantity) $($order.Unit)" -ForegroundColor Gray
        }
    }
    
    if ($Data.DecompositionItems -and $Data.DecompositionItems.Count -gt 0) {
        Write-Host "分解项目：" -ForegroundColor Yellow
        foreach ($item in $Data.DecompositionItems) {
            Write-Host "  - $($item.MaterialName) (数量: $($item.RequiredQuantity) $($item.Unit))" -ForegroundColor White
        }
    }
}

# 执行测试
try {
    Write-Host "=== 开始测试修复后的分解功能 ===" -ForegroundColor Cyan
    
    # 1. 预览分解结果
    Test-Decomposition -Url "$BaseUrl/api/BomDecomposition/preview/$ProductionPlanId" -Method "GET" -Description "预览分解结果"
    
    # 2. 执行分解
    Test-Decomposition -Url "$BaseUrl/api/BomDecomposition/decompose/$ProductionPlanId" -Method "POST" -Description "执行分解"
    
} catch {
    Write-Host "测试过程中发生错误：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "修复内容：" -ForegroundColor Yellow
Write-Host "1. 添加了生产计划ID验证" -ForegroundColor White
Write-Host "2. 确保所有必需字段都正确设置" -ForegroundColor White
Write-Host "3. 添加了详细的错误处理" -ForegroundColor White
Write-Host "4. 支持所有类型物料的分解" -ForegroundColor White 