# 测试所有类型物料分解功能的PowerShell脚本

param(
    [string]$BaseUrl = "http://localhost:5000",
    [string]$ProductionPlanId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"
)

Write-Host "=== 测试所有类型物料分解功能 ===" -ForegroundColor Green
Write-Host "生产计划ID: $ProductionPlanId" -ForegroundColor Yellow

# 测试函数
function Test-Decomposition {
    param(
        [string]$Url,
        [string]$Method,
        [string]$Description,
        [object]$Body = $null
    )
    
    Write-Host "`n测试: $Description" -ForegroundColor Cyan
    Write-Host "URL: $Url" -ForegroundColor Gray
    Write-Host "方法: $Method" -ForegroundColor Gray
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Method -eq "POST") {
            $response = Invoke-RestMethod -Uri $Url -Method Post -Headers $headers -Body ($Body | ConvertTo-Json -Depth 10) -TimeoutSec 60
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method Get -Headers $headers -TimeoutSec 30
        }
        
        if ($response.isSuc) {
            Write-Host "✅ 成功" -ForegroundColor Green
            Show-Result $response.data
        } else {
            Write-Host "❌ 失败: $($response.message)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示结果
function Show-Result {
    param(
        [object]$Data
    )
    
    if ($Data.Steps -and $Data.Steps.Count -gt 0) {
        Write-Host "调试步骤分析：" -ForegroundColor Yellow
        foreach ($step in $Data.Steps) {
            Write-Host "  $($step.Step): $($step.Status)" -ForegroundColor $(if($step.Status -eq "成功") { "Green" } else { "Red" })
            
            if ($step.Data -and $step.Data.TotalItems) {
                Write-Host "    分解项目总数: $($step.Data.TotalItems)" -ForegroundColor White
                Write-Host "    所有项目都会生成工单: $($step.Data.AllItems)" -ForegroundColor White
            }
            
            if ($step.Data -and $step.Data.TotalOrders) {
                Write-Host "    生成工单总数: $($step.Data.TotalOrders)" -ForegroundColor White
            }
        }
    }
    
    if ($Data.EstimatedOrders) {
        Write-Host "预估工单数: $($Data.EstimatedOrders)" -ForegroundColor White
    }
    
    if ($Data.TotalOrders) {
        Write-Host "实际生成工单数: $($Data.TotalOrders)" -ForegroundColor White
    }
    
    if ($Data.DecompositionItems -and $Data.DecompositionItems.Count -gt 0) {
        Write-Host "分解项目：" -ForegroundColor Yellow
        foreach ($item in $Data.DecompositionItems) {
            Write-Host "  - $($item.MaterialName) (数量: $($item.RequiredQuantity) $($item.Unit))" -ForegroundColor White
        }
    }
}

# 执行测试
try {
    Write-Host "=== 开始测试所有类型物料分解 ===" -ForegroundColor Cyan
    
    # 1. 调试分解过程
    Test-Decomposition -Url "$BaseUrl/api/BomDecomposition/debug/$ProductionPlanId" -Method "GET" -Description "调试分解过程"
    
    # 2. 预览分解结果
    Test-Decomposition -Url "$BaseUrl/api/BomDecomposition/preview/$ProductionPlanId" -Method "GET" -Description "预览分解结果"
    
    # 3. 执行分解
    Test-Decomposition -Url "$BaseUrl/api/BomDecomposition/decompose/$ProductionPlanId" -Method "POST" -Description "执行分解"
    
    # 4. 再次调试查看结果
    Test-Decomposition -Url "$BaseUrl/api/BomDecomposition/debug/$ProductionPlanId" -Method "GET" -Description "查看分解结果"
    
} catch {
    Write-Host "测试过程中发生错误：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "修改说明：" -ForegroundColor Yellow
Write-Host "1. 所有类型的物料（Product和Material）都会生成生产工单" -ForegroundColor White
Write-Host "2. 不再限制只有Product类型才能分解" -ForegroundColor White
Write-Host "3. 每个BOM节点都会生成对应的生产工单" -ForegroundColor White
Write-Host "4. 支持多层级BOM结构的完整分解" -ForegroundColor White 