-- 测试BOM数据插入脚本
-- 用于测试BOM树形下拉列表功能

-- 1. 插入测试物料
INSERT INTO "MaterialEntity" ("Id", "MaterialNumber", "MaterialName", "SpecificationModel", "Unit", "MaterialType", "MaterialProperty", "Status", "CreatedAt", "UpdatedAt", "IsDeleted")
VALUES 
    ('11111111-1111-1111-1111-111111111111', 'MAT001', '测试物料1', '规格1', '个', 1, '外购', '启用', NOW(), NOW(), false),
    ('22222222-2222-2222-2222-222222222222', 'MAT002', '测试物料2', '规格2', '个', 1, '外购', '启用', NOW(), NOW(), false),
    ('33333333-3333-3333-3333-333333333333', 'MAT003', '测试物料3', '规格3', '个', 1, '外购', '启用', NOW(), NOW(), false),
    ('44444444-4444-4444-4444-444444444444', 'PROD001', '测试产品1', '产品规格1', '台', 2, '自制', '启用', NOW(), NOW(), false)
ON CONFLICT ("Id") DO NOTHING;

-- 2. 插入测试BOM信息
INSERT INTO "BomInfo" ("Id", "BomNumber", "IsSystemNumber", "IsDefault", "Version", "ProductId", "ProductName", "ColorCode", "Unit", "DailyOutput", "Remark", "CreatedAt", "UpdatedAt", "IsDeleted")
VALUES 
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'BOM001', true, true, '1.0', '44444444-4444-4444-4444-444444444444', '测试产品1', '红色', '台', 100.0, '测试BOM', NOW(), NOW(), false)
ON CONFLICT ("Id") DO NOTHING;

-- 3. 插入测试BOM项目（树形结构）
INSERT INTO "BomItem" ("Id", "BomId", "MaterialId", "ParentItemId", "Quantity", "LossRate", "InOutType", "Unit", "Remark", "CreatedAt", "UpdatedAt", "IsDeleted")
VALUES 
    -- 根节点（产品）
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '44444444-4444-4444-4444-444444444444', NULL, 1.0, 0.0, 2, '台', '主产品', NOW(), NOW(), false),
    
    -- 一级子节点（物料1）
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 2.0, 0.05, 1, '个', '主要物料', NOW(), NOW(), false),
    
    -- 一级子节点（物料2）
    ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 1.5, 0.02, 1, '个', '辅助物料', NOW(), NOW(), false),
    
    -- 二级子节点（物料3，属于物料1）
    ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '33333333-3333-3333-3333-333333333333', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 0.5, 0.01, 1, '个', '子物料', NOW(), NOW(), false)
ON CONFLICT ("Id") DO NOTHING;

-- 4. 验证插入结果
SELECT 'BOM数据插入完成' as message;

-- 5. 查询验证
SELECT 
    'BomInfo' as table_name,
    COUNT(*) as record_count
FROM "BomInfo" 
WHERE "IsDeleted" = false
UNION ALL
SELECT 
    'BomItem' as table_name,
    COUNT(*) as record_count
FROM "BomItem" 
WHERE "IsDeleted" = false
UNION ALL
SELECT 
    'MaterialEntity' as table_name,
    COUNT(*) as record_count
FROM "MaterialEntity" 
WHERE "IsDeleted" = false;

-- 6. 显示BOM树形结构
SELECT 
    bi."Id",
    bi."ParentItemId",
    bi."Quantity",
    bi."Unit",
    m."MaterialName",
    m."MaterialNumber",
    CASE 
        WHEN bi."ParentItemId" IS NULL THEN '根节点'
        WHEN EXISTS (SELECT 1 FROM "BomItem" WHERE "ParentItemId" = bi."Id") THEN '父节点'
        ELSE '叶子节点'
    END as node_type
FROM "BomItem" bi
LEFT JOIN "MaterialEntity" m ON bi."MaterialId" = m."Id"
WHERE bi."BomId" = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'
  AND bi."IsDeleted" = false
ORDER BY bi."ParentItemId" NULLS FIRST, bi."Id"; 