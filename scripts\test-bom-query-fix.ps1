# BOM查询修复测试脚本
# 用于测试修复后的BOM查询和父子关系逻辑

param(
    [Parameter(Mandatory=$true)]
    [string]$BomId,
    
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== BOM查询修复测试 ===" -ForegroundColor Green
Write-Host "BOM ID: $BomId" -ForegroundColor Yellow
Write-Host "API地址: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

try {
    # 1. 获取BOM树形结构
    Write-Host "1. 获取BOM树形结构..." -ForegroundColor White
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/bom-structure/$BomId" -Method Get
    
    if ($response.isSuc) {
        $bomTree = $response.data
        Write-Host "   获取成功!" -ForegroundColor Green
        Write-Host "   根节点数: $($bomTree.Count)" -ForegroundColor Gray
        
        # 2. 收集所有节点信息
        Write-Host "`n2. 收集所有节点信息..." -ForegroundColor White
        $allNodes = @()
        function Collect-AllNodes {
            param($nodes)
            foreach ($node in $nodes) {
                $allNodes += $node
                if ($node.children -and $node.children.Count -gt 0) {
                    Collect-AllNodes -nodes $node.children
                }
            }
        }
        Collect-AllNodes -nodes $bomTree
        
        Write-Host "   总节点数: $($allNodes.Count)" -ForegroundColor Gray
        
        # 3. 显示每个节点的详细信息
        Write-Host "`n3. 节点详细信息..." -ForegroundColor White
        foreach ($node in $allNodes) {
            Write-Host "   节点ID: $($node.id)" -ForegroundColor Yellow
            Write-Host "   BOM ID: $($node.bomId)" -ForegroundColor Yellow
            Write-Host "   父节点ID: $($node.parentItemId)" -ForegroundColor Yellow
            Write-Host "   显示名称: $($node.displayName)" -ForegroundColor Yellow
            Write-Host "   层级: $($node.level)" -ForegroundColor Yellow
            Write-Host "   子节点数: $($node.children.Count)" -ForegroundColor Yellow
            Write-Host "   可展开: $($node.isExpandable)" -ForegroundColor Yellow
            Write-Host ""
        }
        
        # 4. 分析父子关系
        Write-Host "`n4. 分析父子关系..." -ForegroundColor White
        $rootNodes = @()
        $childNodes = @()
        
        foreach ($node in $allNodes) {
            if ([string]::IsNullOrEmpty($node.parentItemId)) {
                $rootNodes += $node
            } else {
                $childNodes += $node
            }
        }
        
        Write-Host "   根节点数: $($rootNodes.Count)" -ForegroundColor Gray
        Write-Host "   子节点数: $($childNodes.Count)" -ForegroundColor Gray
        
        foreach ($root in $rootNodes) {
            Write-Host "   根节点: $($root.id) - $($root.displayName)" -ForegroundColor Cyan
        }
        
        foreach ($child in $childNodes) {
            Write-Host "   子节点: $($child.id) - $($child.displayName) (父节点: $($child.parentItemId))" -ForegroundColor Magenta
        }
        
        # 5. 显示树形结构
        Write-Host "`n5. 显示树形结构..." -ForegroundColor White
        function Show-TreeStructure {
            param($nodes, $level = 0)
            foreach ($node in $nodes) {
                $indent = "  " * $level
                $expandIcon = if ($node.isExpandable) { "▼" } else { "  " }
                Write-Host "$indent$expandIcon [$($node.sequence)] $($node.displayName)" -ForegroundColor Gray
                Write-Host "$indent    节点ID: $($node.id)" -ForegroundColor DarkGray
                Write-Host "$indent    BOM ID: $($node.bomId)" -ForegroundColor DarkGray
                Write-Host "$indent    父节点ID: $($node.parentItemId)" -ForegroundColor DarkGray
                Write-Host "$indent    层级: $($node.level)" -ForegroundColor DarkGray
                
                if ($node.children -and $node.children.Count -gt 0) {
                    Show-TreeStructure -nodes $node.children -level ($level + 1)
                }
            }
        }
        
        Show-TreeStructure -nodes $bomTree
        
    } else {
        Write-Host "   获取失败: $($response.msg)" -ForegroundColor Red
    }

    Write-Host "`n=== 测试完成 ===" -ForegroundColor Green

} catch {
    Write-Host "测试过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误信息: $($_.Exception)" -ForegroundColor Red
} 