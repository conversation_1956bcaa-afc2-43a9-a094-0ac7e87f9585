using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.Process
{
    /// <summary>
    /// 工序组成数据传输对象
    /// </summary>
    public class ProcessCompositionDto
    {
        /// <summary>
        /// 工艺路线Id（必填）
        /// </summary>
        [Required(ErrorMessage = "工艺路线ID不能为空")]
        public Guid ProcessRouteId { get; set; }

        /// <summary>
        /// 工序Id（必填）
        /// </summary>
        [Required(ErrorMessage = "工序ID不能为空")]
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 工序顺序号（必填，用于确定工序在工艺路线中的执行顺序）
        /// </summary>
        [Required(ErrorMessage = "工序顺序号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "工序顺序号必须大于0")]
        public int StepOrder { get; set; }

        /// <summary>
        /// 版本号（默认1.0）
        /// </summary>
        [Required(ErrorMessage = "版本号不能为空")]
        [StringLength(50, ErrorMessage = "版本号长度不能超过50个字符")]
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 是否当前有效版本（默认为true）
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 备注（可选）
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 工序组成列表显示DTO
    /// </summary>
    public class ProcessCompositionListDto
    {
        /// <summary>
        /// 工序组成ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public Guid ProcessRouteId { get; set; }

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        public string ProcessRouteName { get; set; } = string.Empty;

        /// <summary>
        /// 工艺路线编号
        /// </summary>
        public string ProcessRouteNumber { get; set; } = string.Empty;

        /// <summary>
        /// 工序ID
        /// </summary>
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        public string ProcessStepName { get; set; } = string.Empty;

        /// <summary>
        /// 工序编号（课题编号，自动使用工序的编号）
        /// </summary>
        public string ProcessStepNumber { get; set; } = string.Empty;

        /// <summary>
        /// 工序顺序号
        /// </summary>
        public int StepOrder { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 是否当前有效版本
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}