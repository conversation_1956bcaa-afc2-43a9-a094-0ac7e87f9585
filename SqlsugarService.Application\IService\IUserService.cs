﻿using SqlsugarService.Application.Until;
using SqlsugarService.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService
{
    public interface IUserService
    {
        Task<ApiResult<List<Users>>> GetUsers();
        Task<ApiResult<PageResult<List<Users>>>> GetUserspage(Seach? seach);
        Task<ApiResult> AddUser(Users user);

    }
}
