﻿using SqlsugarService.Application.DTOs.ProductionOrderDto;
using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlsugarService.Application.DTOs.Process;

namespace SqlsugarService.Application.IService.ProductionOrders
{
    public interface IProductionOrderService
    {
        /// <summary>
        /// 获取生产工单分页列表
        /// </summary>
        /// <param name="search">搜索条件</param>
        /// <returns>分页结果</returns>
        Task<ApiResult<PageResult<List<GetProductionOrderDto>>>> GetProductionOrderList(GetProductionOrderSearchDto search);

        /// <summary>
        /// 根据生产工单列表获取物料信息
        /// </summary>
        /// <param name="search">搜索条件</param>
        /// <returns>包含物料信息的生产工单列表</returns>
        Task<ApiResult<PageResult<List<GetProductionOrderWithMaterialsDto>>>> GetProductionOrderListWithMaterials(GetProductionOrderSearchDto search);

        /// <summary>
        /// 获取生产工单的物料清单
        /// </summary>
        /// <param name="productionOrderId">生产工单ID</param>
        /// <returns>物料清单信息</returns>
        Task<ApiResult<MaterialListResultDto>> GetProductionOrderMaterialList(Guid productionOrderId);

        /// <summary>
        /// 根据生产工单ID获取工艺路线信息
        /// </summary>
        /// <param name="productionOrderId">生产工单ID</param>
        /// <param name="includeDeleted">是否包含已删除的工单，默认true</param>
        /// <returns>工艺路线信息</returns>
        Task<ApiResult<GetRoutingDto>> GetProductionOrderRouting(Guid productionOrderId, bool includeDeleted = true);

        /// <summary>
        /// 根据生产工单ID获取工序信息
        /// </summary>
        /// <param name="productionOrderId">生产工单ID</param>
        /// <param name="includeDeleted">是否包含已删除的工单，默认true</param>
        /// <returns>工序信息列表</returns>
        Task<ApiResult<List<GetProcessDto>>> GetProductionOrderProcesses(Guid productionOrderId, bool includeDeleted = true);

        /// <summary>
        /// 批量新增工单任务
        /// </summary>
        /// <param name="dto">批量工单任务信息</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> BatchAddWorkOrderTasks(BatchAddWorkOrderTaskDto dto);

        /// <summary>
        /// 修改生产工单状态
        /// </summary>
        /// <param name="dto">修改生产工单状态信息</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> UpdateProductionOrderStatus(UpdateProductionOrderStatusDto dto);
    }
}
