# 测试BOM分解功能的PowerShell脚本
# 用于演示如何将生产计划的BOM树形分解成多个生产工单，包括软删除功能

param(
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== 测试BOM分解功能（包含软删除） ===" -ForegroundColor Green

# 测试函数
function Test-BomDecomposition {
    param(
        [string]$Url,
        [string]$Method,
        [string]$Description,
        [object]$Body = $null
    )
    
    Write-Host "`n测试: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    Write-Host "方法: $Method" -ForegroundColor Gray
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Method -eq "POST") {
            $response = Invoke-RestMethod -Uri $Url -Method Post -Headers $headers -Body ($Body | ConvertTo-Json -Depth 10) -TimeoutSec 60
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method Get -Headers $headers -TimeoutSec 30
        }
        
        if ($response.isSuc) {
            Write-Host "✅ 成功" -ForegroundColor Green
            
            if ($response.data) {
                Show-DecompositionResult $response.data
            } else {
                Write-Host "返回数据: $($response.message)" -ForegroundColor Cyan
            }
        } else {
            Write-Host "❌ 失败: $($response.message)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示分解结果
function Show-DecompositionResult {
    param(
        [object]$Data
    )
    
    Write-Host "分解结果分析：" -ForegroundColor Cyan
    
    if ($Data.TotalOrders) {
        Write-Host "总工单数: $($Data.TotalOrders)" -ForegroundColor White
    }
    
    if ($Data.Orders -and $Data.Orders.Count -gt 0) {
        Write-Host "生成的工单：" -ForegroundColor Yellow
        foreach ($order in $Data.Orders) {
            Write-Host "  - $($order.OrderName) (编号: $($order.OrderNumber))" -ForegroundColor White
            Write-Host "    产品: $($order.ProductName), 数量: $($order.PlanQuantity) $($order.Unit)" -ForegroundColor Gray
            Write-Host "    计划时间: $($order.PlanStartTime) - $($order.PlanEndTime)" -ForegroundColor Gray
            if ($order.IsDeleted) {
                Write-Host "    删除时间: $($order.DeletedAt), 删除者: $($order.DeletedBy)" -ForegroundColor Red
            }
        }
    }
    
    if ($Data.DecompositionItems -and $Data.DecompositionItems.Count -gt 0) {
        Write-Host "分解项目：" -ForegroundColor Yellow
        foreach ($item in $Data.DecompositionItems) {
            $type = if ($item.IsProduct) { "产品" } else { "物料" }
            Write-Host "  - $($item.MaterialName) (类型: $type, 数量: $($item.RequiredQuantity) $($item.Unit))" -ForegroundColor White
        }
    }
    
    if ($Data.DecompositionTime) {
        Write-Host "分解时间: $($Data.DecompositionTime)" -ForegroundColor Cyan
    }
    
    if ($Data.UndoTime) {
        Write-Host "撤销时间: $($Data.UndoTime)" -ForegroundColor Cyan
    }
}

# 显示预览结果
function Show-PreviewResult {
    param(
        [object]$Data
    )
    
    Write-Host "预览结果分析：" -ForegroundColor Cyan
    Write-Host "生产计划: $($Data.ProductionPlanName)" -ForegroundColor White
    Write-Host "当前状态: $($Data.CurrentStatus)" -ForegroundColor White
    Write-Host "可以分解: $($Data.CanDecompose)" -ForegroundColor White
    Write-Host "预估工单数: $($Data.EstimatedOrders)" -ForegroundColor White
    
    if ($Data.DecompositionItems -and $Data.DecompositionItems.Count -gt 0) {
        Write-Host "分解项目：" -ForegroundColor Yellow
        foreach ($item in $Data.DecompositionItems) {
            $type = if ($item.IsProduct) { "产品" } else { "物料" }
            Write-Host "  - $($item.MaterialName) (类型: $type, 数量: $($item.RequiredQuantity) $($item.Unit))" -ForegroundColor White
        }
    }
}

# 显示已删除工单列表
function Show-DeletedOrders {
    param(
        [object]$Data
    )
    
    Write-Host "已删除工单列表：" -ForegroundColor Cyan
    
    if ($Data -and $Data.Count -gt 0) {
        foreach ($order in $Data) {
            Write-Host "  - $($order.OrderName) (编号: $($order.OrderNumber))" -ForegroundColor White
            Write-Host "    产品: $($order.ProductName), 数量: $($order.PlanQuantity) $($order.Unit)" -ForegroundColor Gray
            Write-Host "    删除时间: $($order.DeletedAt), 删除者: $($order.DeletedBy)" -ForegroundColor Red
        }
    } else {
        Write-Host "  没有已删除的工单" -ForegroundColor Gray
    }
}

# 测试各个接口
try {
    Write-Host "=== BOM分解功能测试（包含软删除） ===" -ForegroundColor Cyan
    
    # 使用测试BOM ID
    $testBomId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"
    
    # 1. 预览分解结果
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/preview/$testBomId" -Method "GET" -Description "预览BOM分解结果"
    
    # 2. 获取分解状态
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/status/$testBomId" -Method "GET" -Description "获取分解状态"
    
    # 3. 执行BOM分解
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/decompose/$testBomId" -Method "POST" -Description "执行BOM分解"
    
    # 4. 再次获取分解状态
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/status/$testBomId" -Method "GET" -Description "分解后获取状态"
    
    # 5. 查询已删除的工单（此时应该没有）
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/deleted-orders/$testBomId" -Method "GET" -Description "查询已删除的工单"
    
    # 6. 撤销分解（软删除）
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/undo/$testBomId" -Method "POST" -Description "撤销BOM分解（软删除）"
    
    # 7. 查询已删除的工单（此时应该有）
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/deleted-orders/$testBomId" -Method "GET" -Description "查询已删除的工单"
    
    # 8. 恢复已删除的工单
    $restoreBody = @{
        OrderIds = @("order-id-1", "order-id-2")  # 这里需要实际的工单ID
    }
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/restore/$testBomId" -Method "POST" -Description "恢复已删除的工单" -Body $restoreBody
    
    # 9. 永久删除工单（硬删除）
    $deleteBody = @{
        OrderIds = @("order-id-1", "order-id-2")  # 这里需要实际的工单ID
    }
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/permanently-delete/$testBomId" -Method "POST" -Description "永久删除工单" -Body $deleteBody
    
    # 10. 撤销后获取状态
    Test-BomDecomposition -Url "$BaseUrl/api/BomDecomposition/status/$testBomId" -Method "GET" -Description "撤销后获取状态"
    
} catch {
    Write-Host "测试过程中发生错误：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== BOM分解功能测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "功能说明：" -ForegroundColor Cyan
Write-Host "1. 预览分解：在正式分解前预览分解结果" -ForegroundColor White
Write-Host "2. 状态检查：检查生产计划当前状态和可执行操作" -ForegroundColor White
Write-Host "3. 执行分解：将BOM树形分解为生产工单" -ForegroundColor White
Write-Host "4. 软删除撤销：撤销已分解的工单（软删除）" -ForegroundColor White
Write-Host "5. 查询已删除工单：查看已软删除的工单列表" -ForegroundColor White
Write-Host "6. 恢复工单：恢复已软删除的工单" -ForegroundColor White
Write-Host "7. 永久删除：永久删除已软删除的工单" -ForegroundColor White
Write-Host ""
Write-Host "状态控制：" -ForegroundColor Yellow
Write-Host "1. 未分解状态(0)：可以执行分解操作" -ForegroundColor White
Write-Host "2. 已分解状态(1)：可以执行撤销操作" -ForegroundColor White
Write-Host "3. 其他状态：不允许分解或撤销" -ForegroundColor White
Write-Host ""
Write-Host "软删除特性：" -ForegroundColor Yellow
Write-Host "1. 软删除标记：使用IsDeleted字段标记删除状态" -ForegroundColor White
Write-Host "2. 删除信息记录：记录删除时间和删除者" -ForegroundColor White
Write-Host "3. 数据保留：软删除的数据仍然保留在数据库中" -ForegroundColor White
Write-Host "4. 恢复功能：可以恢复已软删除的工单" -ForegroundColor White
Write-Host "5. 永久删除：支持永久删除已软删除的工单" -ForegroundColor White
Write-Host ""
Write-Host "分解策略：" -ForegroundColor Yellow
Write-Host "1. 按产品物料分解：识别MaterialType为Product的物料" -ForegroundColor White
Write-Host "2. 数量计算：根据BOM用量和计划数量计算实际需求" -ForegroundColor White
Write-Host "3. 时间安排：按层级和数量安排工单时间" -ForegroundColor White
Write-Host "4. 状态更新：分解后更新生产计划状态" -ForegroundColor White
Write-Host ""
Write-Host "安全控制：" -ForegroundColor Yellow
Write-Host "1. 状态验证：只有特定状态才能执行相应操作" -ForegroundColor White
Write-Host "2. 工单状态检查：只有未开始的工单才能撤销" -ForegroundColor White
Write-Host "3. 数据完整性：确保分解和撤销操作的数据一致性" -ForegroundColor White
Write-Host "4. 软删除保护：防止误删除，支持数据恢复" -ForegroundColor White 