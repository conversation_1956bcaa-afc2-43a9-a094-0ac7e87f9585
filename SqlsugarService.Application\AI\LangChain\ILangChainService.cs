using SqlsugarService.Application.AI.LangChain.Dtos;

namespace SqlsugarService.Application.AI.LangChain
{
    /// <summary>
    /// LangChain服务接口
    /// 
    /// 提供基于LangChain.NET的AI交互能力，包括：
    /// - 基础对话功能
    /// - 对话历史记忆
    /// - 链式工具调用
    /// - 自定义提示词模板
    /// </summary>
    public interface ILangChainService
    {
        /// <summary>
        /// 发送消息到LangChain并获取回复
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>AI回复内容</returns>
        Task<LangChainResponseDto> SendMessageAsync(LangChainRequestDto request);

        /// <summary>
        /// 快速发送消息（使用默认配置）
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <param name="userId">用户ID，可选</param>
        /// <returns>AI回复内容</returns>
        Task<LangChainResponseDto> QuickSendAsync(string content, string? userId = null);

        /// <summary>
        /// 发送带工具定义的消息，支持AI调用工具
        /// </summary>
        /// <param name="request">带工具定义的请求参数</param>
        /// <returns>AI回复内容，可能包含工具调用</returns>
        Task<LangChainToolResponseDto> SendMessageWithToolsAsync(LangChainToolRequestDto request);

        /// <summary>
        /// 提交工具执行结果并继续对话
        /// </summary>
        /// <param name="request">工具执行结果</param>
        /// <returns>AI的后续回复</returns>
        Task<LangChainToolResponseDto> SubmitToolResultsAsync(ToolResultsRequest request);

        /// <summary>
        /// 清除指定用户的会话历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="sessionId">会话ID，如果为null则清除该用户的所有会话</param>
        /// <returns>操作是否成功</returns>
        Task<bool> ClearMemoryAsync(string userId, string? sessionId = null);

        /// <summary>
        /// 健康检查
        /// </summary>
        /// <returns>服务是否正常</returns>
        Task<bool> HealthCheckAsync();
    }
} 