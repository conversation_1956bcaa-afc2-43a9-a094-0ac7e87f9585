# 测试修复后的BOM树形功能
# 用于验证BOM树形下拉列表是否正确返回完整的树形结构

param(
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== 测试修复后的BOM树形功能 ===" -ForegroundColor Green

# 测试函数
function Test-BomTree {
    param(
        [string]$Url,
        [string]$Description
    )
    
    Write-Host "`n测试: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method Get -ContentType "application/json" -TimeoutSec 30
        
        if ($response.isSuc) {
            Write-Host "✅ 成功" -ForegroundColor Green
            
            if ($response.data -and $response.data.Count -gt 0) {
                Write-Host "返回数据数量: $($response.data.Count)" -ForegroundColor Cyan
                
                # 分析树形结构
                AnalyzeTreeStructure $response.data
                
                # 统计节点信息
                $totalNodes = CountTotalNodes $response.data
                Write-Host "总节点数: $totalNodes" -ForegroundColor Cyan
                
            } else {
                Write-Host "返回空数据" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ 失败: $($response.message)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 分析树形结构
function AnalyzeTreeStructure {
    param(
        [array]$Nodes,
        [int]$Level = 0
    )
    
    foreach ($node in $Nodes) {
        $indent = "  " * $Level
        $nodeInfo = "$($node.name)"
        
        # 添加详细信息
        $details = @()
        if ($node.nodeType) { $details += "类型: $($node.nodeType)" }
        if ($node.quantity) { $details += "用量: $($node.quantity) $($node.unit)" }
        if ($node.bomNumber) { $details += "BOM: $($node.bomNumber)" }
        
        $nodeInfo += " ($($details -join ', '))"
        
        if ($node.children -and $node.children.Count -gt 0) {
            $nodeInfo += " [子节点: $($node.children.Count)]"
            Write-Host "$indent- $nodeInfo" -ForegroundColor Cyan
            
            # 递归显示子节点
            AnalyzeTreeStructure $node.children ($Level + 1)
        } else {
            Write-Host "$indent- $nodeInfo [无子节点]" -ForegroundColor White
        }
    }
}

# 统计总节点数
function CountTotalNodes {
    param(
        [array]$Nodes
    )
    
    $count = 0
    foreach ($node in $Nodes) {
        $count++
        if ($node.children -and $node.children.Count -gt 0) {
            $count += CountTotalNodes $node.children
        }
    }
    return $count
}

# 测试各个接口
try {
    Write-Host "=== 测试BOM树形功能 ===" -ForegroundColor Cyan
    
    # 1. 测试获取所有BOM树形
    Test-BomTree -Url "$BaseUrl/api/ProductionPlans/bom-tree" -Description "获取所有BOM树形"
    
    # 2. 测试根据BOM ID获取树形
    Test-BomTree -Url "$BaseUrl/api/ProductionPlans/bom-tree/9690a4a8-168d-4a11-866c-c280330e107b" -Description "根据BOM ID获取树形"
    
    # 3. 测试数据检查
    Test-BomTree -Url "$BaseUrl/api/ProductionPlans/check-bom-data" -Description "检查BOM数据"
    
} catch {
    Write-Host "测试过程中发生错误：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "修复说明：" -ForegroundColor Cyan
Write-Host "1. 修改了GetBomTreeDropdownSimple方法，现在会返回完整的树形结构" -ForegroundColor White
Write-Host "2. 新增了BuildCompleteBomTree和BuildCompleteTreeNode方法" -ForegroundColor White
Write-Host "3. 支持子BOM的递归展开" -ForegroundColor White
Write-Host "4. 包含所有层级的节点信息" -ForegroundColor White
Write-Host ""
Write-Host "预期结果：" -ForegroundColor Yellow
Write-Host "1. 应该返回多个节点，而不是只有一个根节点" -ForegroundColor White
Write-Host "2. 每个节点都应该包含完整的子节点信息" -ForegroundColor White
Write-Host "3. 子BOM应该正确展开显示" -ForegroundColor White 