# 测试扣子AI修复后的响应解析
# 这个脚本直接测试修复后的API，验证是否能正确解析扣子AI的响应

Write-Host "🔧 测试扣子AI响应解析修复" -ForegroundColor Yellow
Write-Host "=" * 50

# 配置
$baseUrl = "http://localhost:5000"
$testMessage = "你是一个有用的AI助手，专注于帮助用户解决问题。基于扣子AI提供智能服务。当前用户问题：我们是一家汽车零部件制造企业，想了解MES系统能为我们带来什么价值"

Write-Host "`n📋 1. 检查服务状态..." -ForegroundColor Yellow
try {
    $healthCheck = Invoke-RestMethod -Uri "$baseUrl/swagger" -Method Get -TimeoutSec 5
    Write-Host "✅ 服务运行正常!" -ForegroundColor Green
} catch {
    Write-Host "❌ 服务未启动或无法访问: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 请先启动服务: dotnet run --project SqlsugarService.API --urls `"http://localhost:5000`"" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n📋 2. 测试快速发送接口..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/quick-send" -Method Post -ContentType "application/json" -Body (ConvertTo-Json $testMessage) -TimeoutSec 60
    
    Write-Host "✅ 请求成功!" -ForegroundColor Green
    Write-Host "📝 AI回复内容:" -ForegroundColor Cyan
    Write-Host $response.content -ForegroundColor White
    Write-Host "`n🆔 会话ID: $($response.sessionId)" -ForegroundColor Magenta
    Write-Host "⏰ 创建时间: $($response.createdAt)" -ForegroundColor Gray
    
    if ($response.content -and $response.content.Length -gt 10) {
        Write-Host "`n🎉 修复成功! 扣子AI响应解析正常工作!" -ForegroundColor Green
    } else {
        Write-Host "`n⚠️  响应内容为空或过短，可能仍有问题" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Message -like "*无法连接*") {
        Write-Host "💡 提示：服务可能未启动或端口不正确" -ForegroundColor Yellow
    } elseif ($_.Exception.Message -like "*超时*") {
        Write-Host "💡 提示：请求超时，可能是扣子AI响应较慢" -ForegroundColor Yellow
    } else {
        Write-Host "💡 提示：检查服务日志获取详细错误信息" -ForegroundColor Yellow
    }
}

Write-Host "`n📋 3. 测试扣子AI直接接口..." -ForegroundColor Yellow
try {
    $kouziResponse = Invoke-RestMethod -Uri "$baseUrl/api/KouziAI/quick-send" -Method Post -ContentType "application/json" -Body (ConvertTo-Json $testMessage) -TimeoutSec 60
    
    Write-Host "✅ 扣子AI直接接口成功!" -ForegroundColor Green
    Write-Host "📝 扣子AI回复:" -ForegroundColor Cyan
    Write-Host $kouziResponse.content -ForegroundColor White
    
} catch {
    Write-Host "❌ 扣子AI直接接口失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 测试完成！" -ForegroundColor Green
Write-Host "💡 如果看到有效的AI回复内容，说明修复成功！" -ForegroundColor Yellow
