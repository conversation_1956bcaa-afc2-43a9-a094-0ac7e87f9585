using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Materials;
using System;

namespace SqlsugarService.Domain.Craftsmanship
{
    /// <summary>
    /// 工序物料详情表
    /// 用于存储某个工序中的所有产品/物料的详细信息
    /// </summary>
    public class ProcessStepMaterialDetail : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 工序Id
        /// </summary>
        public Guid ProcessStepId { get; set; }
        
        /// <summary>
        /// 工序导航属性
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public ProcessStep? ProcessStep { get; set; }

        /// <summary>
        /// 物料Id
        /// </summary>
        public Guid MaterialId { get; set; }
        
        /// <summary>
        /// 物料导航属性
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public MaterialEntity? Material { get; set; }

        /// <summary>
        /// 序号（在工序中的排序）
        /// </summary>
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 物料编号（冗余字段，便于查询显示）
        /// </summary>
        public string MaterialNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物料名称（冗余字段，便于查询显示）
        /// </summary>
        public string MaterialName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 使用量/需求量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 用料比例（百分比，如20表示20%）
        /// </summary>
        public decimal UsageRatio { get; set; }

        /// <summary>
        /// 投入产出类型
        /// </summary>
        public MaterialIOType IOType { get; set; }

        /// <summary>
        /// 损耗率（百分比）
        /// </summary>
        public decimal LossRate { get; set; } = 0;

        /// <summary>
        /// 是否必需物料
        /// </summary>
        public bool IsRequired { get; set; } = true;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 备注说明
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 版本号（用于版本管理）
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 是否当前有效版本
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 物料投入产出类型枚举
    /// </summary>
    public enum MaterialIOType
    {
        /// <summary>
        /// 投入物料（原料、半成品等）
        /// </summary>
        Input = 1,

        /// <summary>
        /// 产出物料（成品、半成品等）
        /// </summary>
        Output = 2,

        /// <summary>
        /// 副产品
        /// </summary>
        ByProduct = 3,

        /// <summary>
        /// 废料
        /// </summary>
        Scrap = 4,

        /// <summary>
        /// 可回收物料
        /// </summary>
        Recyclable = 5
    }
}