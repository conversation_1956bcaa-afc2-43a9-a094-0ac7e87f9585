# BOM数据关联诊断指南

## 问题描述

BOM树形查询方法没有问题，但是联查没有数据，而产品、物料、BomInfo表中都有数据。

## 可能的原因

### 1. 外键关联问题
- BomItem表中的BomId和MaterialId可能为空或无效
- GUID格式不匹配
- 数据类型不一致

### 2. 数据匹配问题
- BomItem.BomId与BomInfo.Id不匹配
- BomItem.MaterialId与MaterialEntity.Id不匹配
- 存在软删除数据

### 3. 查询条件问题
- 查询条件过于严格
- 过滤条件错误

## 诊断步骤

### 步骤1：使用诊断接口

访问诊断端点：
```
GET /api/ProductionPlans/diagnose-bom-data
```

这个接口会返回：
- BomItem表的前5条数据
- BomInfo表的前5条数据
- MaterialEntity表的前5条数据
- 关联匹配结果

### 步骤2：检查诊断结果

查看返回的JSON数据，重点关注：

```json
{
  "bomItemsCount": 5,
  "bomInfosCount": 5,
  "materialsCount": 5,
  "bomItems": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "bomId": "550e8400-e29b-41d4-a716-************",
      "materialId": "550e8400-e29b-41d4-a716-************",
      "parentItemId": null,
      "quantity": 50,
      "unit": "个",
      "inOutType": 0
    }
  ],
  "bomIdsInBomItems": ["550e8400-e29b-41d4-a716-************"],
  "materialIdsInBomItems": ["550e8400-e29b-41d4-a716-************"],
  "matchedBomInfosCount": 1,
  "matchedMaterialsCount": 1
}
```

### 步骤3：分析问题

#### 情况1：BomItemsCount为0
**问题**：BomItem表没有数据
**解决方案**：检查BomItem表是否有数据

#### 情况2：BomIdsInBomItems为空
**问题**：BomItem表中的BomId都为空
**解决方案**：检查BomItem数据插入时是否正确设置了BomId

#### 情况3：MaterialIdsInBomItems为空
**问题**：BomItem表中的MaterialId都为空
**解决方案**：检查BomItem数据插入时是否正确设置了MaterialId

#### 情况4：matchedBomInfosCount为0
**问题**：BomItem中的BomId在BomInfo表中找不到对应记录
**解决方案**：检查BomInfo表是否有对应的ID

#### 情况5：matchedMaterialsCount为0
**问题**：BomItem中的MaterialId在MaterialEntity表中找不到对应记录
**解决方案**：检查MaterialEntity表是否有对应的ID

## 手动SQL查询验证

### 1. 检查BomItem表数据
```sql
SELECT 
    id,
    bom_id,
    material_id,
    parent_item_id,
    quantity,
    unit,
    in_out_type
FROM bom_item 
LIMIT 5;
```

### 2. 检查BomInfo表数据
```sql
SELECT 
    id,
    bom_number,
    product_name,
    version
FROM bom_info 
LIMIT 5;
```

### 3. 检查MaterialEntity表数据
```sql
SELECT 
    id,
    material_name,
    material_number,
    specification_model
FROM material_entity 
LIMIT 5;
```

### 4. 检查关联关系
```sql
-- 检查BomItem中的BomId是否在BomInfo中存在
SELECT 
    bi.id as bom_item_id,
    bi.bom_id,
    binfo.id as bom_info_id,
    binfo.bom_number
FROM bom_item bi
LEFT JOIN bom_info binfo ON bi.bom_id = binfo.id
WHERE bi.bom_id IS NOT NULL
LIMIT 10;

-- 检查BomItem中的MaterialId是否在MaterialEntity中存在
SELECT 
    bi.id as bom_item_id,
    bi.material_id,
    me.id as material_entity_id,
    me.material_name
FROM bom_item bi
LEFT JOIN material_entity me ON bi.material_id = me.id
WHERE bi.material_id IS NOT NULL
LIMIT 10;
```

## 常见问题及解决方案

### 问题1：BomId为空
**原因**：插入BomItem时没有设置BomId
**解决方案**：
```csharp
// 确保插入时设置正确的BomId
var bomItem = new BomItem
{
    Id = Guid.NewGuid(),
    BomId = bomInfoId, // 确保这个值不为空
    MaterialId = materialId,
    Quantity = 50,
    Unit = "个"
};
```

### 问题2：MaterialId为空
**原因**：插入BomItem时没有设置MaterialId
**解决方案**：
```csharp
// 确保插入时设置正确的MaterialId
var bomItem = new BomItem
{
    Id = Guid.NewGuid(),
    BomId = bomInfoId,
    MaterialId = materialId, // 确保这个值不为空
    Quantity = 50,
    Unit = "个"
};
```

### 问题3：GUID格式不匹配
**原因**：数据库中的GUID格式与代码中的不匹配
**解决方案**：
```csharp
// 确保GUID格式一致
var bomId = Guid.Parse("550e8400-e29b-41d4-a716-************");
```

### 问题4：软删除数据
**原因**：相关表中有软删除的数据
**解决方案**：
```sql
-- 检查软删除的数据
SELECT * FROM bom_info WHERE is_deleted = true;
SELECT * FROM material_entity WHERE is_deleted = true;
```

## 测试步骤

### 1. 运行诊断接口
```bash
curl http://localhost:5000/api/ProductionPlans/diagnose-bom-data
```

### 2. 分析诊断结果
根据返回的数据分析问题所在

### 3. 修复数据问题
根据分析结果修复数据关联问题

### 4. 重新测试
```bash
curl http://localhost:5000/api/ProductionPlans/bom-tree-simple
```

## 预防措施

1. **数据插入时验证**：确保插入BomItem时BomId和MaterialId不为空
2. **外键约束**：在数据库层面添加外键约束
3. **数据完整性检查**：定期检查数据关联完整性
4. **单元测试**：为数据插入和查询添加单元测试

## 联系支持

如果问题仍然存在，请提供：
1. 诊断接口的完整返回结果
2. 相关表的SQL查询结果
3. 数据插入的代码示例
4. 错误日志信息 