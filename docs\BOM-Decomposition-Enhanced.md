# 增强版BOM分解功能说明

## 📋 **概述**

本文档说明了完善后的生产计划分解功能，该功能通过调用 `GetBomTreeDropdownByBomId` 方法来获取BOM树形结构，并根据产品信息生成对应的生产工单。

## 🎯 **核心改进**

### 1. **使用GetBomTreeDropdownByBomId方法**
- 替换了原有的BOM树构建逻辑
- 直接使用 `ProductionPlanService` 中的 `GetBomTreeDropdownByBomId` 方法
- 确保BOM树形结构的一致性

### 2. **智能产品识别**
- 根据BOM编号和用量判断是否为产品节点
- 只有有BOM编号且用量大于0的节点才会生成生产工单
- 支持递归处理子BOM结构

### 3. **物料ID处理**
- 尝试从产品编号解析物料ID
- 如果产品编号是有效的GUID，直接使用
- 否则生成新的GUID（临时方案）

## 🔧 **实现细节**

### 1. **依赖注入配置**
```csharp
public BomDecompositionService(
    IBaseRepository<ProductionPlan> productionPlanRepository,
    IBaseRepository<ProductionOrder> productionOrderRepository,
    IBaseRepository<BomItem> bomItemRepository,
    IBaseRepository<BomInfo> bomInfoRepository,
    ISqlSugarClient db,
    IProductionPlanService productionPlanService) // 新增依赖
{
    // ... 初始化代码
    _productionPlanService = productionPlanService;
}
```

### 2. **分解流程**
```csharp
public async Task<ApiResult<BomDecompositionResult>> DecomposeProductionPlan(Guid productionPlanId)
{
    // 1. 验证生产计划状态
    var validationResult = await ValidateProductionPlanForDecomposition(productionPlanId);
    
    // 2. 通过BomId获取BOM树形结构（使用GetBomTreeDropdownByBomId方法）
    var bomTreeResult = await _productionPlanService.GetBomTreeDropdownByBomId(productionPlan.BomId);
    
    // 3. 分析BOM树形结构，识别需要生成工单的产品
    var decompositionItems = AnalyzeBomTreeForDecomposition(bomTreeResult.Data, productionPlan.PlanQuantity);
    
    // 4. 生成生产工单
    var productionOrders = await GenerateProductionOrdersFromBomTree(decompositionItems, productionPlan);
    
    // 5. 计算工单时间安排
    var scheduledOrders = CalculateWorkOrderSchedule(productionOrders, productionPlan);
    
    // 6. 保存生产工单
    var savedOrders = await SaveProductionOrders(scheduledOrders);
    
    // 7. 更新生产计划状态为已分解
    await UpdateProductionPlanStatus(productionPlanId, 1);
    
    return ApiResult<BomDecompositionResult>.Success(result, ResultCode.Success);
}
```

### 3. **产品识别逻辑**
```csharp
private void AnalyzeBomTreeNodeForDecomposition(BomTreeDto node, decimal parentQuantity, List<DecompositionItem> decompositionItems)
{
    // 计算当前节点的实际数量
    var actualQuantity = node.UsageQuantity * parentQuantity;

    // 判断是否为产品（根据节点类型和BOM编号判断）
    // 如果节点有BOM编号，说明这是一个产品节点，需要生成生产工单
    var isProduct = !string.IsNullOrEmpty(node.BomNumber) && node.UsageQuantity > 0;

    if (isProduct)
    {
        // 尝试从产品编号获取物料ID
        var materialId = GetMaterialIdFromProductNumber(node.ProductNumber);
        
        var decompositionItem = new DecompositionItem
        {
            MaterialId = materialId,
            MaterialName = node.ProductName,
            MaterialNumber = node.ProductNumber,
            RequiredQuantity = actualQuantity,
            Unit = node.Unit,
            IsProduct = true,
            MaterialType = MaterialTypeEnum.Product
        };

        decompositionItems.Add(decompositionItem);
    }

    // 递归分析子节点
    foreach (var child in node.Children)
    {
        AnalyzeBomTreeNodeForDecomposition(child, actualQuantity, decompositionItems);
    }
}
```

## 📊 **数据结构**

### 1. **BomTreeDto结构**
```csharp
public class BomTreeDto
{
    public int Sequence { get; set; }
    public string ProductName { get; set; }
    public string ProductNumber { get; set; }
    public string Specification { get; set; }
    public string Unit { get; set; }
    public string BomNumber { get; set; }
    public string BomVersion { get; set; }
    public decimal UsageQuantity { get; set; }
    public string UsageRatio { get; set; }
    public bool IsExpandable { get; set; }
    public List<BomTreeDto> Children { get; set; }
}
```

### 2. **DecompositionItem结构**
```csharp
public class DecompositionItem
{
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; }
    public string MaterialNumber { get; set; }
    public decimal RequiredQuantity { get; set; }
    public string Unit { get; set; }
    public int Level { get; set; }
    public bool IsProduct { get; set; }
    public string ParentNodeId { get; set; }
    public MaterialTypeEnum MaterialType { get; set; }
}
```

## 🚀 **使用示例**

### 1. **API调用**
```bash
# 预览分解结果
GET /api/BomDecomposition/preview/{productionPlanId}

# 执行分解
POST /api/BomDecomposition/decompose/{productionPlanId}

# 获取分解详情
GET /api/BomDecomposition/detail/{productionPlanId}
```

### 2. **测试脚本**
```powershell
# 运行增强版测试脚本
.\scripts\test-bom-decomposition-enhanced.ps1 -ProductionPlanId "your-plan-id"
```

## 🔍 **关键特性**

### 1. **状态控制**
- 只有未分解状态(0)的生产计划才能分解
- 分解成功后状态更新为已分解(1)
- 支持撤销操作，将状态恢复为未分解(0)

### 2. **数据完整性**
- 每个产品节点生成一个生产工单
- 工单包含完整的产品信息和数量
- 支持软删除和恢复功能

### 3. **时间安排**
- 自动计算工单的时间安排
- 按比例分配生产计划的时间
- 确保工单按时完成

### 4. **错误处理**
- 完整的异常处理机制
- 详细的错误信息提示
- 操作失败时的数据回滚

## 📈 **业务流程**

### 1. **分解流程**
```
生产计划 → 状态验证 → 获取BOM树 → 分析产品 → 生成工单 → 时间安排 → 保存工单 → 更新状态
```

### 2. **撤销流程**
```
已分解状态 → 查询工单 → 状态检查 → 软删除工单 → 更新状态为未分解
```

### 3. **预览流程**
```
生产计划 → 获取BOM树 → 分析产品 → 生成预览信息
```

## 🎯 **应用场景**

### 1. **生产计划分解**
- 根据BOM结构自动生成生产工单
- 支持复杂的产品层级结构
- 确保生产计划的完整性

### 2. **工单管理**
- 每个产品对应一个生产工单
- 支持工单的状态跟踪
- 提供完整的工单信息

### 3. **生产调度**
- 自动计算工单时间安排
- 支持多工单的并行处理
- 确保生产计划的按时完成

## 🔧 **配置要求**

### 1. **依赖注入**
确保以下服务已正确注册：
```csharp
builder.Services.AddScoped<IBomDecompositionService, BomDecompositionService>();
builder.Services.AddScoped<IProductionPlanService, ProductionPlanService>();
```

### 2. **数据库要求**
- ProductionPlan表包含BomId字段
- BomInfo表包含完整的BOM信息
- ProductionOrder表支持工单数据存储

## 📝 **注意事项**

1. **物料ID处理**：当前版本使用临时方案处理物料ID，在实际应用中需要根据业务需求完善
2. **产品识别**：根据BOM编号判断是否为产品，可根据实际业务逻辑调整
3. **数量计算**：基于UsageQuantity和计划数量计算实际需求量
4. **时间安排**：按比例分配时间，可根据实际生产需求优化

## 🚀 **未来优化**

1. **智能物料ID解析**：根据产品编号查询数据库获取准确的物料ID
2. **高级时间安排**：考虑生产能力和资源约束的时间安排算法
3. **批量分解**：支持多个生产计划的批量分解
4. **分解模板**：支持分解模板的保存和复用 