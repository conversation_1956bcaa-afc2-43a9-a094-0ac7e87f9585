using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.AI.KouZi_AI.Dtos
{
    /// <summary>
    /// 扣子空间智能体请求参数DTO（符合v3 API规范）
    /// 
    /// 此DTO封装了与扣子AI平台交互所需的所有请求参数，设计原则：
    /// 
    /// 1. 数据验证：使用DataAnnotations确保数据有效性
    /// 2. 可选配置：提供合理的默认值减少配置复杂度
    /// 3. 向后兼容：支持扣子AI v3 API的所有功能特性
    /// 4. 类型安全：使用强类型避免运行时错误
    /// 5. 文档完整：详细的注释说明每个字段的作用
    /// 
    /// 使用场景：
    /// - 用户发起AI对话请求
    /// - 系统内部调用AI服务
    /// - 批量处理AI任务
    /// - 集成第三方应用
    /// 
    /// 数据流向：
    /// 客户端请求 -> KouZiAIRequestDto -> API处理 -> 扣子AI平台
    /// 
    /// 验证规则：
    /// - Content字段必填且长度限制
    /// - 其他字段可选但有格式要求
    /// - 自动处理默认值和空值情况
    /// 
    /// 安全考虑：
    /// - 输入长度限制防止DoS攻击
    /// - 内容过滤避免恶意输入
    /// - 敏感信息处理规范
    /// </summary>
    public class KouZiAIRequestDto
    {
        /// <summary>
        /// 用户输入的消息内容 - 核心交互数据
        /// 
        /// 这是用户与AI智能体交互的主要载体：
        /// - 支持纯文本内容（未来可扩展支持富文本）
        /// - 长度限制确保性能和成本控制
        /// - 内容会被发送到扣子AI进行理解和处理
        /// 
        /// 验证规则：
        /// - 必填字段：不能为空或null
        /// - 长度限制：最大4000字符（基于token消耗考虑）
        /// - 字符编码：支持UTF-8，包括中文、英文、特殊字符
        /// 
        /// 最佳实践：
        /// - 清晰表达用户意图和需求
        /// - 避免过于复杂的单次请求
        /// - 考虑上下文相关性
        /// - 适当使用标点符号提高理解准确性
        /// 
        /// 示例内容：
        /// - "请介绍一下人工智能的发展历史"
        /// - "帮我制定一个健康的减肥计划"
        /// - "解释一下什么是区块链技术"
        /// </summary>
        [Required(ErrorMessage = "消息内容不能为空")]
        [StringLength(4000, ErrorMessage = "消息内容不能超过4000字符")]
        public string Content { get; set; }

        /// <summary>
        /// 智能体ID（Bot标识符）- 指定处理请求的AI智能体
        /// 
        /// 扣子AI平台支持多个不同配置和能力的智能体：
        /// - 每个智能体有独特的知识库和处理能力
        /// - 不同智能体可能专精于不同领域（医疗、教育、技术等）
        /// - ID格式通常为数字字符串，如"7524702072735367168"
        /// 
        /// 配置逻辑：
        /// - 如果指定：使用指定的智能体处理请求
        /// - 如果为空：使用配置文件中的默认智能体(KouZiAI:BotId)
        /// - 配置文件也为空：抛出配置错误异常
        /// 
        /// 使用场景：
        /// - 多租户系统：不同用户使用不同智能体
        /// - A/B测试：对比不同智能体的效果
        /// - 专业领域：使用特定领域的专家智能体
        /// - 功能隔离：不同功能使用不同配置的智能体
        /// 
        /// 注意事项：
        /// - 智能体ID需要在扣子AI平台预先创建
        /// - 不同智能体可能有不同的计费规则
        /// - 确保有权限访问指定的智能体
        /// </summary>
        [DefaultValue("7524702072735367168")]
        public string? BotId { get; set; }
        
        /// <summary>
        /// 用户ID（用户唯一标识符）- 用于会话管理和个性化服务
        /// 
        /// 用户标识符在AI交互中的重要作用：
        /// - 会话关联：将多次对话关联到同一用户
        /// - 上下文维护：保持对话的连续性和个性化
        /// - 使用统计：追踪用户的使用模式和偏好
        /// - 权限控制：基于用户身份的访问控制
        /// 
        /// ID格式建议：
        /// - 使用系统中的用户唯一标识符
        /// - 避免使用敏感的个人信息（如邮箱、手机号）
        /// - 保持格式一致性（如UUID、数字ID等）
        /// - 考虑隐私保护需求
        /// 
        /// 默认处理：
        /// - 如果未提供：系统使用默认值"123456"
        /// - 默认值适用于匿名用户或测试场景
        /// - 生产环境建议提供真实的用户标识符
        /// 
        /// 隐私考虑：
        /// - 不要包含个人敏感信息
        /// - 考虑使用哈希或加密后的标识符
        /// - 遵循数据保护法规要求
        /// 
        /// 示例：
        /// - "user_12345"
        /// - "550e8400-e29b-41d4-a716-************"
        /// - "anonymous_user_001"
        /// </summary>
        public string? UserId { get; set; }
        
        /// <summary>
        /// 会话ID（对话上下文标识符）- 维持多轮对话的连续性
        /// 
        /// 会话管理是AI对话系统的核心功能：
        /// - 上下文保持：AI能够理解前面对话的内容
        /// - 主题连贯：保持对话主题的一致性
        /// - 个性化交互：基于历史对话提供个性化回复
        /// - 状态管理：维护对话过程中的状态信息
        /// 
        /// 生命周期：
        /// - 新对话：不提供ConversationId，系统会创建新会话
        /// - 继续对话：使用之前对话返回的ConversationId
        /// - 会话终止：显式结束或超时自动清理
        /// 
        /// 使用模式：
        /// 1. 首次对话：ConversationId = null
        /// 2. 后续对话：ConversationId = 上次响应中的ConversationId
        /// 3. 新话题：ConversationId = null 开始新会话
        /// 
        /// 技术细节：
        /// - ID格式：通常为扣子AI生成的长整型字符串
        /// - 有效期：根据平台配置，通常24小时到7天
        /// - 存储：建议在客户端临时存储，服务端不持久化
        /// 
        /// 最佳实践：
        /// - 单个会话专注于一个主题或任务
        /// - 会话过长时考虑重新开始
        /// - 处理会话过期的异常情况
        /// - 为用户提供"开始新对话"的选项
        /// 
        /// 示例场景：
        /// - 多轮问答：用户问"天气如何"，然后问"那明天呢"
        /// - 任务协作：制定计划 -> 细化步骤 -> 调整优化
        /// - 教学辅导：提问 -> 解答 -> 深入探讨 -> 总结
        /// </summary>
        public string? ConversationId { get; set; }
        
        /// <summary>
        /// 是否开启流式响应 - 控制响应方式的关键参数
        /// 
        /// 流式响应vs非流式响应的对比：
        /// 
        /// 流式响应（Stream = true）：
        /// - 实时体验：用户可以看到AI逐字生成回复
        /// - 响应更快：无需等待完整回复即可开始显示
        /// - 用户体验好：类似真人打字的自然感觉
        /// - 网络效率高：边生成边传输，减少延迟感知
        /// - 适用场景：实时聊天、长文本生成、交互式应用
        /// 
        /// 非流式响应（Stream = false）：
        /// - 完整性好：一次性获得完整的回复内容
        /// - 处理简单：无需处理复杂的流式解析
        /// - 稳定性高：网络问题影响较小
        /// - 集成容易：适合批处理和API集成
        /// - 适用场景：批量处理、后端服务、简单问答
        /// 
        /// 技术实现差异：
        /// - 流式：使用Server-Sent Events (SSE)协议
        /// - 非流式：标准HTTP请求-响应模式
        /// - 流式需要特殊的客户端处理逻辑
        /// - 非流式使用标准的HTTP客户端即可
        /// 
        /// 性能考虑：
        /// - 流式响应：服务器资源占用时间longer，但用户感知更快
        /// - 非流式响应：服务器处理更集中，但用户等待时间长
        /// - 网络环境：不稳定网络建议使用非流式
        /// - 客户端能力：简单客户端建议使用非流式
        /// 
        /// 默认值说明：
        /// - 默认为true（流式），提供更好的用户体验
        /// - 可根据具体场景和需求进行调整
        /// - 某些方法会强制覆盖此设置
        /// </summary>
        public bool Stream { get; set; } = true;
        
        /// <summary>
        /// 是否自动保存历史记录 - 控制对话数据的持久化
        /// 
        /// 历史记录保存的重要性：
        /// - 上下文连续性：支持多轮对话的理解和响应
        /// - 个性化服务：基于历史交互提供个性化建议
        /// - 学习改进：帮助AI理解用户偏好和习惯
        /// - 问题追溯：便于排查问题和优化服务
        /// 
        /// 保存内容：
        /// - 用户输入的问题和请求
        /// - AI生成的回复和建议
        /// - 会话元数据（时间、用户ID等）
        /// - 交互状态和上下文信息
        /// 
        /// 数据安全：
        /// - 遵循数据保护法规（GDPR、个人信息保护法等）
        /// - 实施适当的数据加密和访问控制
        /// - 提供用户数据删除和导出功能
        /// - 明确数据保留期限和使用范围
        /// 
        /// 业务价值：
        /// - 服务质量：基于历史数据优化AI回复质量
        /// - 用户洞察：分析用户需求和行为模式
        /// - 产品改进：指导功能迭代和产品优化
        /// - 合规审计：满足监管要求和审计需求
        /// 
        /// 配置建议：
        /// - 个人用户：通常建议开启，提供更好体验
        /// - 企业环境：根据数据政策和合规要求确定
        /// - 敏感场景：涉及隐私敏感信息时谨慎开启
        /// - 测试环境：可以关闭以避免测试数据污染
        /// 
        /// 技术实现：
        /// - 存储位置：扣子AI平台的数据库系统
        /// - 访问方式：通过ConversationId查询历史记录
        /// - 生命周期：根据平台配置自动清理过期数据
        /// - 格式：结构化JSON格式，便于查询和分析
        /// 
        /// 默认值说明：
        /// - 默认为true，大多数场景下建议保存历史记录
        /// - 特殊需求（如隐私敏感）可设置为false
        /// - 影响对话的连续性和智能程度
        /// </summary>
        public bool AutoSaveHistory { get; set; } = true;
    }
}