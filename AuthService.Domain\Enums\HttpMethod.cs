namespace AuthService.Domain.Enums;

/// <summary>
/// HTTP方法枚举
/// 定义支持的HTTP请求方法
/// </summary>
public enum HttpMethod
{
    /// <summary>
    /// GET请求 - 获取资源
    /// </summary>
    Get = 1,

    /// <summary>
    /// POST请求 - 创建资源
    /// </summary>
    Post = 2,

    /// <summary>
    /// PUT请求 - 更新资源（完整更新）
    /// </summary>
    Put = 3,

    /// <summary>
    /// DELETE请求 - 删除资源
    /// </summary>
    Delete = 4,

    /// <summary>
    /// PATCH请求 - 部分更新资源
    /// </summary>
    Patch = 5,

    /// <summary>
    /// HEAD请求 - 获取资源头信息
    /// </summary>
    Head = 6,

    /// <summary>
    /// OPTIONS请求 - 获取支持的方法
    /// </summary>
    Options = 7
}

/// <summary>
/// HTTP方法扩展方法
/// </summary>
public static class HttpMethodExtensions
{
    /// <summary>
    /// 转换为字符串表示
    /// </summary>
    /// <param name="method">HTTP方法</param>
    /// <returns>字符串表示</returns>
    public static string ToStringValue(this HttpMethod method)
    {
        return method switch
        {
            HttpMethod.Get => "GET",
            HttpMethod.Post => "POST",
            HttpMethod.Put => "PUT",
            HttpMethod.Delete => "DELETE",
            HttpMethod.Patch => "PATCH",
            HttpMethod.Head => "HEAD",
            HttpMethod.Options => "OPTIONS",
            _ => throw new ArgumentOutOfRangeException(nameof(method), method, "不支持的HTTP方法")
        };
    }

    /// <summary>
    /// 从字符串解析HTTP方法
    /// </summary>
    /// <param name="methodString">方法字符串</param>
    /// <returns>HTTP方法枚举</returns>
    public static HttpMethod FromString(string methodString)
    {
        return methodString?.ToUpperInvariant() switch
        {
            "GET" => HttpMethod.Get,
            "POST" => HttpMethod.Post,
            "PUT" => HttpMethod.Put,
            "DELETE" => HttpMethod.Delete,
            "PATCH" => HttpMethod.Patch,
            "HEAD" => HttpMethod.Head,
            "OPTIONS" => HttpMethod.Options,
            _ => throw new ArgumentException($"不支持的HTTP方法: {methodString}", nameof(methodString))
        };
    }

    /// <summary>
    /// 检查是否为安全方法（不会修改服务器状态）
    /// </summary>
    /// <param name="method">HTTP方法</param>
    /// <returns>是否为安全方法</returns>
    public static bool IsSafe(this HttpMethod method)
    {
        return method is HttpMethod.Get or HttpMethod.Head or HttpMethod.Options;
    }

    /// <summary>
    /// 检查是否为幂等方法（多次执行结果相同）
    /// </summary>
    /// <param name="method">HTTP方法</param>
    /// <returns>是否为幂等方法</returns>
    public static bool IsIdempotent(this HttpMethod method)
    {
        return method is HttpMethod.Get or HttpMethod.Head or HttpMethod.Put or HttpMethod.Delete or HttpMethod.Options;
    }
}
