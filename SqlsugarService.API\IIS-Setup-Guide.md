# SqlsugarService.API IIS 部署指南

## 前提条件

### 1. 安装 ASP.NET Core Hosting Bundle
- 下载并安装 [ASP.NET Core 6.0 Hosting Bundle](https://dotnet.microsoft.com/download/dotnet/6.0)
- 安装后重启 IIS：`iisreset`

### 2. 启用 IIS 功能
确保以下 Windows 功能已启用：
- Internet Information Services
- World Wide Web Services
- Application Development Features
  - ASP.NET 4.8
  - .NET Extensibility 4.8
  - ISAPI Extensions
  - ISAPI Filters

## IIS 配置步骤

### 1. 创建应用程序池
1. 打开 IIS 管理器
2. 右键点击"应用程序池" → "添加应用程序池"
3. 配置如下：
   - **名称**: `SqlsugarService`
   - **.NET CLR 版本**: `无托管代码`
   - **托管管道模式**: `集成`
   - **启动模式**: `AlwaysRunning`

### 2. 配置应用程序池高级设置
右键应用程序池 → "高级设置"：
- **进程模型** → **标识**: `ApplicationPoolIdentity`
- **进程模型** → **空闲超时**: `00:00:00` (禁用)
- **回收条件** → **定期时间间隔**: `1740` (29小时)

### 3. 创建网站
1. 右键点击"网站" → "添加网站"
2. 配置如下：
   - **网站名称**: `SqlsugarService.API`
   - **应用程序池**: `SqlsugarService`
   - **物理路径**: `C:\inetpub\wwwroot\SqlsugarService`
   - **绑定类型**: `http`
   - **端口**: `80` (或其他可用端口)

## 部署方法

### 方法1: 使用部署脚本（推荐）
```powershell
# 在项目根目录执行
.\deploy-to-iis.ps1 -SitePath "C:\inetpub\wwwroot\SqlsugarService"
```

### 方法2: 手动部署
1. 发布项目：
   ```bash
   dotnet publish --configuration Release --output bin\Release\net6.0\publish
   ```

2. 复制发布文件到 IIS 目录：
   ```
   复制 bin\Release\net6.0\publish\* 到 C:\inetpub\wwwroot\SqlsugarService\
   ```

3. 设置目录权限：
   - 右键网站目录 → "属性" → "安全"
   - 添加 `IIS_IUSRS` 用户，给予"完全控制"权限

## 故障排除

### 问题1: 页面显示空白
**可能原因**:
- web.config 文件缺失或配置错误
- ASP.NET Core Hosting Bundle 未安装
- 应用程序池配置错误

**解决方案**:
1. 确认 web.config 文件存在且配置正确
2. 检查应用程序池 .NET CLR 版本设置为"无托管代码"
3. 重启应用程序池和 IIS

### 问题2: 500.19 错误
**可能原因**: web.config 配置错误

**解决方案**:
1. 检查 web.config 语法
2. 确认 ASP.NET Core Module 已安装

### 问题3: 502.5 错误
**可能原因**: 
- .NET 6.0 运行时未安装
- 应用程序启动失败

**解决方案**:
1. 安装 .NET 6.0 运行时
2. 检查应用程序日志
3. 启用 stdout 日志记录

### 问题4: 数据库连接失败
**可能原因**: 生产环境数据库连接字符串配置错误

**解决方案**:
1. 检查 appsettings.Production.json 配置
2. 确认数据库服务器可访问
3. 验证数据库用户权限

## 验证部署

部署完成后，访问以下URL验证：

1. **主页**: `http://your-domain/`
   - 应显示欢迎页面并自动重定向到 Swagger

2. **健康检查**: `http://your-domain/health`
   - 应返回 JSON 格式的健康状态

3. **API 文档**: `http://your-domain/swagger`
   - 应显示 Swagger UI 界面

4. **测试 API**: `http://your-domain/WeatherForecast`
   - 应返回天气预报数据

## 日志配置

### 启用详细日志
在 web.config 中启用 stdout 日志：
```xml
<aspNetCore processPath="dotnet" 
            arguments=".\SqlsugarService.API.dll" 
            stdoutLogEnabled="true" 
            stdoutLogFile=".\logs\stdout">
```

### 查看日志
- **应用程序日志**: `C:\inetpub\wwwroot\SqlsugarService\logs\`
- **IIS 日志**: `C:\inetpub\logs\LogFiles\`
- **Windows 事件日志**: 事件查看器 → Windows 日志 → 应用程序

## 性能优化

### 1. 启用压缩
在 IIS 中启用动态内容压缩

### 2. 配置缓存
设置适当的缓存策略

### 3. 监控性能
- 使用 IIS 性能计数器
- 配置应用程序性能监控

## 安全配置

### 1. HTTPS 配置
- 安装 SSL 证书
- 配置 HTTPS 绑定
- 启用 HTTPS 重定向

### 2. 防火墙配置
- 开放必要端口
- 限制访问来源

### 3. 身份验证
- 配置适当的身份验证方式
- 设置访问权限
