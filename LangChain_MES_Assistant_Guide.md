# 🤖 MES 智能助手 LangChain 功能完整指南

## 📋 目录

- [功能概述](#功能概述)
- [系统架构](#系统架构)
- [核心流程](#核心流程)
- [API 接口说明](#api接口说明)
- [傻瓜式使用教程](#傻瓜式使用教程)
- [实际应用示例](#实际应用示例)
- [故障排除](#故障排除)

---

## 🎯 功能概述

MES 智能助手是基于 LangChain 技术栈构建的智能对话系统，专门为制造执行系统(MES)设计。它能够：

### ✨ 核心能力

- 🗣️ **自然语言对话** - 用普通话与系统交互
- 🔍 **智能数据查询** - 查询生产、库存、销售等数据
- 📊 **自动报表生成** - 生成各类统计分析报表
- 📚 **专业知识问答** - 提供 MES 相关专业指导
- 🔧 **业务操作协助** - 指导用户完成各种操作
- 💾 **对话记忆** - 记住上下文，支持连续对话

### 🎪 适用场景

- **生产管理** - 查询生产进度、分析效率
- **库存管理** - 检查物料状态、预警处理
- **质量管理** - 质量数据分析、异常处理
- **新员工培训** - 学习系统操作、获取帮助
- **日常运营** - 快速获取各类业务信息

---

## 🏗️ 系统架构

### 技术栈组成

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层     │    │   API控制器层    │    │    服务层       │
│                │    │                │    │                │
│ • Web界面      │───▶│ • MES助手控制器  │───▶│ • LangChain服务 │
│ • 移动端       │    │ • LangChain控制器│    │ • 扣子AI服务    │
│ • API客户端    │    │ • 工具调用接口   │    │ • MES工具服务   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                      │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI引擎层      │    │   业务服务层     │    │    数据层       │
│                │    │                │    │                │
│ • Semantic     │◀───│ • 生产计划服务   │───▶│ • SqlSugar ORM │
│   Kernel       │    │ • 物料管理服务   │    │ • 数据库       │
│ • 扣子AI API   │    │ • 销售管理服务   │    │ • 配置文件     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件说明

#### 🧠 AI 引擎

- **扣子 AI** - 主要对话引擎，专门优化中文理解
- **Microsoft Semantic Kernel** - 工具调用和复杂推理
- **OpenAI/Azure OpenAI** - 底层大语言模型支持

#### 🔧 MES 工具集

- **生产订单查询** - 查询生产计划和进度
- **物料库存查询** - 检查库存状态和预警
- **销售订单查询** - 查询销售订单信息
- **BOM 信息查询** - 查询产品结构信息
- **报表生成** - 自动生成各类统计报表
- **计划创建** - 协助创建生产计划

---

## 🔄 核心流程

### 1. 基础对话流程

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant API as 🌐 API
    participant AI as 🤖 AI引擎
    participant Memory as 💾 记忆

    User->>API: 发送消息
    API->>Memory: 获取对话历史
    Memory-->>API: 返回历史记录
    API->>AI: 处理消息+历史
    AI-->>API: 返回AI回复
    API->>Memory: 保存对话
    API-->>User: 返回回复
```

### 2. 工具调用流程

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant API as 🌐 API
    participant AI as 🤖 AI引擎
    participant Tools as 🔧 MES工具
    participant DB as 🗄️ 数据库

    User->>API: "查询今天的生产订单"
    API->>AI: 分析用户意图
    AI->>AI: 判断需要工具调用
    AI->>Tools: 调用生产订单查询工具
    Tools->>DB: 查询数据库
    DB-->>Tools: 返回数据
    Tools-->>AI: 返回查询结果
    AI->>AI: 整合数据生成回复
    AI-->>API: 返回最终回复
    API-->>User: "今天有3个生产订单..."
```

### 3. 知识库查询流程

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant API as 🌐 API
    participant AI as 🤖 AI引擎
    participant KB as 📚 知识库

    User->>API: "如何创建生产计划？"
    API->>AI: 分析问题类型
    AI->>KB: 搜索相关知识
    KB-->>AI: 返回操作指南
    AI->>AI: 整合知识生成回复
    AI-->>API: 返回详细指导
    API-->>User: "创建生产计划的步骤..."
```

---

## 📡 API 接口说明

### 主要接口列表

| 接口路径                         | 方法 | 功能描述            |
| -------------------------------- | ---- | ------------------- |
| `/api/mesassistant/chat`         | POST | MES 智能助手对话    |
| `/api/mesassistant/chat-stream`  | POST | 流式对话            |
| `/api/mesassistant/help`         | GET  | 获取帮助信息        |
| `/api/mesassistant/execute-tool` | POST | 执行工具调用        |
| `/api/langchain/send-message`    | POST | 基础 LangChain 对话 |
| `/api/langchain/quick-send`      | POST | 快速发送消息        |

### 核心接口详解

#### 1. MES 助手对话接口

**接口地址**: `POST /api/mesassistant/chat`

**请求参数**:

```json
{
  "message": "查询今天的生产订单",
  "userId": "user123",
  "conversationId": "conv456"
}
```

**响应格式**:

```json
{
  "success": true,
  "message": "今天共有3个生产订单...",
  "conversationId": "conv456",
  "source": "KouZiAI",
  "needsToolExecution": true,
  "requiredActions": [
    {
      "toolName": "query_production_orders",
      "parameters": {
        "date": "2025-01-31"
      }
    }
  ],
  "createdAt": "2025-01-31T10:30:00Z"
}
```

#### 2. 工具执行接口

**接口地址**: `POST /api/mesassistant/execute-tool`

**请求参数**:

```json
{
  "toolName": "query_production_orders",
  "parameters": {
    "pageIndex": 1,
    "pageSize": 10,
    "planName": "iPhone生产",
    "status": 5
  }
}
```

**响应格式**:

```json
{
  "success": true,
  "result": {
    "data": [
      {
        "id": 1,
        "planName": "iPhone外壳生产",
        "productName": "iPhone外壳",
        "planQuantity": 1000,
        "completedQuantity": 650,
        "progress": 65.0,
        "status": 5
      }
    ],
    "total": 1,
    "pageIndex": 1,
    "pageSize": 10
  }
}
```

---

## 📖 傻瓜式使用教程

### 🚀 快速开始

#### 步骤 1: 启动服务

1. **打开命令行工具** (PowerShell 或 CMD)

2. **导航到项目目录**

```bash
cd C:\Users\<USER>\Desktop\EmployeeService\EmployeeService
```

3. **启动 AuthService 服务**

```bash
cd AuthService.Api
dotnet run
```

> 🔍 **说明**: 这会启动认证服务，默认端口通常是 5000 或 5001

4. **新开一个命令行窗口，启动 SqlsugarService**

```bash
cd SqlsugarService.API
dotnet run
```

> 🔍 **说明**: 这会启动主要的 MES 服务，包含 LangChain 功能

5. **确认服务启动成功**
   - 看到类似 `Now listening on: https://localhost:7001` 的信息
   - 没有报错信息

#### 步骤 2: 访问 API 文档

1. **打开浏览器**

2. **访问 Swagger 文档**

```
https://localhost:7001/swagger
```

> 🔍 **说明**: 端口号可能不同，请查看启动日志中的实际端口

3. **找到 MES 助手相关接口**
   - 查找 `MESAssistant` 相关的接口
   - 点击展开查看详细信息

#### 步骤 3: 第一次对话测试

1. **找到聊天接口**

   - 在 Swagger 中找到 `POST /api/mesassistant/chat`
   - 点击 "Try it out" 按钮

2. **输入测试数据**

```json
{
  "message": "你好，请介绍一下你的功能",
  "userId": "test_user",
  "conversationId": "test_conv_001"
}
```

3. **点击 Execute 执行**

4. **查看返回结果**
   - 应该看到 AI 的回复介绍其功能
   - `success` 字段应该为 `true`

### 🎯 常用功能使用指南

#### 功能 1: 查询生产订单

**使用场景**: 想了解当前的生产情况

**操作步骤**:

1. **发送查询请求**

```json
{
  "message": "查询今天的生产订单",
  "userId": "production_manager",
  "conversationId": "prod_query_001"
}
```

2. **AI 会自动**:

   - 理解你要查询生产订单
   - 调用生产订单查询工具
   - 从数据库获取数据
   - 整理成易读的格式返回

3. **预期回复示例**:

```
今天共有3个生产订单：

📋 订单1：
- 订单号：PO20250131001
- 产品：iPhone外壳
- 计划数量：1000个
- 完成数量：650个
- 进度：65%
- 状态：进行中

📋 订单2：
- 订单号：PO20250131002
- 产品：电路板
- 计划数量：500个
- 完成数量：500个
- 进度：100%
- 状态：已完成

📋 订单3：
- 订单号：PO20250131003
- 产品：包装盒
- 计划数量：800个
- 完成数量：200个
- 进度：25%
- 状态：进行中
```

#### 功能 2: 查询物料库存

**使用场景**: 检查某个物料的库存情况

**操作步骤**:

1. **发送查询请求**

```json
{
  "message": "物料A001的库存还有多少？",
  "userId": "warehouse_manager",
  "conversationId": "inventory_check_001"
}
```

2. **AI 处理过程**:

   - 识别物料编码 "A001"
   - 调用物料库存查询工具
   - 查询该物料的详细信息

3. **预期回复示例**:

```
📦 物料A001的库存情况：

🏷️ 基本信息：
- 物料编码：A001
- 物料名称：钢板材料
- 规格型号：304不锈钢 2mm
- 单位：张

📊 库存状态：
- 当前库存：100张
- 安全库存：50张
- 库存状态：正常 ✅
- 最后更新：2025-01-25

💡 建议：库存充足，无需补货
```

#### 功能 3: 生成生产报表

**使用场景**: 需要了解生产统计数据

**操作步骤**:

1. **发送报表请求**

```json
{
  "message": "生成本月的生产统计报表",
  "userId": "production_director",
  "conversationId": "report_gen_001"
}
```

2. **AI 处理过程**:

   - 理解要生成报表
   - 调用报表生成工具
   - 汇总各类生产数据
   - 计算关键指标

3. **预期回复示例**:

```
📊 2025年1月生产统计报表

📈 总体概况：
- 生产计划总数：15个
- 已完成计划：8个 (53.33%)
- 进行中计划：5个 (33.33%)
- 待开始计划：2个 (13.33%)

🎯 生产效率：
- 计划总产量：50,000件
- 实际完成：39,250件
- 完成率：78.5%
- 日均产量：1,266件

📋 详细分析：
- 按时完成率：87.5%
- 质量合格率：99.2%
- 设备利用率：85.3%

💡 改进建议：
1. 关注进行中的5个计划，确保按时完成
2. 优化生产流程，提升日均产量
3. 加强设备维护，提高利用率
```

#### 功能 4: 获取操作指导

**使用场景**: 不知道如何操作某个功能

**操作步骤**:

1. **发送指导请求**

```json
{
  "message": "如何创建新的生产计划？",
  "userId": "new_employee",
  "conversationId": "training_001"
}
```

2. **AI 处理过程**:

   - 识别这是操作指导需求
   - 从知识库中查找相关指南
   - 整理成步骤化的指导

3. **预期回复示例**:

```
📋 创建生产计划的详细步骤：

🔍 1. 准备阶段：
- ✅ 确认产品BOM结构是否完整
- ✅ 检查所需物料库存是否充足
- ✅ 确认相关设备是否可用
- ✅ 了解客户交期要求

⚙️ 2. 创建步骤：
① 登录MES系统
② 进入"生产管理"模块
③ 点击"新建生产计划"按钮
④ 填写基本信息：
   - 计划名称（建议格式：产品名_日期_批次）
   - 产品信息（选择或输入产品编码）
   - 客户信息（如果是客户订单）
⑤ 设置数量和时间：
   - 计划生产数量
   - 计划开始时间
   - 计划完成时间
⑥ 选择BOM版本（使用最新有效版本）
⑦ 设置优先级（紧急/高/中/低）
⑧ 添加备注说明

✅ 3. 审核发布：
① 检查计划信息的完整性和合理性
② 点击"提交审核"
③ 等待主管审核通过
④ 审核通过后自动发布执行

💡 小贴士：
- 建议先进行物料需求分析，确保原材料充足
- 合理安排时间，考虑设备维护和人员休息
- 紧急订单要及时沟通协调资源
- 定期检查计划执行进度并及时调整

❓ 如果遇到问题，可以联系生产主管或系统管理员
```

### 🔧 高级功能使用

#### 连续对话功能

**特点**: AI 会记住之前的对话内容，支持上下文相关的连续提问

**示例对话**:

**第 1 轮**:

```json
{
  "message": "查询生产订单PO20250131001的详细信息",
  "userId": "user123",
  "conversationId": "continuous_chat_001"
}
```

**AI 回复**: 返回该订单的详细信息...

**第 2 轮** (在同一个 conversationId 下):

```json
{
  "message": "这个订单的进度为什么这么慢？",
  "userId": "user123",
  "conversationId": "continuous_chat_001"
}
```

**AI 回复**: AI 会知道"这个订单"指的是 PO20250131001，并分析进度慢的可能原因...

#### 流式对话功能

**使用场景**: 需要实时看到 AI 的回复过程

**接口**: `POST /api/mesassistant/chat-stream`

**特点**:

- 实时返回 AI 生成的内容
- 用户体验更好，不用等待完整回复
- 适合长篇回复的场景

### 🎪 实际应用场景

#### 场景 1: 生产主管的一天

**上午 9 点 - 查看生产状况**

```
用户: "早上好，帮我看看今天的生产情况"
AI: 分析当日生产订单，提供概览和重点关注事项
```

**上午 10 点 - 处理异常**

```
用户: "生产线2号出现故障，影响了订单PO001的进度，怎么处理？"
AI: 提供故障处理流程，建议调整生产计划
```

**下午 2 点 - 物料检查**

```
用户: "检查一下关键物料的库存，有没有需要紧急采购的？"
AI: 查询库存状态，标识预警物料，提供采购建议
```

**下午 5 点 - 日报生成**

```
用户: "生成今天的生产日报"
AI: 自动汇总当日生产数据，生成完整报表
```

#### 场景 2: 新员工培训

**第 1 天 - 系统介绍**

```
新员工: "我是新来的，请介绍一下MES系统的主要功能"
AI: 详细介绍系统各模块功能和基本操作流程
```

**第 2 天 - 具体操作**

```
新员工: "如何查询某个产品的BOM结构？"
AI: 提供详细的操作步骤和注意事项
```

**第 3 天 - 实际应用**

```
新员工: "客户要求查询订单SO001的交期，我应该怎么查？"
AI: 指导查询销售订单，关联生产计划，计算交期
```

### ⚠️ 注意事项

#### 1. 数据安全

- 不要在对话中包含敏感的个人信息
- 系统会记录对话内容用于改进服务
- 重要操作建议通过正式流程确认

#### 2. 系统限制

- AI 回复基于当前数据库中的数据
- 某些复杂业务逻辑可能需要人工处理
- 系统维护期间功能可能暂时不可用

#### 3. 最佳实践

- 问题描述尽量具体明确
- 包含相关的订单号、物料编码等关键信息
- 对于重要决策，建议结合人工判断

---

## 🔧 故障排除

### 常见问题及解决方案

#### 问题 1: 服务启动失败

**症状**: 运行 `dotnet run` 时出现错误

**可能原因**:

- .NET SDK 版本不匹配
- 端口被占用
- 数据库连接失败
- 配置文件错误

**解决步骤**:

1. **检查.NET 版本**

```bash
dotnet --version
```

确保版本为 6.0 或更高

2. **检查端口占用**

```bash
netstat -ano | findstr :7001
```

如果端口被占用，结束相关进程或修改配置

3. **检查数据库连接**

- 确认数据库服务正在运行
- 检查连接字符串配置

4. **查看详细错误日志**

```bash
dotnet run --verbosity detailed
```

#### 问题 2: AI 回复异常

**症状**: AI 回复内容不正确或出现错误

**可能原因**:

- AI 服务配置错误
- API 密钥失效
- 网络连接问题

**解决步骤**:

1. **检查配置文件**

```json
{
  "LangChain": {
    "ApiKey": "your-api-key",
    "Endpoint": "https://api.openai.com/v1",
    "ModelName": "gpt-3.5-turbo"
  }
}
```

2. **测试网络连接**

```bash
ping api.openai.com
```

3. **查看应用日志**

- 检查控制台输出的错误信息
- 查看日志文件中的详细错误

#### 问题 3: 工具调用失败

**症状**: AI 无法查询数据或执行操作

**可能原因**:

- 数据库连接问题
- 权限不足
- 数据格式错误

**解决步骤**:

1. **测试数据库连接**

```bash
# 在项目目录下运行
dotnet ef database update
```

2. **检查用户权限**

- 确认用户有相应的数据访问权限
- 检查角色配置

3. **查看工具执行日志**

- 检查 MESToolService 的日志输出
- 确认参数传递是否正确

### 获取帮助

如果遇到无法解决的问题：

1. **查看系统帮助**

```bash
GET /api/mesassistant/help
```

2. **联系技术支持**

- 邮箱：<EMAIL>
- 电话：400-123-4567
- 内部工单系统

3. **查看文档**

- API 文档：https://localhost:7001/swagger
- 用户手册：项目 docs 目录
- 开发文档：README.md

---

## 🎉 总结

MES 智能助手 LangChain 功能为制造企业提供了强大的智能化工具，通过自然语言交互大大简化了系统操作。无论是日常的数据查询、报表生成，还是复杂的业务指导，都能通过简单的对话完成。

**核心优势**:

- 🗣️ **自然交互** - 用说话的方式操作系统
- 🚀 **高效便捷** - 快速获取所需信息
- 🧠 **智能理解** - 准确理解用户意图
- 📚 **专业知识** - 内置 MES 专业知识库
- 🔄 **持续学习** - 系统会不断优化改进

开始使用 MES 智能助手，让 AI 成为您的得力助手！

---

## 🛠️ 开发者指南

### 自定义工具开发

如果需要添加新的 MES 工具功能，可以按照以下步骤：

#### 1. 创建新工具方法

在 `MESToolService.cs` 中添加新方法：

```csharp
/// <summary>
/// 查询设备状态信息
/// </summary>
public async Task<object> QueryEquipmentStatusAsync(Dictionary<string, object>? parameters)
{
    try
    {
        _logger.LogInformation("开始查询设备状态信息");

        // 解析参数
        var equipmentCode = parameters?.GetValueOrDefault("equipmentCode")?.ToString();
        var status = parameters?.GetValueOrDefault("status")?.ToString();

        // 调用设备服务查询数据
        var result = await _equipmentService.GetEquipmentListAsync(new EquipmentSearchDto
        {
            EquipmentCode = equipmentCode,
            Status = status
        });

        return new
        {
            success = true,
            message = "查询成功",
            data = result.Data?.Select(equipment => new
            {
                id = equipment.Id,
                equipmentCode = equipment.EquipmentCode,
                equipmentName = equipment.EquipmentName,
                status = equipment.Status,
                location = equipment.Location,
                lastMaintenance = equipment.LastMaintenanceDate,
                nextMaintenance = equipment.NextMaintenanceDate
            }),
            total = result.Total,
            queryTime = DateTime.UtcNow
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "查询设备状态失败");
        return new
        {
            success = false,
            message = $"查询失败: {ex.Message}",
            queryTime = DateTime.UtcNow
        };
    }
}
```

#### 2. 注册工具调用

在 `MESAssistantController.cs` 的工具执行方法中添加：

```csharp
case "query_equipment":
case "query_equipment_status":
    response.Result = await _mesToolService.QueryEquipmentStatusAsync(request.Parameters);
    response.Success = true;
    break;
```

#### 3. 更新 AI 提示词

在系统提示词中添加新工具的描述，让 AI 知道何时调用这个工具。

### 配置文件详解

#### appsettings.json 完整配置示例

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "SqlsugarService.Application.AI": "Debug"
    }
  },
  "AllowedHosts": "*",

  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=MES_DB;Trusted_Connection=true;TrustServerCertificate=true;"
  },

  "LangChain": {
    "ApiKey": "your-openai-api-key",
    "Endpoint": "https://api.openai.com/v1",
    "ModelName": "gpt-3.5-turbo",
    "Temperature": "0.7",
    "MaxTokens": "2000",
    "SystemPrompt": "你是一个专业的MES系统智能助手...",
    "EnableMemory": true,
    "MemoryMaxSize": 50
  },

  "KouZiAI": {
    "ApiKey": "your-kouzi-api-key",
    "Endpoint": "https://api.coze.cn/v1",
    "BotId": "your-bot-id",
    "UserId": "default-user",
    "Stream": false,
    "AutoSaveHistory": true
  },

  "MESTools": {
    "EnableToolCalls": true,
    "MaxQueryResults": 100,
    "QueryTimeout": 30,
    "CacheEnabled": true,
    "CacheExpirationMinutes": 5
  }
}
```

### 性能优化建议

#### 1. 数据库查询优化

```csharp
// 使用分页查询避免大量数据
var searchDto = new GetproductionplanSearchDto
{
    PageIndex = pageIndex,
    PageSize = Math.Min(pageSize, 50), // 限制最大页面大小
    // 添加必要的索引字段查询条件
};

// 使用缓存减少重复查询
[MemoryCache(Duration = 300)] // 缓存5分钟
public async Task<object> QueryProductionOrdersAsync(Dictionary<string, object>? parameters)
{
    // 查询逻辑
}
```

#### 2. AI 调用优化

```csharp
// 设置合理的超时时间
var executionSettings = new OpenAIPromptExecutionSettings
{
    MaxTokens = 1000,
    Temperature = 0.7,
    TopP = 0.9,
    FrequencyPenalty = 0.0,
    PresencePenalty = 0.0,
    RequestTimeout = TimeSpan.FromSeconds(30)
};
```

#### 3. 内存管理

```csharp
// 定期清理对话历史
private void CleanupChatHistories()
{
    var cutoffTime = DateTime.UtcNow.AddHours(-24);
    var keysToRemove = _chatHistories
        .Where(kvp => kvp.Value.LastAccessTime < cutoffTime)
        .Select(kvp => kvp.Key)
        .ToList();

    foreach (var key in keysToRemove)
    {
        _chatHistories.Remove(key);
    }
}
```

### 监控和日志

#### 1. 结构化日志

```csharp
_logger.LogInformation("MES工具调用开始 - 工具: {ToolName}, 用户: {UserId}, 参数: {@Parameters}",
    toolName, userId, parameters);

_logger.LogWarning("查询结果为空 - 工具: {ToolName}, 查询条件: {@SearchConditions}",
    toolName, searchConditions);

_logger.LogError(ex, "工具调用失败 - 工具: {ToolName}, 错误: {ErrorMessage}",
    toolName, ex.Message);
```

#### 2. 性能监控

```csharp
using var activity = ActivitySource.StartActivity("MESToolExecution");
activity?.SetTag("tool.name", toolName);
activity?.SetTag("user.id", userId);

var stopwatch = Stopwatch.StartNew();
try
{
    var result = await ExecuteToolAsync(toolName, parameters);
    activity?.SetTag("execution.success", true);
    return result;
}
catch (Exception ex)
{
    activity?.SetTag("execution.success", false);
    activity?.SetTag("error.message", ex.Message);
    throw;
}
finally
{
    stopwatch.Stop();
    activity?.SetTag("execution.duration_ms", stopwatch.ElapsedMilliseconds);

    _logger.LogInformation("工具执行完成 - 耗时: {Duration}ms", stopwatch.ElapsedMilliseconds);
}
```

### 安全考虑

#### 1. 输入验证

```csharp
public async Task<object> QueryProductionOrdersAsync(Dictionary<string, object>? parameters)
{
    // 验证参数
    if (parameters != null)
    {
        // 防止SQL注入
        if (parameters.TryGetValue("planName", out var planName))
        {
            var sanitizedPlanName = planName?.ToString()?.Replace("'", "''");
            parameters["planName"] = sanitizedPlanName;
        }

        // 限制查询范围
        if (parameters.TryGetValue("pageSize", out var pageSizeObj) &&
            int.TryParse(pageSizeObj.ToString(), out var pageSize))
        {
            parameters["pageSize"] = Math.Min(pageSize, 100); // 最大100条
        }
    }

    // 执行查询...
}
```

#### 2. 权限控制

```csharp
[Authorize(Roles = "ProductionManager,SystemAdmin")]
public async Task<ActionResult<MESToolExecutionResponseDto>> ExecuteToolAsync(
    [FromBody] MESToolExecutionRequestDto request)
{
    // 检查用户权限
    var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
    if (!IsAuthorizedForTool(request.ToolName, userRole))
    {
        return Forbid("您没有权限执行此操作");
    }

    // 执行工具...
}

private bool IsAuthorizedForTool(string toolName, string userRole)
{
    var toolPermissions = new Dictionary<string, string[]>
    {
        ["query_production_orders"] = new[] { "ProductionManager", "SystemAdmin", "Operator" },
        ["create_production_plan"] = new[] { "ProductionManager", "SystemAdmin" },
        ["update_order_status"] = new[] { "ProductionManager", "SystemAdmin" }
    };

    return toolPermissions.TryGetValue(toolName, out var allowedRoles) &&
           allowedRoles.Contains(userRole);
}
```

### 部署指南

#### 1. Docker 部署

创建 `Dockerfile`:

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["SqlsugarService.API/SqlsugarService.API.csproj", "SqlsugarService.API/"]
COPY ["SqlsugarService.Application/SqlsugarService.Application.csproj", "SqlsugarService.Application/"]
COPY ["SqlsugarService.Domain/SqlsugarService.Domain.csproj", "SqlsugarService.Domain/"]
COPY ["SqlsugarService.Infrastructure/SqlsugarService.Infrastructure.csproj", "SqlsugarService.Infrastructure/"]

RUN dotnet restore "SqlsugarService.API/SqlsugarService.API.csproj"
COPY . .
WORKDIR "/src/SqlsugarService.API"
RUN dotnet build "SqlsugarService.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SqlsugarService.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "SqlsugarService.API.dll"]
```

创建 `docker-compose.yml`:

```yaml
version: "3.8"

services:
  mes-api:
    build: .
    ports:
      - "8080:80"
      - "8443:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=db;Database=MES_DB;User Id=sa;Password=YourPassword123;TrustServerCertificate=true;
    depends_on:
      - db
    volumes:
      - ./logs:/app/logs

  db:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourPassword123
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql

volumes:
  sqlserver_data:
```

#### 2. 生产环境配置

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "SqlsugarService.Application.AI": "Information"
    },
    "File": {
      "Path": "/app/logs/mes-api-.log",
      "RollingInterval": "Day",
      "RetainedFileCountLimit": 30
    }
  },

  "LangChain": {
    "ApiKey": "${OPENAI_API_KEY}",
    "Endpoint": "${OPENAI_ENDPOINT}",
    "ModelName": "gpt-3.5-turbo",
    "Temperature": "0.5",
    "MaxTokens": "1500",
    "EnableMemory": true,
    "MemoryMaxSize": 100
  },

  "HealthChecks": {
    "Enabled": true,
    "DatabaseCheck": true,
    "AIServiceCheck": true
  },

  "RateLimiting": {
    "Enabled": true,
    "RequestsPerMinute": 60,
    "BurstSize": 10
  }
}
```

---

_文档版本: v1.0 | 更新时间: 2025-01-31 | 作者: MES 开发团队_
