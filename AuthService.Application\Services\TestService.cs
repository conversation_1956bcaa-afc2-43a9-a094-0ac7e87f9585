using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace AuthService.Application.Services;

/// <summary>
/// 测试服务实现 - 用于验证自动发现功能
/// </summary>
public class TestService : ITestService
{
    private readonly ILogger<TestService> _logger;
    private static readonly List<TestData> _testData = new();
    private static int _nextId = 1;

    public TestService(ILogger<TestService> logger)
    {
        _logger = logger;
        
        // 初始化一些测试数据
        if (!_testData.Any())
        {
            _testData.AddRange(new[]
            {
                new TestData { Id = _nextId++, Name = "测试数据1", Value = 100 },
                new TestData { Id = _nextId++, Name = "测试数据2", Value = 200 },
                new TestData { Id = _nextId++, Name = "测试数据3", Value = 300 }
            });
        }
    }

    /// <summary>
    /// 获取测试消息
    /// </summary>
    public async Task<string> GetMessageAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("获取测试消息");
        
        await Task.Delay(10, cancellationToken); // 模拟异步操作
        
        var message = $"Hello from TestService! 当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
        
        _logger.LogInformation("返回测试消息: {Message}", message);
        return message;
    }

    /// <summary>
    /// 根据ID获取测试数据
    /// </summary>
    public async Task<TestData> GetTestDataAsync(int id, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("根据ID获取测试数据: {Id}", id);
        
        await Task.Delay(10, cancellationToken);
        
        var data = _testData.FirstOrDefault(x => x.Id == id);
        if (data == null)
        {
            _logger.LogWarning("未找到ID为 {Id} 的测试数据", id);
            throw new ArgumentException($"未找到ID为 {id} 的测试数据");
        }
        
        _logger.LogInformation("成功获取测试数据: {Name}", data.Name);
        return data;
    }

    /// <summary>
    /// 创建测试数据
    /// </summary>
    public async Task<TestData> CreateTestDataAsync(string name, int value, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("创建测试数据: {Name}, {Value}", name, value);
        
        if (string.IsNullOrWhiteSpace(name))
        {
            throw new ArgumentException("名称不能为空", nameof(name));
        }
        
        await Task.Delay(10, cancellationToken);
        
        var newData = new TestData
        {
            Id = _nextId++,
            Name = name,
            Value = value,
            CreatedAt = DateTime.UtcNow
        };
        
        _testData.Add(newData);
        
        _logger.LogInformation("成功创建测试数据: {Id}, {Name}", newData.Id, newData.Name);
        return newData;
    }

    /// <summary>
    /// 获取所有测试数据
    /// </summary>
    public async Task<List<TestData>> GetAllTestDataAsync(int page = 1, int size = 10, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("获取所有测试数据，页码: {Page}, 大小: {Size}", page, size);
        
        await Task.Delay(10, cancellationToken);
        
        var skip = (page - 1) * size;
        var result = _testData.Skip(skip).Take(size).ToList();
        
        _logger.LogInformation("返回 {Count} 条测试数据", result.Count);
        return result;
    }
}
