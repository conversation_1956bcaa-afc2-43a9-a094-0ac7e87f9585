# BOM分解产品信息测试脚本
# 验证分解后工单包含完整的产品信息

param(
    [Parameter(Mandatory=$true)]
    [string]$ProductionPlanId,
    
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== BOM分解产品信息测试 ===" -ForegroundColor Green
Write-Host "生产计划ID: $ProductionPlanId" -ForegroundColor Yellow
Write-Host "API地址: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

try {
    # 1. 获取生产计划信息
    Write-Host "1. 获取生产计划信息..." -ForegroundColor White
    $planResponse = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/$ProductionPlanId" -Method Get
    
    if ($planResponse.isSuc) {
        $productionPlan = $planResponse.data
        Write-Host "   获取成功!" -ForegroundColor Green
        Write-Host "   计划名称: $($productionPlan.planName)" -ForegroundColor Gray
        Write-Host "   BOM ID: $($productionPlan.bomId)" -ForegroundColor Gray
        Write-Host "   计划数量: $($productionPlan.planQuantity)" -ForegroundColor Gray
        Write-Host "   状态: $($productionPlan.status)" -ForegroundColor Gray
    } else {
        Write-Host "   获取失败: $($planResponse.msg)" -ForegroundColor Red
        return
    }
    
    # 2. 获取BOM树形结构
    Write-Host "`n2. 获取BOM树形结构..." -ForegroundColor White
    $bomResponse = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/bom-structure/$($productionPlan.bomId)" -Method Get
    
    if ($bomResponse.isSuc) {
        $bomTree = $bomResponse.data
        Write-Host "   获取成功!" -ForegroundColor Green
        Write-Host "   根节点数: $($bomTree.Count)" -ForegroundColor Gray
        
        # 收集所有节点
        $allNodes = @()
        function Collect-AllNodes {
            param($nodes)
            foreach ($node in $nodes) {
                $allNodes += $node
                if ($node.children -and $node.children.Count -gt 0) {
                    Collect-AllNodes -nodes $node.children
                }
            }
        }
        Collect-AllNodes -nodes $bomTree
        Write-Host "   总节点数: $($allNodes.Count)" -ForegroundColor Gray
        
        # 显示产品节点信息
        Write-Host "   产品节点信息:" -ForegroundColor Gray
        foreach ($node in $allNodes) {
            if ($node.bomNumber -or $node.materialType -eq "Product" -or $node.productName) {
                Write-Host "     - 显示名称: $($node.displayName)" -ForegroundColor Cyan
                Write-Host "       产品名称: $($node.productName)" -ForegroundColor Cyan
                Write-Host "       产品编号: $($node.productNumber)" -ForegroundColor Cyan
                Write-Host "       产品规格: $($node.productSpecification)" -ForegroundColor Cyan
                Write-Host "       BOM编号: $($node.bomNumber)" -ForegroundColor Cyan
                Write-Host "       BOM版本: $($node.bomVersion)" -ForegroundColor Cyan
                Write-Host "       用量: $($node.usageQuantity) $($node.unit)" -ForegroundColor Cyan
                Write-Host ""
            }
        }
    } else {
        Write-Host "   获取失败: $($bomResponse.msg)" -ForegroundColor Red
        return
    }
    
    # 3. 预览分解结果
    Write-Host "`n3. 预览分解结果..." -ForegroundColor White
    $previewResponse = Invoke-RestMethod -Uri "$BaseUrl/api/BomDecomposition/preview/$ProductionPlanId" -Method Get
    
    if ($previewResponse.isSuc) {
        $preview = $previewResponse.data
        Write-Host "   预览成功!" -ForegroundColor Green
        Write-Host "   预计生成工单数: $($preview.estimatedOrders)" -ForegroundColor Gray
        Write-Host "   可以分解: $($preview.canDecompose)" -ForegroundColor Gray
        Write-Host "   当前状态: $($preview.currentStatus)" -ForegroundColor Gray
        
        if ($preview.decompositionItems -and $preview.decompositionItems.Count -gt 0) {
            Write-Host "   分解项目详情:" -ForegroundColor Gray
            foreach ($item in $preview.decompositionItems) {
                if ($item.isProduct) {
                    Write-Host "     - 产品名称: $($item.materialName)" -ForegroundColor Yellow
                    Write-Host "       产品编号: $($item.materialNumber)" -ForegroundColor Yellow
                    Write-Host "       产品规格: $($item.productSpecification)" -ForegroundColor Yellow
                    Write-Host "       BOM编号: $($item.bomNumber)" -ForegroundColor Yellow
                    Write-Host "       BOM版本: $($item.bomVersion)" -ForegroundColor Yellow
                    Write-Host "       显示名称: $($item.displayName)" -ForegroundColor Yellow
                    Write-Host "       显示编号: $($item.displayProductNumber)" -ForegroundColor Yellow
                    Write-Host "       显示规格: $($item.displaySpecification)" -ForegroundColor Yellow
                    Write-Host "       需求量: $($item.requiredQuantity) $($item.unit)" -ForegroundColor Yellow
                    Write-Host "       层级: $($item.level)" -ForegroundColor Yellow
                    Write-Host ""
                }
            }
        }
    } else {
        Write-Host "   预览失败: $($previewResponse.msg)" -ForegroundColor Red
        return
    }
    
    # 4. 执行分解
    Write-Host "`n4. 执行分解..." -ForegroundColor White
    $decomposeResponse = Invoke-RestMethod -Uri "$BaseUrl/api/BomDecomposition/decompose/$ProductionPlanId" -Method Post
    
    if ($decomposeResponse.isSuc) {
        $result = $decomposeResponse.data
        Write-Host "   分解成功!" -ForegroundColor Green
        Write-Host "   生成工单数: $($result.totalOrders)" -ForegroundColor Gray
        Write-Host "   分解时间: $($result.decompositionTime)" -ForegroundColor Gray
        
        if ($result.orders -and $result.orders.Count -gt 0) {
            Write-Host "   生成的工单详情:" -ForegroundColor Gray
            foreach ($order in $result.orders) {
                Write-Host "     - 工单名称: $($order.orderName)" -ForegroundColor Yellow
                Write-Host "       工单编号: $($order.orderNumber)" -ForegroundColor Yellow
                Write-Host "       产品ID: $($order.productId)" -ForegroundColor Yellow
                Write-Host "       产品名称: $($order.productName)" -ForegroundColor Yellow
                Write-Host "       产品编号: $($order.productNumber)" -ForegroundColor Yellow
                Write-Host "       产品规格: $($order.specification)" -ForegroundColor Yellow
                Write-Host "       计划数量: $($order.planQuantity) $($order.unit)" -ForegroundColor Yellow
                Write-Host "       计划开始时间: $($order.planStartTime)" -ForegroundColor Yellow
                Write-Host "       计划结束时间: $($order.planEndTime)" -ForegroundColor Yellow
                Write-Host "       状态: $($order.status)" -ForegroundColor Yellow
                Write-Host ""
            }
        }
    } else {
        Write-Host "   分解失败: $($decomposeResponse.msg)" -ForegroundColor Red
        return
    }
    
    # 5. 获取分解详情
    Write-Host "`n5. 获取分解详情..." -ForegroundColor White
    $detailResponse = Invoke-RestMethod -Uri "$BaseUrl/api/BomDecomposition/detail/$ProductionPlanId" -Method Get
    
    if ($detailResponse.isSuc) {
        $detail = $detailResponse.data
        Write-Host "   获取成功!" -ForegroundColor Green
        Write-Host "   BOM树节点数: $($detail.bomTreeNodes)" -ForegroundColor Gray
        Write-Host "   分解项目总数: $($detail.totalDecompositionItems)" -ForegroundColor Gray
        Write-Host "   产品项目数: $($detail.productCount)" -ForegroundColor Gray
        Write-Host "   物料项目数: $($detail.materialCount)" -ForegroundColor Gray
        Write-Host "   将生成工单数: $($detail.willGenerateOrders)" -ForegroundColor Gray
        Write-Host "   现有工单数: $($detail.existingOrderCount)" -ForegroundColor Gray
        Write-Host "   已分解: $($detail.isDecomposed)" -ForegroundColor Gray
        
        if ($detail.productItems -and $detail.productItems.Count -gt 0) {
            Write-Host "   产品项目详情:" -ForegroundColor Gray
            foreach ($item in $detail.productItems) {
                Write-Host "     - 产品名称: $($item.materialName)" -ForegroundColor Cyan
                Write-Host "       产品编号: $($item.materialNumber)" -ForegroundColor Cyan
                Write-Host "       需求量: $($item.requiredQuantity) $($item.unit)" -ForegroundColor Cyan
                Write-Host "       层级: $($item.level)" -ForegroundColor Cyan
                Write-Host ""
            }
        }
    } else {
        Write-Host "   获取失败: $($detailResponse.msg)" -ForegroundColor Red
    }

    Write-Host "`n=== 测试完成 ===" -ForegroundColor Green

} catch {
    Write-Host "测试过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误信息: $($_.Exception)" -ForegroundColor Red
} 