﻿using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.DTOs.PlanDto
{
    /// <summary>
    /// 获取产品列表搜索参数
    /// </summary>
    public class GetProductSearchDto:Seach
    {
        /// <summary>
        /// 产品编号
        /// </summary>
        public string? MaterialNumber { get; set; }
        /// <summary>
        /// 规格型号
        /// </summary>
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位【个、箱、件、套、台、米、条】
        /// </summary>
        public string? Unit { get; set; }
    }
}
