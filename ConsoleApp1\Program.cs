﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SqlsugarService.Application.AI.KouZi_AI;
using SqlsugarService.Application.AI.KouZi_AI.Dtos;
using SqlsugarService.Application.AI.LangChain;
using SqlsugarService.Application.AI.LangChain.Dtos;

Console.WriteLine("=== AI服务测试程序 ===");

// 配置服务
var services = new ServiceCollection();
var configuration = new ConfigurationBuilder()
    .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "ConsoleApp1"))
    .AddJsonFile("appsettings.json", optional: false)
    .Build();

services.AddSingleton<IConfiguration>(configuration);
services.AddLogging(builder => builder.AddConsole());

// 配置HttpClient for KouziAI
services.AddHttpClient("KouZiAI", (serviceProvider, client) =>
{
    var config = serviceProvider.GetRequiredService<IConfiguration>();
    var apiBaseUrl = config["KouZiAI:ApiBaseUrl"] ?? "https://api.coze.cn";
    var apiToken = config["KouZiAI:ApiToken"];
    var timeoutMinutes = config.GetValue<int>("KouZiAI:TimeoutMinutes", 5);

    client.BaseAddress = new Uri(apiBaseUrl);
    if (!string.IsNullOrEmpty(apiToken) && apiToken != "YOUR_API_TOKEN_HERE")
    {
        client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiToken}");
    }
    client.Timeout = TimeSpan.FromMinutes(timeoutMinutes);
    client.DefaultRequestHeaders.Add("User-Agent", "SqlsugarService/1.0");
    client.DefaultRequestHeaders.Add("Accept", "application/json");
});

// 注册AI服务
services.AddScoped<IKouZiAIService, KouZiAIService>();
services.AddScoped<ILangChainService, LangChainService>();

var serviceProvider = services.BuildServiceProvider();

Console.WriteLine("1. 测试扣子AI服务");
await TestKouZiAI(serviceProvider);

Console.WriteLine("\n2. 测试LangChain服务");
await TestLangChain(serviceProvider);

Console.WriteLine("\n测试完成！");

static async Task TestKouZiAI(ServiceProvider serviceProvider)
{
    try
    {
        var kouziService = serviceProvider.GetRequiredService<IKouZiAIService>();

        Console.WriteLine("正在测试扣子AI健康检查...");
        var healthCheck = await kouziService.HealthCheckAsync();
        Console.WriteLine($"健康检查结果: {healthCheck}");

        if (healthCheck)
        {
            Console.WriteLine("正在发送测试消息...");
            var response = await kouziService.QuickSendAsync("你好，这是一个测试消息");

            Console.WriteLine($"响应成功: {response.Success}");
            if (response.Success)
            {
                Console.WriteLine($"AI回复: {response.Content}");
                Console.WriteLine($"会话ID: {response.ConversationId}");
            }
            else
            {
                Console.WriteLine($"错误信息: {response.ErrorMessage}");
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"扣子AI测试失败: {ex.Message}");
        Console.WriteLine($"详细错误: {ex}");
    }
}

static async Task TestLangChain(ServiceProvider serviceProvider)
{
    try
    {
        var langChainService = serviceProvider.GetRequiredService<ILangChainService>();

        Console.WriteLine("正在测试LangChain健康检查...");
        var healthCheck = await langChainService.HealthCheckAsync();
        Console.WriteLine($"健康检查结果: {healthCheck}");

        if (healthCheck)
        {
            Console.WriteLine("正在发送测试消息...");
            var response = await langChainService.QuickSendAsync("你好，这是一个测试消息");

            Console.WriteLine($"响应成功: {response.Success}");
            if (response.Success)
            {
                Console.WriteLine($"AI回复: {response.Content}");
                Console.WriteLine($"会话ID: {response.SessionId}");
            }
            else
            {
                Console.WriteLine($"错误信息: {response.ErrorMessage}");
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"LangChain测试失败: {ex.Message}");
        Console.WriteLine($"详细错误: {ex}");
    }
}

