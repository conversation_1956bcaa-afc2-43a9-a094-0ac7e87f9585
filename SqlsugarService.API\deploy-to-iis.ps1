# SqlsugarService.API IIS 部署脚本
# 使用方法: .\deploy-to-iis.ps1 -SitePath "C:\inetpub\wwwroot\SqlsugarService"

param(
    [Parameter(Mandatory=$true)]
    [string]$SitePath,
    
    [string]$Configuration = "Release",
    [string]$Framework = "net6.0"
)

Write-Host "开始部署 SqlsugarService.API 到 IIS..." -ForegroundColor Green

# 1. 清理并构建项目
Write-Host "1. 清理项目..." -ForegroundColor Yellow
dotnet clean --configuration $Configuration

Write-Host "2. 构建项目..." -ForegroundColor Yellow
dotnet build --configuration $Configuration --no-restore

if ($LASTEXITCODE -ne 0) {
    Write-Error "构建失败！"
    exit 1
}

# 3. 发布项目
Write-Host "3. 发布项目..." -ForegroundColor Yellow
$PublishPath = "bin\$Configuration\$Framework\publish"
dotnet publish --configuration $Configuration --output $PublishPath --no-build

if ($LASTEXITCODE -ne 0) {
    Write-Error "发布失败！"
    exit 1
}

# 4. 停止 IIS 应用程序池（如果存在）
Write-Host "4. 停止 IIS 应用程序池..." -ForegroundColor Yellow
try {
    Import-Module WebAdministration -ErrorAction SilentlyContinue
    $AppPoolName = "SqlsugarService"
    if (Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
        Stop-WebAppPool -Name $AppPoolName
        Write-Host "应用程序池 '$AppPoolName' 已停止" -ForegroundColor Green
    }
} catch {
    Write-Warning "无法停止应用程序池: $($_.Exception.Message)"
}

# 5. 复制文件到 IIS 目录
Write-Host "5. 复制文件到 IIS 目录..." -ForegroundColor Yellow
if (Test-Path $SitePath) {
    Remove-Item -Path "$SitePath\*" -Recurse -Force -ErrorAction SilentlyContinue
} else {
    New-Item -Path $SitePath -ItemType Directory -Force
}

Copy-Item -Path "$PublishPath\*" -Destination $SitePath -Recurse -Force

# 6. 设置权限
Write-Host "6. 设置目录权限..." -ForegroundColor Yellow
try {
    $acl = Get-Acl $SitePath
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    Set-Acl -Path $SitePath -AclObject $acl
    Write-Host "权限设置完成" -ForegroundColor Green
} catch {
    Write-Warning "设置权限失败: $($_.Exception.Message)"
}

# 7. 启动应用程序池
Write-Host "7. 启动 IIS 应用程序池..." -ForegroundColor Yellow
try {
    if (Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
        Start-WebAppPool -Name $AppPoolName
        Write-Host "应用程序池 '$AppPoolName' 已启动" -ForegroundColor Green
    }
} catch {
    Write-Warning "无法启动应用程序池: $($_.Exception.Message)"
}

Write-Host "部署完成！" -ForegroundColor Green
Write-Host "部署路径: $SitePath" -ForegroundColor Cyan
Write-Host "请确保 IIS 中已正确配置站点和应用程序池" -ForegroundColor Yellow

# 8. 显示部署后的检查清单
Write-Host "`n部署后检查清单:" -ForegroundColor Magenta
Write-Host "□ 确认 ASP.NET Core Hosting Bundle 已安装" -ForegroundColor White
Write-Host "□ 确认应用程序池 .NET CLR 版本设置为 '无托管代码'" -ForegroundColor White
Write-Host "□ 确认站点绑定和端口配置正确" -ForegroundColor White
Write-Host "□ 检查 web.config 文件是否存在" -ForegroundColor White
Write-Host "□ 访问 http://your-site/health 检查服务状态" -ForegroundColor White
Write-Host "□ 访问 http://your-site/swagger 查看 API 文档" -ForegroundColor White
