# 插入子BOM测试数据的PowerShell脚本
# 用于测试BOM树形下拉列表的子BOM展开功能

param(
    [string]$ConnectionString = "Host=*************;Port=5432;Database=sqlsugardata;Username=****;Password=****;SearchPath=public"
)

Write-Host "=== 开始插入子BOM测试数据 ===" -ForegroundColor Green

try {
    # 读取SQL脚本
    $sqlScript = Get-Content -Path ".\scripts\test-sub-bom-data.sql" -Raw -Encoding UTF8
    
    if (-not $sqlScript) {
        Write-Host "错误：无法读取SQL脚本文件" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "SQL脚本读取成功" -ForegroundColor Yellow
    
    # 执行SQL脚本
    Write-Host "正在执行SQL脚本..." -ForegroundColor Yellow
    
    # 使用psql执行SQL脚本
    $env:PGPASSWORD = "****"
    $result = & psql -h ************* -p 5432 -U **** -d sqlsugardata -c $sqlScript 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SQL脚本执行成功！" -ForegroundColor Green
        Write-Host "执行结果：" -ForegroundColor Yellow
        Write-Host $result
    } else {
        Write-Host "SQL脚本执行失败！" -ForegroundColor Red
        Write-Host "错误信息：" -ForegroundColor Red
        Write-Host $result
        exit 1
    }
    
} catch {
    Write-Host "执行过程中发生错误：$($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "=== 子BOM测试数据插入完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "数据结构说明：" -ForegroundColor Cyan
Write-Host "主产品A (PROD001) - 包含子产品B作为子BOM" -ForegroundColor White
Write-Host "子产品B (PROD002) - 包含基础物料3和4" -ForegroundColor White
Write-Host "基础物料1-4 (MAT001-MAT004) - 基础组件" -ForegroundColor White
Write-Host ""
Write-Host "现在可以测试以下API接口：" -ForegroundColor Cyan
Write-Host "1. 检查数据是否存在：GET /api/ProductionPlans/check-bom-data" -ForegroundColor White
Write-Host "2. 获取所有BOM树形（包含子BOM）：GET /api/ProductionPlans/bom-tree" -ForegroundColor White
Write-Host "3. 根据主BOM ID获取树形：GET /api/ProductionPlans/bom-tree/aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa" -ForegroundColor White
Write-Host "4. 根据子BOM ID获取树形：GET /api/ProductionPlans/bom-tree/bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb" -ForegroundColor White
Write-Host "5. 测试主BOM结构：GET /api/ProductionPlans/test-bom-structure/aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa" -ForegroundColor White
Write-Host "6. 测试子BOM结构：GET /api/ProductionPlans/test-bom-structure/bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb" -ForegroundColor White 