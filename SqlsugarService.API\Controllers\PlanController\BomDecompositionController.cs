using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.Service.Plan;
using SqlsugarService.Application.Until;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using SqlsugarService.Domain.Plan;
using SqlsugarService.Application.IService.Plan;

namespace SqlsugarService.API.Controllers.PlanController
{
    /// <summary>
    /// BOM分解控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class BomDecompositionController : ControllerBase
    {
        private readonly IBomDecompositionService _bomDecompositionService;

        public BomDecompositionController(IBomDecompositionService bomDecompositionService)
        {
            _bomDecompositionService = bomDecompositionService;
        }

        /// <summary>
        /// 分解生产计划为生产工单
        /// </summary>
        /// <param name="productionPlanId">生产计划ID</param>
        /// <returns>分解结果</returns>
        [HttpPost("decompose/{productionPlanId}")]
        public async Task<ApiResult<BomDecompositionResult>> DecomposeProductionPlan(Guid productionPlanId)
        {
            return await _bomDecompositionService.DecomposeProductionPlan(productionPlanId);
        }

        /// <summary>
        /// 撤销已分解的工单
        /// </summary>
        /// <param name="productionPlanId">生产计划ID</param>
        /// <returns>撤销结果</returns>
        [HttpPost("undo/{productionPlanId}")]
        public async Task<ApiResult<BomDecompositionResult>> UndoDecomposition(Guid productionPlanId)
        {
            return await _bomDecompositionService.UndoDecomposition(productionPlanId);
        }

        /// <summary>
        /// 预览分解结果（不保存工单）
        /// </summary>
        /// <param name="productionPlanId">生产计划ID</param>
        /// <returns>预览结果</returns>
        [HttpGet("preview/{productionPlanId}")]
        public async Task<ApiResult<BomDecompositionPreview>> PreviewDecomposition(Guid productionPlanId)
        {
            return await _bomDecompositionService.PreviewDecomposition(productionPlanId);
        }

        /// <summary>
        /// 获取生产计划的分解状态
        /// </summary>
        /// <param name="productionPlanId">生产计划ID</param>
        /// <returns>分解状态信息</returns>
        [HttpGet("status/{productionPlanId}")]
        public async Task<ApiResult<object>> GetDecompositionStatus(Guid productionPlanId)
        {
            try
            {
                var preview = await _bomDecompositionService.PreviewDecomposition(productionPlanId);
                if (!preview.IsSuc)
                {
                    return ApiResult<object>.Fail(preview.Msg, ResultCode.Error);
                }

                var statusInfo = new
                {
                    ProductionPlanId = productionPlanId,
                    ProductionPlanName = preview.Data.ProductionPlanName,
                    CurrentStatus = preview.Data.CurrentStatus,
                    CanDecompose = preview.Data.CanDecompose,
                    CanUndo = preview.Data.CurrentStatus == "已分解",
                    EstimatedOrders = preview.Data.EstimatedOrders,
                    DecompositionItems = preview.Data.DecompositionItems
                };

                return ApiResult<object>.Success(statusInfo, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<object>.Fail($"获取状态失败：{ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 查询已删除的生产工单
        /// </summary>
        /// <param name="productionPlanId">生产计划ID</param>
        /// <returns>已删除的工单列表</returns>
        [HttpGet("deleted-orders/{productionPlanId}")]
        public async Task<ApiResult<List<ProductionOrder>>> GetDeletedProductionOrders(Guid productionPlanId)
        {
            return await _bomDecompositionService.GetDeletedProductionOrders(productionPlanId);
        }

        /// <summary>
        /// 恢复已删除的生产工单
        /// </summary>
        /// <param name="productionPlanId">生产计划ID</param>
        /// <param name="request">恢复请求</param>
        /// <returns>恢复结果</returns>
        [HttpPost("restore/{productionPlanId}")]
        public async Task<ApiResult<BomDecompositionResult>> RestoreDeletedOrders(Guid productionPlanId, [FromBody] RestoreOrdersRequest request)
        {
            return await _bomDecompositionService.RestoreDeletedOrders(productionPlanId, request.OrderIds);
        }

        /// <summary>
        /// 永久删除生产工单（硬删除）
        /// </summary>
        /// <param name="productionPlanId">生产计划ID</param>
        /// <param name="request">删除请求</param>
        /// <returns>删除结果</returns>
        //[HttpPost("permanently-delete/{productionPlanId}")]
        //public async Task<ApiResult<BomDecompositionResult>> PermanentlyDeleteOrders(Guid productionPlanId, [FromBody] DeleteOrdersRequest request)
        //{
        //    return await _bomDecompositionService.PermanentlyDeleteOrders(productionPlanId, request.OrderIds);
        //}

        /// <summary>
        /// 获取BOM分解详细信息
        /// </summary>
        /// <param name="productionPlanId">生产计划ID</param>
        /// <returns>BOM分解详细信息</returns>
        [HttpGet("detail/{productionPlanId}")]
        public async Task<ApiResult<BomDecompositionDetail>> GetBomDecompositionDetail(Guid productionPlanId)
        {
            return await _bomDecompositionService.GetBomDecompositionDetail(productionPlanId);
        }
    }

    /// <summary>
    /// 恢复工单请求
    /// </summary>
    public class RestoreOrdersRequest
    {
        /// <summary>
        /// 要恢复的工单ID列表
        /// </summary>
        public List<Guid> OrderIds { get; set; } = new List<Guid>();
    }

    /// <summary>
    /// 删除工单请求
    /// </summary>
    public class DeleteOrdersRequest
    {
        /// <summary>
        /// 要删除的工单ID列表
        /// </summary>
        public List<Guid> OrderIds { get; set; } = new List<Guid>();
    }
} 