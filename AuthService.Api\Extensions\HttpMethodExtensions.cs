using AuthService.Domain.Enums;
using HttpMethod = AuthService.Domain.Enums.HttpMethod;

namespace AuthService.Api.Extensions;

/// <summary>
/// HTTP方法扩展
/// </summary>
public static class HttpMethodExtensions
{
    /// <summary>
    /// 从字符串创建HttpMethod枚举
    /// </summary>
    /// <param name="method">HTTP方法字符串</param>
    /// <returns>HttpMethod枚举</returns>
    public static HttpMethod FromString(string method)
    {
        return method.ToUpperInvariant() switch
        {
            "GET" => HttpMethod.Get,
            "POST" => HttpMethod.Post,
            "PUT" => HttpMethod.Put,
            "DELETE" => HttpMethod.Delete,
            "PATCH" => HttpMethod.Patch,
            "HEAD" => HttpMethod.Head,
            "OPTIONS" => HttpMethod.Options,
            _ => HttpMethod.Get
        };
    }

    /// <summary>
    /// 将HttpMethod枚举转换为字符串
    /// </summary>
    /// <param name="method">HttpMethod枚举</param>
    /// <returns>HTTP方法字符串</returns>
    public static string ToStringValue(this HttpMethod method)
    {
        return method switch
        {
            HttpMethod.Get => "GET",
            HttpMethod.Post => "POST",
            HttpMethod.Put => "PUT",
            HttpMethod.Delete => "DELETE",
            HttpMethod.Patch => "PATCH",
            HttpMethod.Head => "HEAD",
            HttpMethod.Options => "OPTIONS",
            _ => "GET"
        };
    }
}
