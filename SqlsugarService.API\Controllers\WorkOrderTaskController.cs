using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.WorkOrderTaskDto;
using SqlsugarService.Application.IService.Plan;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SqlsugarService.API.Controllers
{
    /// <summary>
    /// 工单任务管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class WorkOrderTaskController : ControllerBase
    {
        private readonly IWorkOrderTaskService _workOrderTaskService;

        public WorkOrderTaskController(IWorkOrderTaskService workOrderTaskService)
        {
            _workOrderTaskService = workOrderTaskService;
        }

        /// <summary>
        /// 获取工单任务分页列表
        /// </summary>
        /// <param name="searchDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost("list")]
        public async Task<IActionResult> GetWorkOrderTaskList([FromBody] GetWorkOrderTaskSearchDto searchDto)
        {
            var result = await _workOrderTaskService.GetWorkOrderTaskListAsync(searchDto);
            return Ok(result);
        }

        /// <summary>
        /// 获取所有工单任务（用于调试）
        /// </summary>
        /// <returns>所有工单任务</returns>
        [HttpGet("debug/all")]
        public async Task<IActionResult> GetAllWorkOrderTasks()
        {
            var result = await _workOrderTaskService.GetAllWorkOrderTasksAsync();
            return Ok(result);
        }

        /// <summary>
        /// 简单分页查询（用于调试）
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>分页结果</returns>
        [HttpGet("debug/simple-page")]
        public async Task<IActionResult> GetSimplePage(int pageIndex = 1, int pageSize = 10)
        {
            var searchDto = new GetWorkOrderTaskSearchDto
            {
                PageIndex = pageIndex,
                PageSize = pageSize
                // 不设置任何搜索条件
            };
            var result = await _workOrderTaskService.GetWorkOrderTaskListAsync(searchDto);
            return Ok(result);
        }

        /// <summary>
        /// 根据ID获取工单任务详情
        /// </summary>
        /// <param name="id">工单任务ID</param>
        /// <returns>工单任务详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetWorkOrderTaskById(Guid id)
        {
            var result = await _workOrderTaskService.GetWorkOrderTaskByIdAsync(id);
            return Ok(result);
        }

        /// <summary>
        /// 工单任务报工
        /// </summary>
        /// <param name="workReportDto">报工信息</param>
        /// <returns>操作结果</returns>
        [HttpPost("work-report")]
        public async Task<IActionResult> WorkReport([FromBody] WorkReportDto workReportDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _workOrderTaskService.WorkReportAsync(workReportDto);
            return Ok(result);
        }

        /// <summary>
        /// 批量工单任务报工
        /// </summary>
        /// <param name="workReportDtos">批量报工信息</param>
        /// <returns>操作结果</returns>
        [HttpPost("batch-work-report")]
        public async Task<IActionResult> BatchWorkReport([FromBody] List<WorkReportDto> workReportDtos)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _workOrderTaskService.BatchWorkReportAsync(workReportDtos);
            return Ok(result);
        }

        /// <summary>
        /// 获取工单任务的报工记录
        /// </summary>
        /// <param name="workOrderTaskId">工单任务ID</param>
        /// <returns>报工记录列表</returns>
        [HttpGet("{workOrderTaskId}/work-reports")]
        public async Task<IActionResult> GetWorkReportsByTaskId(Guid workOrderTaskId)
        {
            var result = await _workOrderTaskService.GetWorkReportsByTaskIdAsync(workOrderTaskId);
            return Ok(result);
        }

        /// <summary>
        /// 获取工单任务状态选项
        /// </summary>
        /// <returns>状态选项列表</returns>
        [HttpGet("status-options")]
        public IActionResult GetStatusOptions()
        {
            var statusOptions = new[]
            {
                new { Value = "未开工", Label = "未开工" },
                new { Value = "进行中", Label = "进行中" },
                new { Value = "已完成", Label = "已完成" },
                new { Value = "已暂停", Label = "已暂停" },
                new { Value = "已取消", Label = "已取消" }
            };

            return Ok(new { Success = true, Data = statusOptions });
        }

        /// <summary>
        /// 获取优先级选项
        /// </summary>
        /// <returns>优先级选项列表</returns>
        [HttpGet("priority-options")]
        public IActionResult GetPriorityOptions()
        {
            var priorityOptions = new[]
            {
                new { Value = 1, Label = "低" },
                new { Value = 2, Label = "中" },
                new { Value = 3, Label = "高" },
                new { Value = 4, Label = "紧急" }
            };

            return Ok(new { Success = true, Data = priorityOptions });
        }

        /// <summary>
        /// 获取检验类型选项
        /// </summary>
        /// <returns>检验类型选项列表</returns>
        [HttpGet("inspection-type-options")]
        public IActionResult GetInspectionTypeOptions()
        {
            var inspectionTypeOptions = new[]
            {
                new { Value = "首检", Label = "首检" },
                new { Value = "巡检", Label = "巡检" },
                new { Value = "末检", Label = "末检" },
                new { Value = "抽检", Label = "抽检" },
                new { Value = "全检", Label = "全检" }
            };

            return Ok(new { Success = true, Data = inspectionTypeOptions });
        }

        /// <summary>
        /// 批量派工（支持单个和批量）
        /// 功能：1. 添加派工表记录 2. 自动更新工单任务状态为"已派工"
        /// </summary>
        /// <param name="dispatchWorkDtos">派工信息列表</param>
        /// <returns>操作结果</returns>
        [HttpPost("batch-dispatch")]
        public async Task<IActionResult> BatchDispatchWork([FromBody] List<DispatchWorkDto> dispatchWorkDtos)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _workOrderTaskService.BatchDispatchWorkAsync(dispatchWorkDtos);
            return Ok(result);
        }
    }
}
