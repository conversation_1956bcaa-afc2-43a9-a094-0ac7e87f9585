# 密码安全机制详解

## 概述

本文档详细说明了AuthService中密码存储和验证的安全机制，包括密码哈希值、盐值的作用原理和使用方法。

## 核心概念

### 1. 密码哈希值 (Password Hash)

**定义**: 密码哈希值是用户明文密码经过加密算法处理后的结果，存储在数据库中。

**作用**:
- 保护用户密码安全：即使数据库泄露，攻击者也无法直接获得明文密码
- 单向加密：理论上无法从哈希值反推出原始密码
- 验证身份：通过重新计算哈希值来验证用户输入的密码是否正确

**示例**:
```
明文密码: "MyPassword123"
哈希值: "kQJmH8K7fX9vN2pL4sR6tY8uI0oP3qW5eE7rT9yU1iA="
```

### 2. 密码盐值 (Password Salt)

**定义**: 盐值是一个随机生成的字符串，在计算密码哈希值时与密码组合使用。

**作用**:
- **防止彩虹表攻击**: 攻击者无法使用预计算的哈希表破解密码
- **防止相同密码识别**: 即使两个用户使用相同密码，由于盐值不同，哈希值也不同
- **增加破解难度**: 攻击者需要为每个用户单独计算哈希值

**示例**:
```
用户A: 密码"123456" + 盐值"randomSalt1" = 哈希值"abc123..."
用户B: 密码"123456" + 盐值"randomSalt2" = 哈希值"def456..."
```

## 技术实现

### 1. PBKDF2算法

我们使用PBKDF2 (Password-Based Key Derivation Function 2) 算法：

**参数配置**:
- 哈希算法: SHA256
- 迭代次数: 10,000次
- 盐值长度: 32字节 (256位)
- 输出长度: 32字节 (256位)

**为什么选择PBKDF2**:
- 业界标准的密码哈希算法
- 通过多次迭代故意变慢，增加暴力破解成本
- 支持可配置的迭代次数，可根据硬件性能调整安全级别

### 2. 密码存储流程

```csharp
// 用户注册时的密码处理
public (string hash, string salt) HashPassword(string password)
{
    // 1. 生成32字节随机盐值
    var saltBytes = new byte[32];
    using var rng = RandomNumberGenerator.Create();
    rng.GetBytes(saltBytes);
    var salt = Convert.ToBase64String(saltBytes);

    // 2. 使用PBKDF2计算哈希值
    using var pbkdf2 = new Rfc2898DeriveBytes(password, saltBytes, 10000, HashAlgorithmName.SHA256);
    var hashBytes = pbkdf2.GetBytes(32);
    var hash = Convert.ToBase64String(hashBytes);

    return (hash, salt);
}
```

### 3. 密码验证流程

```csharp
// 用户登录时的密码验证
public bool VerifyPassword(string password, string hash, string salt)
{
    // 1. 将Base64盐值转换为字节数组
    var saltBytes = Convert.FromBase64String(salt);
    
    // 2. 使用相同参数重新计算哈希值
    using var pbkdf2 = new Rfc2898DeriveBytes(password, saltBytes, 10000, HashAlgorithmName.SHA256);
    var hashBytes = pbkdf2.GetBytes(32);
    var computedHash = Convert.ToBase64String(hashBytes);

    // 3. 比较计算结果与存储的哈希值
    return hash == computedHash;
}
```

## 登录验证流程

### 完整的用户登录过程

1. **用户输入**: 用户名和密码
2. **查找用户**: 根据用户名从数据库查找用户记录
3. **状态检查**: 验证用户是否存在且未被删除
4. **密码验证**: 
   - 获取数据库中存储的哈希值和盐值
   - 使用用户输入的密码和存储的盐值重新计算哈希值
   - 比较新计算的哈希值与存储的哈希值
5. **返回结果**: 验证成功返回用户信息，失败返回错误

### 示例代码

```csharp
public async Task<(bool isValid, User? user)> ValidateUserLoginAsync(string username, string password)
{
    // 1. 查找用户
    var user = await _userRepository.GetByUsernameAsync(username);
    if (user == null || user.IsDeleted)
        return (false, null);

    // 2. 验证密码
    bool passwordValid = VerifyPassword(password, user.PasswordHash, user.PasswordSalt);
    
    return passwordValid ? (true, user) : (false, null);
}
```

## 安全优势

### 1. 防护措施

- **数据库泄露保护**: 即使数据库被攻击者获取，也无法直接得到用户密码
- **彩虹表攻击防护**: 每个用户的唯一盐值使预计算攻击无效
- **暴力破解防护**: PBKDF2的多次迭代大大增加了破解成本
- **时序攻击防护**: 使用恒定时间的字符串比较

### 2. 合规性

- 符合OWASP密码存储指南
- 满足GDPR等数据保护法规要求
- 遵循业界最佳实践

## 最佳实践

### 1. 密码策略

- 最小长度: 8个字符
- 复杂度要求: 包含大小写字母、数字和特殊字符
- 定期更换: 建议用户定期更新密码

### 2. 系统配置

- 迭代次数: 根据服务器性能调整（推荐10,000-100,000次）
- 盐值长度: 至少32字节
- 哈希输出: 至少32字节

### 3. 监控和日志

- 记录登录尝试（成功和失败）
- 监控异常登录模式
- 不在日志中记录密码或哈希值

## 常见问题

### Q: 为什么不直接使用MD5或SHA1？
A: MD5和SHA1算法太快，容易被暴力破解。PBKDF2通过多次迭代故意变慢，增加破解成本。

### Q: 盐值需要保密吗？
A: 盐值不需要保密，可以明文存储。它的作用是防止彩虹表攻击，而不是作为密钥。

### Q: 如果忘记密码怎么办？
A: 由于哈希算法是单向的，无法恢复原始密码。只能通过重置密码的方式解决。

### Q: 可以升级哈希算法吗？
A: 可以。在用户下次登录时，使用新算法重新哈希密码并更新数据库记录。
