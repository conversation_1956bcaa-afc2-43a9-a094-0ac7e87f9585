namespace SqlsugarService.Application.DTOs.PlanDto
{
    /// <summary>
    /// 树形节点DTO - 根据BOM组成界面优化
    /// </summary>
    public class TreeNodeDto
    {
        /// <summary>
        /// 节点ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int Sequence { get; set; }

        /// <summary>
        /// 节点名称（产品名称或物料名称）
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 节点类型（BOM、产品、物料等）
        /// </summary>
        public string NodeType { get; set; } = string.Empty;

        /// <summary>
        /// 产品名称（来自BomInfo）
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 产品编号（来自BomInfo）
        /// </summary>
        public string? ProductNumber { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 物料名称（来自MaterialEntity）
        /// </summary>
        public string? MaterialName { get; set; }

        /// <summary>
        /// 物料编号（来自MaterialEntity）
        /// </summary>
        public string? MaterialNumber { get; set; }

        /// <summary>
        /// 用量
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// BOM编号（如果是子BOM）
        /// </summary>
        public string? BomNumber { get; set; }

        /// <summary>
        /// BOM版本
        /// </summary>
        public string? BomVersion { get; set; }

        /// <summary>
        /// 使用量
        /// </summary>
        public decimal? UsageQuantity { get; set; }

        /// <summary>
        /// 使用比例
        /// </summary>
        public string? UsageRatio { get; set; }

        /// <summary>
        /// 投入产出类型
        /// </summary>
        public string? InOutType { get; set; }

        /// <summary>
        /// 是否可展开（有子节点）
        /// </summary>
        public bool IsExpandable { get; set; }

        /// <summary>
        /// 节点层级（0为根节点）
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 父节点ID
        /// </summary>
        public string? ParentId { get; set; }

        /// <summary>
        /// 子节点列表
        /// </summary>
        public List<TreeNodeDto> Children { get; set; } = new List<TreeNodeDto>();
    }
}
