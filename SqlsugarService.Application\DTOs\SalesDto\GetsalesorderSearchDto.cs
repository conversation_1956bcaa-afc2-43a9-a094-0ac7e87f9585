﻿using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.DTOs.SalesDto
{
    /// <summary>
    /// 销售单查询条件
    /// </summary>
    public class GetsalesorderSearchDto : Seach
    {
        /// <summary>
        /// 销售单号
        /// </summary>
        public string? SalesCode { get; set; }

        /// <summary>
        /// 销售单名称
        /// </summary>
        public string? SalesName { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public string? OrderStatus { get; set; }
    }
}
