﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Materials;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Craftsmanship
{
    /// <summary>
    /// 工序
    /// </summary>
    public class ProcessStep : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 工序编号
        /// </summary>
        public string ProcessStepNumber { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        public string ProcessStepName { get; set; }

        /// <summary>
        /// 状态（启用/禁用）
        /// </summary>
        public StepStatus Status { get; set; } = StepStatus.Enabled;

        /// <summary>
        /// 工序说明
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; } = false;

        /// <summary>
        /// 上一道工序Id
        /// </summary>
        public Guid? PreviousProcessStepId { get; set; }
        public ProcessStep? PreviousProcessStep { get; set; }

        /// <summary>
        /// 下一道工序Id
        /// </summary>
        public Guid? NextProcessStepId { get; set; }
        public ProcessStep? NextProcessStep { get; set; }

        /// <summary>
        /// 与下一道工序关系（如S-F）
        /// </summary>
        public string? RelationToNextStep { get; set; }

        /// <summary>
        /// 是否关键工序
        /// </summary>
        public bool IsKeyStep { get; set; } = false;

        /// <summary>
        /// 准备时间（分钟）
        /// </summary>
        public int PreparationTime { get; set; } = 0;

        /// <summary>
        /// 等待时间（分钟）
        /// </summary>
        public int WaitingTime { get; set; } = 0;

        /// <summary>
        /// 颜色（如#1890FF）
        /// </summary>
        public string? Color { get; set; }

        // 导航属性
        /// <summary>
        /// 工艺路线-工序关联列表
        /// </summary>
        public ICollection<ProcessRouteStep> ProcessRouteSteps { get; set; } = new List<ProcessRouteStep>();

        /// <summary>
        /// 工序-物料关联列表
        /// </summary>
        public ICollection<ProcessStepMaterial> ProcessStepMaterials { get; set; } = new List<ProcessStepMaterial>();

        /// <summary>
        /// 子BOM的工序
        /// </summary>
        public Guid? SubBomId { get; set; }
        public BOM.BomInfo? SubBom { get; set; }
    }

    /// <summary>
    /// 工序状态枚举
    /// </summary>
    public enum StepStatus
    {
        /// <summary>
        /// 启用
        /// </summary> 
        Enabled = 1,
        /// <summary>
        /// 禁用
        /// </summary>
        Disabled = 0
    }
}
