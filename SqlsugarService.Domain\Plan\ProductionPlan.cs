﻿using SqlSugar;
using SqlsugarService.Domain.BOM;
using SqlsugarService.Domain.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Plan
{
    /// <summary>
    /// 生产计划基础表
    /// </summary>
    public class ProductionPlan : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 计划编号
        /// </summary>
        public string PlanNumber { get; set; }

        /// <summary>
        /// 计划名称
        /// </summary>
        public string PlanName { get; set; }

        /// <summary>
        /// 关联的BOM主键Id（指定本次生产所用BOM）
        /// </summary>
        public Guid BomId { get; set; }

        /// <summary>
        /// 关联的BOM对象（导航属性）
        /// </summary>
        //public BomInfo Bom { get; set; }

        /// <summary>
        /// 来源类型
        /// </summary>
        public string SourceType { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 成品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 成品编号
        /// </summary>
        public string ProductNumber { get; set; }

        /// <summary>
        /// 产品类型
        /// </summary>
        public string ProductType { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        public DateTime PlanStartTime { get; set; }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        public DateTime PlanEndTime { get; set; }

        /// <summary>
        /// 需求日期
        /// </summary>
        public DateTime RequiredDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 状态 未分解0 已分解1 已完成2 已关闭3 已撤回4 进行中5
        /// </summary> 
        public int Status { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public string? Attachment { get; set; }
    }
}
