using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.AI.KouZi_AI.Dtos
{
    /// <summary>
    /// 提交工具执行结果请求DTO - 工具执行结果的完整提交载体
    /// 
    /// 此DTO封装了向扣子AI提交工具执行结果的完整请求数据：
    /// 
    /// 核心功能：
    /// - 批量提交：支持一次提交多个工具的执行结果
    /// - 会话关联：通过ChatId和ConversationId关联到特定对话
    /// - 响应控制：通过Stream参数控制后续响应方式
    /// - 状态恢复：让AI从中断状态恢复并继续处理
    /// 
    /// 工作流程：
    /// 1. AI发起工具调用 → 返回requires_action状态
    /// 2. 客户端执行工具操作 → 收集执行结果
    /// 3. 构建SubmitToolOutputsRequestDto → 调用提交接口
    /// 4. AI接收结果并继续处理 → 返回最终回复
    /// 
    /// 使用场景：
    /// - IoT设备控制：控制智能家居设备后提交操作结果
    /// - 文件处理：文件上传、下载、转换等操作的结果提交
    /// - 外部API集成：调用第三方API后将结果返回给AI
    /// - 数据库操作：查询、更新、删除等操作的结果反馈
    /// - 系统交互：与操作系统或其他服务交互的结果
    /// 
    /// 技术特点：
    /// - 异步处理：支持工具的异步执行和结果提交
    /// - 并发支持：可以并发执行多个工具并批量提交结果
    /// - 状态管理：维护对话的连续性和上下文
    /// - 错误恢复：支持工具执行失败后的错误处理
    /// 
    /// 安全考虑：
    /// - 权限验证：确保只有授权的客户端可以提交结果
    /// - 数据验证：验证工具调用ID的有效性和权限
    /// - 结果过滤：过滤敏感信息避免泄露
    /// - 超时处理：处理工具执行超时的情况
    /// </summary>
    public class SubmitToolOutputsRequestDto
    {
        /// <summary>
        /// 聊天会话标识符 - 标识具体的对话会话
        /// 
        /// 会话标识的重要性：
        /// - 对话定位：精确定位到需要恢复的对话会话
        /// - 上下文维护：确保工具执行结果在正确的对话上下文中处理
        /// - 状态恢复：让AI从requires_action状态恢复到正常处理状态
        /// - 并发隔离：在多个并发对话中正确分发工具执行结果
        /// 
        /// 获取方式：
        /// - 初始对话：从发起对话接口的响应中获取
        /// - 流式响应：从conversation.chat.created事件的data.id字段获取
        /// - 非流式响应：从响应的data.id字段获取
        /// - 查询接口：从查询对话详情接口获取
        /// 
        /// 使用注意：
        /// - 精确匹配：必须使用AI返回的确切ChatId
        /// - 时效性：ChatId有一定的有效期，过期后无法使用
        /// - 权限验证：确保当前用户有权限访问该ChatId
        /// - 状态检查：确保对话确实处于requires_action状态
        /// 
        /// 技术实现：
        /// - 格式验证：验证ChatId的格式正确性
        /// - 存在性检查：验证ChatId在系统中是否存在
        /// - 状态验证：验证对话是否处于可接受工具结果的状态
        /// - 权限控制：基于用户身份验证访问权限
        /// </summary>
        [Required(ErrorMessage = "聊天ID不能为空")]
        public string ChatId { get; set; }
        
        /// <summary>
        /// 对话上下文标识符 - 标识更高层次的对话会话
        /// 
        /// 对话上下文的作用：
        /// - 会话连续性：维护跨多轮对话的上下文连续性
        /// - 数据关联：将工具执行结果关联到正确的对话上下文
        /// - 历史维护：保持对话历史的完整性和一致性
        /// - 个性化服务：基于对话历史提供个性化的后续处理
        /// 
        /// 与ChatId的关系：
        /// - 层次结构：ConversationId通常包含多个ChatId
        /// - 生命周期：ConversationId的生命周期通常比ChatId更长
        /// - 数据组织：ConversationId用于组织相关的对话数据
        /// - 上下文管理：ConversationId维护对话的完整上下文
        /// 
        /// 获取方式：
        /// - 对话响应：从对话接口响应的conversation_id字段获取
        /// - 流式事件：从相关流式事件的conversation_id字段获取
        /// - 查询接口：从查询对话或消息接口获取
        /// - 客户端存储：在客户端持久化存储以维持会话
        /// 
        /// 使用场景：
        /// - 多轮对话：在包含多次工具调用的复杂对话中
        /// - 上下文恢复：当需要恢复中断的对话上下文时
        /// - 历史查询：查询相关的历史对话和工具调用记录
        /// - 个性化处理：基于历史上下文的个性化响应
        /// 
        /// 技术考虑：
        /// - 一致性：确保ConversationId与ChatId的关联关系正确
        /// - 有效性：验证ConversationId是否仍然有效
        /// - 权限：确保用户有权限访问该对话上下文
        /// - 完整性：维护对话上下文数据的完整性
        /// </summary>
        [Required(ErrorMessage = "会话ID不能为空")]
        public string ConversationId { get; set; }
        
        /// <summary>
        /// 工具执行结果列表 - 批量工具执行结果的集合
        /// 
        /// 批量处理的优势：
        /// - 效率提升：一次性提交多个工具结果，减少网络往返
        /// - 原子性：多个相关工具的结果可以作为一个整体提交
        /// - 一致性：确保相关工具结果的一致性和完整性
        /// - 性能优化：减少API调用次数，提升整体性能
        /// 
        /// 工具结果组织：
        /// - 顺序性：工具结果的顺序可能影响AI的处理逻辑
        /// - 关联性：相关的工具结果应该一起提交
        /// - 依赖性：有依赖关系的工具结果需要按正确顺序组织
        /// - 完整性：确保所有必需的工具结果都包含在列表中
        /// 
        /// 数据验证：
        /// - 非空验证：列表不能为空，至少包含一个工具结果
        /// - 内容验证：每个工具结果都必须包含有效的数据
        /// - 唯一性验证：同一个ToolCallId不能重复出现
        /// - 格式验证：每个工具结果的格式必须符合要求
        /// 
        /// 使用场景：
        /// 
        /// 单工具场景：
        /// - 简单查询：如天气查询、用户信息查询
        /// - 单一操作：如开关灯、发送消息
        /// - 独立任务：如文件上传、数据计算
        /// 
        /// 多工具场景：
        /// - 复合操作：如"查询用户信息并发送通知"
        /// - 并行任务：如同时控制多个设备
        /// - 流水线处理：如"下载文件 → 处理数据 → 生成报告"
        /// - 错误恢复：如"尝试主服务器 → 失败后尝试备用服务器"
        /// 
        /// 最佳实践：
        /// - 及时提交：工具执行完成后尽快提交结果
        /// - 完整信息：确保每个工具结果包含完整的信息
        /// - 错误处理：明确标识执行失败的工具和失败原因
        /// - 性能考虑：合理控制批量提交的数量和频率
        /// - 日志记录：记录工具执行和结果提交的详细日志
        /// 
        /// 技术实现：
        /// - 集合初始化：默认初始化为空列表
        /// - 类型安全：使用强类型确保数据一致性
        /// - 序列化：支持JSON序列化到扣子AI API
        /// - 验证机制：实现完整的数据验证逻辑
        /// </summary>
        [Required(ErrorMessage = "工具执行结果列表不能为空")]
        [MinLength(1, ErrorMessage = "至少需要提交一个工具执行结果")]
        public List<ToolOutputDto> ToolOutputs { get; set; } = new List<ToolOutputDto>();
        
        /// <summary>
        /// 流式响应控制标志 - 决定后续响应的处理方式
        /// 
        /// 响应方式的影响：
        /// 
        /// Stream = true（流式响应）：
        /// - 实时处理：AI接收到工具结果后立即开始流式处理
        /// - 上下文填充：自动填充之前对话中的上下文信息
        /// - 连续响应：继续之前中断的对话流程
        /// - 用户体验：用户可以实时看到AI基于工具结果的处理过程
        /// - 适用场景：需要实时反馈的交互式场景
        /// 
        /// Stream = false（非流式响应）：
        /// - 简单响应：只返回对话的基本信息和状态
        /// - 批处理：适合批处理或后台处理场景
        /// - 简化集成：减少客户端处理流式数据的复杂性
        /// - 稳定性：网络不稳定时的更可靠选择
        /// - 适用场景：简单的工具结果提交，不需要复杂的后续处理
        /// 
        /// 选择建议：
        /// 
        /// 推荐使用Stream = true的场景：
        /// - 用户等待AI基于工具结果继续对话
        /// - 需要实时显示AI的处理进度
        /// - 工具结果可能触发复杂的后续处理
        /// - 交互式应用场景
        /// 
        /// 推荐使用Stream = false的场景：
        /// - 只需要确认工具结果已提交
        /// - 后续处理通过其他方式查询
        /// - 批处理或自动化场景
        /// - 网络环境不稳定的情况
        /// 
        /// 技术实现：
        /// - 默认值：根据业务需求设置合理的默认值
        /// - 动态选择：可以根据具体场景动态选择响应方式
        /// - 性能考虑：考虑不同响应方式对性能的影响
        /// - 错误处理：不同响应方式的错误处理策略
        /// 
        /// 与其他参数的关系：
        /// - 与ToolOutputs：工具结果的复杂程度可能影响响应方式选择
        /// - 与会话状态：当前会话状态可能影响最佳响应方式
        /// - 与客户端能力：客户端的处理能力影响响应方式选择
        /// - 与业务场景：具体的业务场景决定最适合的响应方式
        /// </summary>
        public bool Stream { get; set; } = true;
    }
}