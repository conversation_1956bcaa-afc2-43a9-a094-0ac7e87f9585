# SqlSugar自动创建数据库项目

## 项目概述

这是一个使用SqlSugar ORM框架的.NET 6 Web API项目，具备自动创建数据库和表的功能。

## 功能特性

- ✅ 自动创建表结构（基于实体类）
- ✅ 自动初始化种子数据
- ✅ 支持软删除
- ✅ 完整的审计字段
- ✅ 详细的日志记录
- ✅ RESTful API接口
- ✅ 数据库配置检查

## 快速开始

### 1. 配置数据库连接

在 `SqlsugarService.API/appsettings.json` 中配置数据库连接字符串：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=sqlsugarservice;Username=admin;Password=******;SearchPath=public"
  }
}
```

### 2. 启动应用

```bash
dotnet run --project SqlsugarService.API
```

应用启动时会自动：
- 创建表结构（如果不存在）
- 初始化种子数据

### 3. 测试功能

#### 检查数据库配置
```bash
curl http://localhost:5000/api/test/connection
```

#### 查看数据库状态
```bash
curl http://localhost:5000/api/database/status
```

#### 测试创建用户
```bash
curl -X POST http://localhost:5000/api/test/create-user
```

#### 查看所有用户
```bash
curl http://localhost:5000/api/users
```

## API接口

### 数据库管理
- `POST /api/database/initialize` - 初始化数据库
- `POST /api/database/reset` - 重置数据库
- `GET /api/database/status` - 获取数据库状态

### 用户管理
- `GET /api/users` - 获取所有用户
- `GET /api/users/{id}` - 根据ID获取用户
- `POST /api/users` - 创建新用户
- `PUT /api/users/{id}` - 更新用户信息
- `DELETE /api/users/{id}` - 删除用户（软删除）

### 测试接口
- `GET /api/test/connection` - 检查数据库配置
- `POST /api/test/create-user` - 测试创建用户
- `GET /api/test/users` - 测试查询用户
- `GET /api/test/db-info` - 获取数据库信息

## 项目结构

```
SqlsugarService/
├── SqlsugarService.API/           # Web API层
│   ├── Controllers/               # 控制器
│   ├── Program.cs                 # 应用启动配置
│   └── appsettings.json          # 配置文件
├── SqlsugarService.Domain/        # 领域层
│   ├── Users.cs                   # 用户实体
│   └── Common/
│       └── BaseEntity.cs          # 基础实体类
├── SqlsugarService.Infrastructure/ # 基础设施层
│   └── DbContext/
│       └── SqlsugarDbContext.cs   # SqlSugar数据库上下文
└── SqlsugarService.Application/   # 应用层
```

## 添加新实体

1. 在 `SqlsugarService.Domain` 中创建新的实体类
2. 继承 `BaseEntity`
3. 使用 `[SugarTable]` 特性指定表名
4. 在 `SqlsugarDbContext.cs` 的 `entityTypes` 数组中添加新实体类型

示例：
```csharp
[SugarTable("products")]
public class Product : BaseEntity
{
    [SugarColumn(IsPrimaryKey = true)]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public string Name { get; set; } = string.Empty;
    public decimal Price { get; set; }
}
```

## 错误修复

### 已修复的问题

1. **编译错误**：修复了SqlSugar 5.x版本的方法调用错误
2. **连接测试**：简化了数据库连接测试，避免使用有问题的API
3. **异步方法**：修复了异步方法调用的问题
4. **错误处理**：完善了异常处理和日志记录

### 主要改进

- 移除了有问题的`DbMaintenance`方法调用
- 简化了数据库初始化逻辑
- 改进了错误处理和日志记录
- 提供了更安全的测试接口

## 注意事项

1. **数据库权限**：确保数据库用户有创建表的权限
2. **连接字符串**：确保连接字符串正确且数据库服务器可访问
3. **生产环境**：在生产环境中建议手动管理数据库结构
4. **数据备份**：在执行重置操作前请备份重要数据

## 故障排除

### 常见问题

1. **连接失败**：检查连接字符串和网络连接
2. **权限不足**：确保数据库用户有足够权限
3. **表创建失败**：检查实体类配置和数据库权限

### 日志查看

应用会输出详细的日志信息，包括：
- SQL语句执行日志
- 数据库初始化过程
- 错误信息

## 技术栈

- .NET 6
- SqlSugar ORM 5.1.4.198
- PostgreSQL
- ASP.NET Core Web API

## 许可证

MIT License 