# BOM树形查询API文档

## 概述

本文档描述了BOM（物料清单）树形查询API的使用方法。该API通过导航属性实现BomItem与BomInfo、MaterialEntity、ChildItems以及产品表的联查，生成树形结构的下拉列表。

## 功能特性

- ✅ 支持完整的BOM树形结构查询
- ✅ 通过导航属性实现多表联查
- ✅ 树形名称为产品名称或物料名称
- ✅ 支持按BOM ID过滤查询
- ✅ 包含详细的节点信息（用量、单位、投入产出类型等）
- ✅ 递归构建多层级树形结构

## API端点

### 1. 获取所有BOM树形下拉列表

**接口地址：** `GET /api/ProductionPlans/bom-tree`

**请求参数：** 无

**响应示例：**
```json
{
  "isSuc": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "产品A (BOM001)",
      "nodeType": "BOM",
      "productName": "产品A",
      "productNumber": "BOM001",
      "materialName": null,
      "materialNumber": null,
      "quantity": 0,
      "unit": "个",
      "inOutType": "投入",
      "children": [
        {
          "id": "550e8400-e29b-41d4-a716-446655440001",
          "name": "物料B (MAT001) - 用量: 2 个",
          "nodeType": "物料",
          "productName": null,
          "productNumber": null,
          "materialName": "物料B",
          "materialNumber": "MAT001",
          "quantity": 2,
          "unit": "个",
          "inOutType": "投入",
          "children": []
        }
      ]
    }
  ]
}
```

### 2. 根据BOM ID获取树形下拉列表

**接口地址：** `GET /api/ProductionPlans/bom-tree/{bomId}`

**路径参数：**
- `bomId` (Guid): BOM主键ID

**响应格式：** 与上述接口相同

## 数据结构说明

### TreeNodeDto

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Guid | 节点ID（BomItem的ID） |
| name | string | 节点显示名称 |
| nodeType | string | 节点类型（BOM、产品、物料） |
| productName | string? | 产品名称（来自BomInfo） |
| productNumber | string? | 产品编号（来自BomInfo） |
| materialName | string? | 物料名称（来自MaterialEntity） |
| materialNumber | string? | 物料编号（来自MaterialEntity） |
| quantity | decimal? | 用量 |
| unit | string? | 单位 |
| inOutType | string? | 投入产出类型 |
| children | List<TreeNodeDto> | 子节点列表 |

### 节点类型说明

- **BOM**: 来自BomInfo的产品信息
- **产品**: MaterialEntity中MaterialType为Product的记录
- **物料**: MaterialEntity中MaterialType为Material的记录

### 投入产出类型

- **投入**: MaterialRelationType.Input
- **产出**: MaterialRelationType.Output
- **副产品**: MaterialRelationType.ByProduct
- **废料**: MaterialRelationType.Scrap
- **可回收**: MaterialRelationType.Recycled

## 节点名称生成规则

1. **优先显示产品名称**：如果存在BomInfo关联，显示"产品名称 (BOM编号)"
2. **其次显示物料名称**：如果存在MaterialEntity关联，显示"物料名称 (物料编号) - 用量: X 单位"
3. **最后显示BOM编号**：如果只有BomInfo，显示"BOM: BOM编号"
4. **兜底显示**：显示"BOM项目 {ID}"

## 使用示例

### 前端调用示例（JavaScript）

```javascript
// 获取所有BOM树形数据
async function getAllBomTree() {
  try {
    const response = await fetch('/api/ProductionPlans/bom-tree');
    const result = await response.json();
    
    if (result.isSuc) {
      console.log('BOM树形数据:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求异常:', error);
  }
}

// 根据BOM ID获取树形数据
async function getBomTreeByBomId(bomId) {
  try {
    const response = await fetch(`/api/ProductionPlans/bom-tree/${bomId}`);
    const result = await response.json();
    
    if (result.isSuc) {
      console.log('指定BOM树形数据:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求异常:', error);
  }
}
```

### 前端树形组件使用示例

```javascript
// 使用Element UI的Tree组件
<template>
  <el-tree
    :data="bomTreeData"
    :props="defaultProps"
    node-key="id"
    default-expand-all>
  </el-tree>
</template>

<script>
export default {
  data() {
    return {
      bomTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  async mounted() {
    this.bomTreeData = await getAllBomTree();
  }
}
</script>
```

## 注意事项

1. **性能考虑**：该API会加载所有BOM项目的导航属性，数据量较大时建议使用按BOM ID过滤的接口
2. **数据完整性**：确保BomItem、BomInfo、MaterialEntity等表的数据完整性
3. **导航属性**：依赖SqlSugar的导航属性功能，确保实体配置正确
4. **错误处理**：API会返回详细的错误信息，前端应做好异常处理

## 技术实现

### 核心查询逻辑

```csharp
// 查询所有 BOM 项目，包含导航属性
var bomItems = await bomItemBase.AsQueryable()
    .Includes(bi => bi.Bom)  // 包含BOM信息
    .Includes(bi => bi.Material)  // 包含物料信息
    .Includes(bi => bi.ChildItems)  // 包含子项目
    .ToListAsync();
```

### 树形构建算法

```csharp
private List<TreeNodeDto> BuildTree(IEnumerable<BomItem> bomItems, Guid? parentId)
{
    return bomItems
        .Where(item => item.ParentItemId == parentId)
        .Select(item => new TreeNodeDto
        {
            Id = item.Id,
            Name = GetNodeName(item),
            NodeType = GetNodeType(item),
            // ... 其他属性
            Children = BuildTree(bomItems, item.Id)
        })
        .ToList();
}
```

## 更新日志

- **v1.0.0**: 初始版本，支持基本的BOM树形查询
- **v1.1.0**: 添加按BOM ID过滤功能
- **v1.2.0**: 优化节点名称生成规则，增加更多节点信息 