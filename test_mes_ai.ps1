# MES智能体测试脚本
Write-Host "🚀 开始测试MES咨询专家智能体..." -ForegroundColor Green

$baseUrl = "http://localhost:64922"

Write-Host "`n📋 1. 测试健康检查..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/health-check" -Method Get
    Write-Host "✅ 健康检查结果: $healthResponse" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n📋 2. 测试快速发送接口..." -ForegroundColor Yellow
try {
    $quickMessage = "我们是一家汽车零部件制造企业，想了解MES系统能为我们带来什么价值？"

    $quickResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/quick-send?userId=test_user_001" -Method Post -ContentType "application/json" -Body (ConvertTo-Json $quickMessage)

    Write-Host "✅ 快速发送成功!" -ForegroundColor Green
    Write-Host "📝 AI回复: $($quickResponse.content)" -ForegroundColor Cyan
    Write-Host "🆔 会话ID: $($quickResponse.sessionId)" -ForegroundColor Magenta
} catch {
    Write-Host "❌ 快速发送失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 测试完成！" -ForegroundColor Green
Write-Host "💡 提示：你可以在Swagger UI中继续测试更多功能" -ForegroundColor Yellow
Write-Host "🌐 Swagger地址: $baseUrl/swagger" -ForegroundColor Blue
