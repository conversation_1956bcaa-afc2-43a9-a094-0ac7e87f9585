﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using SqlsugarService.Application.IService;
using SqlsugarService.Application.Service;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain;

namespace SqlsugarService.API.Controllers
{
    /// <summary>
    /// 用户管理
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService;
        }
        /// <summary>
        /// 获取所有用户
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ApiResult<List<Users>>> GetUsers()
        {
            return await _userService.GetUsers();
        }
        /// <summary>
        /// 分页获取用户
        /// </summary>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<Users>>>> GetUserspage([FromQuery]Seach? seach)
        {
            return await _userService.GetUserspage(seach);
        }
        /// <summary>
        /// 添加用户
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ApiResult> AddUser([FromBody] Users user)
        {
            return await _userService.AddUser(user);
        }
    }
}
