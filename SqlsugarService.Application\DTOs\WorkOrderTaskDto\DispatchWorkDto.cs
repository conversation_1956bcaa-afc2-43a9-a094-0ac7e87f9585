using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.WorkOrderTaskDto
{
    /// <summary>
    /// 派工信息DTO
    /// </summary>
    public class DispatchWorkDto
    {
        /// <summary>
        /// 工单任务ID
        /// </summary>
        [Required(ErrorMessage = "工单任务ID不能为空")]
        public Guid WorkOrderTaskEntityId { get; set; }

        /// <summary>
        /// 班组名称
        /// </summary>
        [Required(ErrorMessage = "班组名称不能为空")]
        [StringLength(100, ErrorMessage = "班组名称长度不能超过100个字符")]
        public string TeamName { get; set; } = string.Empty;

        /// <summary>
        /// 负责人名称
        /// </summary>
        [Required(ErrorMessage = "负责人名称不能为空")]
        [StringLength(50, ErrorMessage = "负责人名称长度不能超过50个字符")]
        public string Teamprincipal { get; set; } = string.Empty;

        /// <summary>
        /// 其他成员
        /// </summary>
        [StringLength(500, ErrorMessage = "其他成员信息长度不能超过500个字符")]
        public string OtherMembers { get; set; } = string.Empty;

        /// <summary>
        /// 质检部门
        /// </summary>
        [StringLength(100, ErrorMessage = "质检部门名称长度不能超过100个字符")]
        public string QualityTestingDept { get; set; } = string.Empty;

        /// <summary>
        /// 质检人员
        /// </summary>
        [StringLength(100, ErrorMessage = "质检人员名称长度不能超过100个字符")]
        public string QualityTestingPeople { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string Descr { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量派工请求DTO
    /// </summary>
    public class BatchDispatchWorkDto
    {
        /// <summary>
        /// 派工信息列表
        /// </summary>
        [Required(ErrorMessage = "派工信息列表不能为空")]
        public List<DispatchWorkDto> DispatchWorks { get; set; } = new List<DispatchWorkDto>();
    }
}
