# 🤖 MES LangChain 智能工具调用测试指南

## 📋 概述

本指南将帮助您测试新的MES LangChain智能工具调用功能。该功能使用Microsoft Semantic Kernel，能够自动理解用户意图并调用相应的MES工具。

## 🎯 核心特性

### ✨ 自动工具调用
- **智能理解**：AI能理解自然语言中的查询需求
- **自动选择**：根据用户意图自动选择合适的工具
- **无缝集成**：工具调用对用户完全透明
- **专业回复**：基于工具结果提供专业分析

### 🔧 可用工具
1. **QueryProductionOrders** - 查询生产订单
2. **QueryMaterialInventory** - 查询物料库存
3. **QuerySalesOrders** - 查询销售订单
4. **QueryBOMInfo** - 查询BOM信息
5. **GenerateProductionReport** - 生成生产报表

## 🚀 快速开始

### 1. 启动服务
```bash
cd SqlsugarService.API
dotnet run
```

### 2. 访问Swagger
打开浏览器访问：`http://localhost:5000/swagger`

### 3. 找到测试接口
- 主接口：`POST /api/mesassistant/chat` (LangChain模式)
- 备用接口：`POST /api/mesassistant/chat-kouzi` (扣子AI模式)

## 📝 测试用例

### 🔍 基础查询测试

#### 测试1：生产订单查询
```json
{
  "message": "查询今天的生产订单",
  "userId": "test_user",
  "conversationId": "test_001"
}
```

**预期行为**：
- AI自动调用QueryProductionOrders工具
- 返回今天的生产订单数据
- 提供订单状态分析

#### 测试2：物料库存查询
```json
{
  "message": "查询物料A001的库存情况",
  "userId": "test_user",
  "conversationId": "test_001"
}
```

**预期行为**：
- AI自动调用QueryMaterialInventory工具
- 返回物料A001的库存信息
- 分析库存状态并给出建议

#### 测试3：销售订单查询
```json
{
  "message": "查询客户ABC公司的订单",
  "userId": "test_user",
  "conversationId": "test_001"
}
```

**预期行为**：
- AI自动调用QuerySalesOrders工具
- 返回ABC公司的销售订单
- 提供订单分析

### 🧠 智能理解测试

#### 测试4：模糊查询
```json
{
  "message": "最近有什么订单需要处理？",
  "userId": "test_user",
  "conversationId": "test_001"
}
```

**预期行为**：
- AI理解"最近"和"需要处理"的含义
- 自动查询近期的生产订单
- 筛选出需要关注的订单

#### 测试5：复合查询
```json
{
  "message": "查询产品P001的BOM信息，并检查相关物料的库存",
  "userId": "test_user",
  "conversationId": "test_001"
}
```

**预期行为**：
- AI理解需要两步操作
- 先调用QueryBOMInfo查询BOM
- 再调用QueryMaterialInventory查询库存
- 综合分析结果

### 📊 报表生成测试

#### 测试6：生产报表
```json
{
  "message": "生成本周的生产统计报表",
  "userId": "test_user",
  "conversationId": "test_001"
}
```

**预期行为**：
- AI自动调用GenerateProductionReport工具
- 设置reportType为"weekly"
- 返回本周生产统计数据

### 🗣️ 对话连续性测试

#### 测试7：上下文理解
```json
// 第一轮对话
{
  "message": "查询订单PO001的信息",
  "userId": "test_user",
  "conversationId": "test_002"
}

// 第二轮对话
{
  "message": "这个订单的物料准备好了吗？",
  "userId": "test_user",
  "conversationId": "test_002"
}
```

**预期行为**：
- 第一轮：查询PO001订单信息
- 第二轮：AI理解"这个订单"指PO001，查询相关物料库存

## 🔧 高级测试

### 测试8：错误处理
```json
{
  "message": "查询不存在的订单XYZ999",
  "userId": "test_user",
  "conversationId": "test_003"
}
```

**预期行为**：
- AI调用查询工具
- 优雅处理"未找到"的情况
- 提供友好的错误提示

### 测试9：参数推理
```json
{
  "message": "查询上个月的生产报表",
  "userId": "test_user",
  "conversationId": "test_003"
}
```

**预期行为**：
- AI自动计算上个月的日期范围
- 设置正确的startDate和endDate参数
- 调用GenerateProductionReport工具

## 📈 性能测试

### 测试10：并发请求
使用工具（如Postman）同时发送多个请求，测试系统的并发处理能力。

### 测试11：长对话
在同一个conversationId下进行多轮对话，测试内存管理和性能。

## 🐛 故障排除

### 常见问题

#### 1. 工具调用失败
**症状**：AI回复但没有调用工具
**解决**：
- 检查LangChain配置
- 确认API密钥正确
- 查看日志中的错误信息

#### 2. 响应时间过长
**症状**：请求超时或响应很慢
**解决**：
- 检查AI服务连接
- 优化工具实现
- 调整超时设置

#### 3. 工具参数错误
**症状**：工具调用但参数不正确
**解决**：
- 检查工具描述是否清晰
- 优化参数定义
- 改进提示词

## 📊 测试结果记录

### 成功指标
- [ ] 基础查询功能正常
- [ ] 智能理解准确
- [ ] 工具自动调用
- [ ] 错误处理优雅
- [ ] 对话连续性良好
- [ ] 性能满足要求

### 测试记录模板
```
测试时间：____
测试用例：____
输入消息：____
预期结果：____
实际结果：____
是否通过：[ ] 是 [ ] 否
备注：____
```

## 🎉 总结

通过以上测试，您可以全面验证MES LangChain智能工具调用功能。这个功能将大大提升用户体验，让MES系统的使用更加智能和便捷。

如果遇到问题，请查看日志文件或联系开发团队获取支持。
