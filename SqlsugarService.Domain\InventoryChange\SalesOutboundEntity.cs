using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Materials;
using SqlsugarService.Domain.Warehouse;
using System;
using System.Collections.Generic;

namespace SqlsugarService.Domain.InventoryChange
{
    /// <summary>
    /// 销售出库单实体类
    /// </summary>
    [SugarTable("sales_outbound")]
    public class SalesOutboundEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 出库单编号
        /// </summary>
        public string OutboundCode { get; set; } = string.Empty;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemCode { get; set; } = true;

        /// <summary>
        /// 出库单名称
        /// </summary>
        public string OutboundName { get; set; } = string.Empty;

        /// <summary>
        /// 出货检验单号
        /// </summary>
        public string? InspectionCode { get; set; } = string.Empty;

        /// <summary>
        /// 出货检验单号（系统生成）
        /// </summary>
        public string? InspectionSystemCode { get; set; } = string.Empty;

        /// <summary>
        /// 出库日期
        /// </summary>
        public DateTime OutboundDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 销售单号
        /// </summary>
        public string? SalesCode { get; set; } = string.Empty;

        /// <summary>
        /// 销售单名称
        /// </summary>
        public string SalesName { get; set; } = string.Empty;
        /// <summary>
        /// 销售人
        /// </summary>
        public string Salesperson { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 出库状态（0-草稿，1-已确认，2-已取消）
        /// </summary>
        public int Status { get; set; } = 0;

        /// <summary>
        /// 仓库ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public Guid? WarehouseId { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string? WarehouseName { get; set; } = string.Empty;

        /// <summary>
        /// 仓库负责人
        /// </summary>
        public string? WarehouseManager { get; set; } = string.Empty;

        /// <summary>
        /// 导航属性 - 仓库信息
        /// </summary>
        [Navigate(NavigateType.ManyToOne, nameof(WarehouseId))]
        public WarehouseEntity? Warehouse { get; set; }

        /// <summary>
        /// 导航属性 - 出库明细
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(SalesOutboundDetailEntity.OutboundId))]
        public List<SalesOutboundDetailEntity> OutboundDetails { get; set; } = new List<SalesOutboundDetailEntity>();

        
    }
}