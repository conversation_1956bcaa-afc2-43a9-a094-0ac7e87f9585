using System;

namespace SqlsugarService.Application.AI.KouZi_AI.Dtos
{
    /// <summary>
    /// 提交工具执行结果响应DTO - 工具结果提交后的响应数据载体
    /// 
    /// 此DTO封装了向扣子AI提交工具执行结果后的响应信息：
    /// 
    /// 响应数据特点：
    /// - 状态确认：确认工具结果已成功提交并被AI接收
    /// - 会话恢复：提供会话恢复后的基本状态信息
    /// - 处理指示：指示AI是否已开始基于工具结果的后续处理
    /// - 元数据提供：包含必要的元数据用于后续操作
    /// 
    /// 业务价值：
    /// - 确认机制：确保工具执行结果已被AI正确接收
    /// - 状态跟踪：跟踪对话从中断到恢复的状态变化
    /// - 错误诊断：在提交失败时提供详细的错误信息
    /// - 后续操作：为后续的对话操作提供必要的信息
    /// 
    /// 使用场景：
    /// - 结果确认：确认工具执行结果提交成功
    /// - 状态监控：监控对话状态的恢复情况
    /// - 错误处理：处理工具结果提交过程中的错误
    /// - 流程控制：控制后续的对话流程
    /// 
    /// 技术特性：
    /// - 统一格式：与其他扣子AI响应保持一致的格式
    /// - 完整信息：包含必要的会话和状态信息
    /// - 扩展性：支持未来功能的扩展和增强
    /// - 兼容性：保持与现有API的兼容性
    /// </summary>
    public class SubmitToolOutputsResponseDto
    {
        /// <summary>
        /// 聊天会话标识符 - 确认处理的会话身份
        /// 
        /// 身份确认的重要性：
        /// - 结果验证：确认响应对应的是正确的聊天会话
        /// - 状态关联：将响应状态与特定的聊天会话关联
        /// - 并发处理：在多个并发会话中正确识别响应归属
        /// - 数据完整性：维护会话数据的完整性和一致性
        /// 
        /// 与请求的对应：
        /// - 一致性检查：应该与请求中的ChatId完全一致
        /// - 数据验证：客户端可以验证响应的正确性
        /// - 状态同步：确保客户端状态与服务端状态同步
        /// - 错误排查：便于定位和排查问题
        /// 
        /// 使用方式：
        /// - 状态更新：更新客户端中对应会话的状态
        /// - 数据关联：关联后续的操作和数据
        /// - 缓存键值：作为缓存和存储的键值
        /// - 日志记录：在日志中记录相关的会话信息
        /// </summary>
        public string? ChatId { get; set; }
        
        /// <summary>
        /// 对话上下文标识符 - 确认处理的对话上下文
        /// 
        /// 上下文确认的作用：
        /// - 上下文验证：确认对话上下文已正确恢复
        /// - 连续性保证：保证对话上下文的连续性
        /// - 历史关联：将当前处理与历史对话关联
        /// - 个性化维护：维护基于上下文的个性化状态
        /// 
        /// 业务应用：
        /// - 会话管理：管理和组织相关的对话会话
        /// - 上下文恢复：在需要时恢复对话上下文
        /// - 历史查询：查询相关的历史对话记录
        /// - 状态同步：同步对话上下文的状态信息
        /// </summary>
        public string? ConversationId { get; set; }
        
        /// <summary>
        /// 智能体标识符 - 处理工具结果的AI智能体
        /// 
        /// 智能体信息的意义：
        /// - 处理确认：确认是由正确的智能体处理工具结果
        /// - 能力匹配：验证智能体是否具备处理相关工具结果的能力
        /// - 一致性保证：确保整个对话过程使用一致的智能体
        /// - 质量控制：基于智能体的能力评估处理质量
        /// 
        /// 应用场景：
        /// - 智能体验证：验证当前智能体的身份和能力
        /// - 处理策略：根据智能体类型调整处理策略
        /// - 结果评估：基于智能体特性评估处理结果
        /// - 问题排查：定位特定智能体的问题和优化点
        /// </summary>
        public string? BotId { get; set; }
        
        /// <summary>
        /// 对话处理状态 - 工具结果提交后的会话状态
        /// 
        /// 状态类型和含义：
        /// 
        /// "completed" - 处理完成：
        /// - 工具结果已成功处理
        /// - AI已基于工具结果生成最终回复
        /// - 对话可以继续进行新的交互
        /// - 所有相关的处理都已完成
        /// 
        /// "in_progress" - 正在处理：
        /// - 工具结果已接收，AI正在处理中
        /// - 可能需要一定时间完成处理
        /// - 建议客户端显示处理中状态
        /// - 可以通过查询接口获取最新状态
        /// 
        /// "failed" - 处理失败：
        /// - 工具结果处理过程中发生错误
        /// - 需要检查错误信息了解失败原因
        /// - 可能需要重新提交工具结果
        /// - 建议向用户显示错误信息
        /// 
        /// "requires_action" - 需要进一步操作：
        /// - 处理工具结果后又触发了新的工具调用
        /// - 需要客户端执行新的工具操作
        /// - 形成工具调用链的情况
        /// - 需要继续工具执行和结果提交的循环
        /// 
        /// 状态处理策略：
        /// - 实时更新：根据状态更新客户端界面
        /// - 轮询机制：对于in_progress状态可以定期查询
        /// - 错误处理：对于failed状态提供重试机制
        /// - 连续处理：对于requires_action状态继续工具调用流程
        /// 
        /// 业务逻辑：
        /// - UI更新：根据状态更新用户界面显示
        /// - 流程控制：控制后续的业务流程
        /// - 用户反馈：向用户提供当前状态的反馈
        /// - 自动化处理：在适当的情况下自动处理状态变化
        /// </summary>
        public string? Status { get; set; }
        
        /// <summary>
        /// 会话创建时间 - 原始会话的创建时间戳
        /// 
        /// 时间信息的价值：
        /// - 会话追踪：追踪会话从创建到当前的完整时间线
        /// - 性能分析：分析工具调用和处理的时间消耗
        /// - 超时管理：判断会话是否超过预期的处理时间
        /// - 用户体验：向用户显示会话的持续时间
        /// 
        /// 技术规范：
        /// - 时间格式：使用UTC时间戳格式
        /// - 精度要求：提供足够的时间精度
        /// - 一致性：与其他时间字段保持格式一致
        /// - 可读性：支持转换为用户友好的显示格式
        /// </summary>
        public DateTime? CreatedAt { get; set; }
        
        /// <summary>
        /// 会话完成时间 - 处理完成的时间戳（如果已完成）
        /// 
        /// 完成时间的意义：
        /// - 处理确认：确认工具结果处理的完成时间
        /// - 性能指标：计算从工具调用到完成的总时间
        /// - SLA监控：监控服务水平协议的达成情况
        /// - 用户反馈：向用户显示处理完成的时间
        /// 
        /// 数据特征：
        /// - 条件性：只有在status为completed时才有值
        /// - 精确性：提供精确的完成时间戳
        /// - 单调性：一旦设置不应该再变化
        /// - 可空性：未完成状态时为null
        /// </summary>
        public DateTime? CompletedAt { get; set; }
        
        /// <summary>
        /// 会话失败时间 - 处理失败的时间戳（如果失败）
        /// 
        /// 失败时间的用途：
        /// - 错误定位：精确定位问题发生的时间点
        /// - 故障分析：分析故障的时间模式和规律
        /// - 重试策略：制定基于时间的重试策略
        /// - 监控告警：基于失败时间的监控和告警
        /// 
        /// 应用场景：
        /// - 问题排查：结合日志进行问题排查
        /// - 性能优化：分析失败的时间分布
        /// - 用户通知：通知用户处理失败的时间
        /// - 系统监控：监控系统的稳定性和可靠性
        /// </summary>
        public DateTime? FailedAt { get; set; }
        
        /// <summary>
        /// Token使用统计 - 本次处理消耗的Token信息
        /// 
        /// Token统计的重要性：
        /// - 成本控制：监控和控制API调用的成本
        /// - 性能分析：分析处理复杂度和资源消耗
        /// - 配额管理：管理用户或系统的Token使用配额
        /// - 优化指导：指导优化工具结果的格式和内容
        /// 
        /// 统计内容：
        /// - 总消耗：包括处理工具结果和生成回复的总Token数
        /// - 输入Token：处理工具结果消耗的Token数
        /// - 输出Token：生成回复消耗的Token数
        /// - 系统Token：系统处理消耗的Token数
        /// 
        /// 应用价值：
        /// - 计费参考：为精确计费提供数据支持
        /// - 性能优化：优化工具结果的格式减少Token消耗
        /// - 容量规划：为系统容量规划提供数据参考
        /// - 用户反馈：向用户展示资源使用情况
        /// </summary>
        public int? TokensUsed { get; set; }
        
        /// <summary>
        /// 请求处理成功标志 - 工具结果提交操作的状态指示器
        /// 
        /// 成功判断标准：
        /// - true：工具结果已成功提交并被AI接收处理
        /// - false：提交失败，需要检查ErrorMessage了解原因
        /// 
        /// 成功场景：
        /// - 工具结果格式正确且完整
        /// - 会话状态允许接收工具结果
        /// - AI成功处理工具结果并更新状态
        /// - 网络连接正常，API调用成功
        /// 
        /// 失败场景：
        /// - 工具调用ID无效或已过期
        /// - 工具结果格式错误或数据无效
        /// - 会话不在requires_action状态
        /// - 网络连接问题或API服务异常
        /// - 权限不足或认证失败
        /// 
        /// 处理建议：
        /// - 始终检查Success字段再处理其他数据
        /// - 失败时向用户显示ErrorMessage
        /// - 提供重试机制处理临时性失败
        /// - 记录失败详情便于问题诊断
        /// </summary>
        public bool Success { get; set; } = true;
        
        /// <summary>
        /// 错误消息描述 - 详细的错误信息载体
        /// 
        /// 错误信息类型：
        /// - 参数错误："工具调用ID无效或格式错误"
        /// - 状态错误："会话不在允许提交工具结果的状态"
        /// - 权限错误："无权限提交该会话的工具结果"
        /// - 数据错误："工具执行结果格式错误或数据无效"
        /// - 系统错误："服务暂时不可用，请稍后重试"
        /// - 超时错误："工具调用已超时，无法接收结果"
        /// 
        /// 信息设计原则：
        /// - 用户友好：使用易于理解的语言描述问题
        /// - 问题明确：准确指出发生错误的具体环节
        /// - 解决导向：尽可能提供解决问题的建议
        /// - 技术平衡：既有技术细节又考虑普通用户理解
        /// 
        /// 处理策略：
        /// - 直接显示：可以直接向用户显示大部分错误信息
        /// - 分类处理：根据错误类型采取不同的处理措施
        /// - 日志记录：记录完整的错误上下文信息
        /// - 用户指导：提供具体的解决方案和操作建议
        /// 
        /// 安全考虑：
        /// - 信息过滤：避免暴露敏感的系统内部信息
        /// - 攻击防护：不要泄露可被恶意利用的技术细节
        /// - 隐私保护：不要在错误信息中包含用户敏感数据
        /// - 合规要求：符合相关法规对错误处理的要求
        /// </summary>
        public string? ErrorMessage { get; set; }
        
        /// <summary>
        /// 响应创建时间戳 - 响应生成的时间记录
        /// 
        /// 时间戳的作用：
        /// - 响应追踪：追踪响应的生成时间
        /// - 性能分析：分析API响应的速度
        /// - 数据新鲜度：判断响应数据的新鲜程度
        /// - 缓存策略：支持基于时间的缓存策略
        /// 
        /// 技术实现：
        /// - 自动设置：默认使用当前UTC时间
        /// - 精确性：提供毫秒级的时间精度
        /// - 一致性：与其他时间字段保持格式一致
        /// - 不可变：一旦设置不应该修改
        /// </summary>
        public DateTime CreatedAtS { get; set; } = DateTime.UtcNow;
    }
}