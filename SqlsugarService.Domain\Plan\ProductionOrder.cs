﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Plan
{
    /// <summary>
    /// 生产工单（由生产计划分解生成的具体生产任务单）
    /// </summary>
    public class ProductionOrder : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 工单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 工单名称
        /// </summary>
        public string OrderName { get; set; }

        /// <summary>
        /// 关联的生产计划Id
        /// </summary>
        public Guid ProductionPlanId { get; set; }

        /// <summary>
        /// 关联的生产计划对象
        /// </summary>
        
        public ProductionPlan ProductionPlan { get; set; }

        /// <summary>
        /// 产品Id
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        public string ProductNumber { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 计划生产数量
        /// </summary>
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际生产数量
        /// </summary>
        public decimal ActualQuantity { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime PlanStartTime { get; set; }

        /// <summary>
        /// 计划完成时间
        /// </summary>
        public DateTime PlanEndTime { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        public DateTime? ActualStartTime { get; set; }

        /// <summary>
        /// 实际完成时间
        /// </summary>
        public DateTime? ActualEndTime { get; set; }

        /// <summary>
        /// 工单状态（待排产、未开始、进行中、已完成、已暂停、已关闭等）
        /// </summary>
        public string Status { get; set; }
    }
}
