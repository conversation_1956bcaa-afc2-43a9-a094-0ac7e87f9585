# BOM数据调试脚本
# 用于调试BOM的父子关系问题

param(
    [Parameter(Mandatory=$true)]
    [string]$BomId,
    
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== BOM数据调试 ===" -ForegroundColor Green
Write-Host "BOM ID: $BomId" -ForegroundColor Yellow
Write-Host "API地址: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

try {
    # 1. 检查BOM数据是否存在
    Write-Host "1. 检查BOM数据..." -ForegroundColor White
    $checkResponse = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/check-bom-data" -Method Get
    
    if ($checkResponse.isSuc) {
        Write-Host "   BOM数据检查结果:" -ForegroundColor Green
        Write-Host $checkResponse.data -ForegroundColor Gray
    } else {
        Write-Host "   BOM数据检查失败: $($checkResponse.msg)" -ForegroundColor Red
    }

    # 2. 测试BOM结构
    Write-Host "`n2. 测试BOM结构..." -ForegroundColor White
    $testResponse = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/test-bom-structure/$BomId" -Method Get
    
    if ($testResponse.isSuc) {
        Write-Host "   BOM结构测试结果:" -ForegroundColor Green
        Write-Host $testResponse.data -ForegroundColor Gray
    } else {
        Write-Host "   BOM结构测试失败: $($testResponse.msg)" -ForegroundColor Red
    }

    # 3. 获取BOM树形结构
    Write-Host "`n3. 获取BOM树形结构..." -ForegroundColor White
    $treeResponse = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/bom-structure/$BomId" -Method Get
    
    if ($treeResponse.isSuc) {
        $bomTree = $treeResponse.data
        Write-Host "   获取成功!" -ForegroundColor Green
        Write-Host "   根节点数: $($bomTree.Count)" -ForegroundColor Gray
        
        # 分析所有节点
        Write-Host "`n4. 分析所有节点..." -ForegroundColor White
        function Analyze-AllNodes {
            param($nodes, $level = 0)
            foreach ($node in $nodes) {
                $indent = "  " * $level
                Write-Host "$indent节点ID: $($node.id)" -ForegroundColor Yellow
                Write-Host "$indent父节点ID: $($node.parentItemId)" -ForegroundColor Yellow
                Write-Host "$indent显示名称: $($node.displayName)" -ForegroundColor Yellow
                Write-Host "$indent层级: $($node.level)" -ForegroundColor Yellow
                Write-Host "$indent子节点数: $($node.children.Count)" -ForegroundColor Yellow
                Write-Host "$indent可展开: $($node.isExpandable)" -ForegroundColor Yellow
                Write-Host ""
                
                if ($node.children -and $node.children.Count -gt 0) {
                    Analyze-AllNodes -nodes $node.children -level ($level + 1)
                }
            }
        }
        
        Analyze-AllNodes -nodes $bomTree
        
        # 检查父子关系问题
        Write-Host "`n5. 检查父子关系问题..." -ForegroundColor White
        function Check-ParentChildIssues {
            param($nodes)
            $issues = @()
            
            foreach ($node in $nodes) {
                # 检查是否有父节点但父节点不存在
                if (![string]::IsNullOrEmpty($node.parentItemId)) {
                    $parentExists = $false
                    foreach ($potentialParent in $nodes) {
                        if ($potentialParent.id -eq $node.parentItemId) {
                            $parentExists = $true
                            break
                        }
                    }
                    
                    if (-not $parentExists) {
                        $issues += "节点 $($node.id) 的父节点 $($node.parentItemId) 不存在"
                    }
                }
                
                # 递归检查子节点
                if ($node.children -and $node.children.Count -gt 0) {
                    $childIssues = Check-ParentChildIssues -nodes $node.children
                    $issues += $childIssues
                }
            }
            
            return $issues
        }
        
        $issues = Check-ParentChildIssues -nodes $bomTree
        if ($issues.Count -gt 0) {
            Write-Host "   发现以下问题:" -ForegroundColor Red
            foreach ($issue in $issues) {
                Write-Host "     - $issue" -ForegroundColor Red
            }
        } else {
            Write-Host "   未发现父子关系问题" -ForegroundColor Green
        }
        
    } else {
        Write-Host "   获取失败: $($treeResponse.msg)" -ForegroundColor Red
    }

    Write-Host "`n=== 调试完成 ===" -ForegroundColor Green

} catch {
    Write-Host "调试过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误信息: $($_.Exception)" -ForegroundColor Red
} 