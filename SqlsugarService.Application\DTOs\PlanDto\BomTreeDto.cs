namespace SqlsugarService.Application.DTOs.PlanDto
{
    /// <summary>
    /// BOM树形结构DTO
    /// </summary>
    public class BomTreeDto
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int Sequence { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 产品编号
        /// </summary>
        public string ProductNumber { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification { get; set; } = string.Empty;

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// BOM编号
        /// </summary>
        public string BomNumber { get; set; } = string.Empty;

        /// <summary>
        /// BOM版本
        /// </summary>
        public string BomVersion { get; set; } = string.Empty;

        /// <summary>
        /// 使用量
        /// </summary>
        public decimal UsageQuantity { get; set; }

        /// <summary>
        /// 使用比例
        /// </summary>
        public string UsageRatio { get; set; } = string.Empty;

        /// <summary>
        /// 是否可展开（有子节点）
        /// </summary>
        public bool IsExpandable { get; set; }

        /// <summary>
        /// 子节点列表
        /// </summary>
        public List<BomTreeDto> Children { get; set; } = new List<BomTreeDto>();
    }
}