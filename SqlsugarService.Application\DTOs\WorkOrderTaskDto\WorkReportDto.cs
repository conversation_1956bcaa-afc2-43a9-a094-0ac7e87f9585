using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.WorkOrderTaskDto
{
    /// <summary>
    /// 报工DTO
    /// </summary>
    public class WorkReportDto
    {
        /// <summary>
        /// 工单任务ID
        /// </summary>
        [Required(ErrorMessage = "工单任务ID不能为空")]
        public Guid WorkOrderTaskId { get; set; }

        /// <summary>
        /// 检验单号（自动生成）
        /// </summary>
        public string? InspectionCode { get; set; }

        /// <summary>
        /// 检验单名称
        /// </summary>
        [Required(ErrorMessage = "检验单名称不能为空")]
        [StringLength(100, ErrorMessage = "检验单名称长度不能超过100个字符")]
        public string InspectionName { get; set; } = string.Empty;

        /// <summary>
        /// 检验类型 (如：首检, 巡检, 末检)
        /// </summary>
        [Required(ErrorMessage = "检验类型不能为空")]
        public string InspectionType { get; set; } = string.Empty;

        /// <summary>
        /// 产品Id
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 工序Id
        /// </summary>
        public Guid? ProcessStepId { get; set; }

        /// <summary>
        /// 站点Id
        /// </summary>
        public Guid? StationId { get; set; }

        /// <summary>
        /// 班组Id
        /// </summary>
        public Guid? TeamId { get; set; }

        /// <summary>
        /// 报工人员Id
        /// </summary>
        [Required(ErrorMessage = "报工人员ID不能为空")]
        public Guid ReporterId { get; set; }

        /// <summary>
        /// 检验人员Id
        /// </summary>
        public Guid? InspectorId { get; set; }

        /// <summary>
        /// 报工数量
        /// </summary>
        [Required(ErrorMessage = "报工数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "报工数量必须大于0")]
        public int ReportedQuantity { get; set; }

        /// <summary>
        /// 报工时间（默认当前时间）
        /// </summary>
        public DateTime ReportTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 检验时间
        /// </summary>
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        public string? InspectionDepartment { get; set; }

        /// <summary>
        /// 检测数量
        /// </summary>
        public int? TestedQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public int? QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public int? UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 检测结果 (如: 合格, 不合格)
        /// </summary>
        public string? OverallResult { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
