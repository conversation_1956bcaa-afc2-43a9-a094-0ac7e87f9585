using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.AI.LangChain.Dtos
{
    /// <summary>
    /// LangChain请求数据传输对象
    /// </summary>
    public class LangChainRequestDto
    {
        /// <summary>
        /// 用户输入的消息内容
        /// </summary>
        [Required(ErrorMessage = "消息内容不能为空")]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID，用于区分不同用户的对话历史
        /// </summary>
        public string? UserId { get; set; } = "default_user";

        /// <summary>
        /// 会话ID，用于区分同一用户的不同会话
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 是否启用记忆功能，默认启用
        /// </summary>
        public bool EnableMemory { get; set; } = true;

        /// <summary>
        /// 模型名称，如果为空则使用配置中的默认模型
        /// </summary>
        public string? ModelName { get; set; }

        /// <summary>
        /// 温度参数，控制生成文本的随机性，取值范围0-1
        /// </summary>
        public float? Temperature { get; set; }

        /// <summary>
        /// 系统提示词，用于设置AI助手的行为和角色
        /// </summary>
        public string? SystemPrompt { get; set; }
    }
} 