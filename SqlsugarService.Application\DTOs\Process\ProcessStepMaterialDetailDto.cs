using SqlsugarService.Domain.Craftsmanship;
using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.Process
{
    /// <summary>
    /// 工序物料详情数据传输对象
    /// </summary>
    public class ProcessStepMaterialDetailDto
    {
        /// <summary>
        /// 工序Id（必填）
        /// </summary>
        [Required(ErrorMessage = "工序ID不能为空")]
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 物料Id（必填）
        /// </summary>
        [Required(ErrorMessage = "物料ID不能为空")]
        public Guid MaterialId { get; set; }

        /// <summary>
        /// 序号（必填）
        /// </summary>
        [Required(ErrorMessage = "序号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "序号必须大于0")]
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 规格型号（可选）
        /// </summary>
        [StringLength(100, ErrorMessage = "规格型号长度不能超过100个字符")]
        public string? Specification { get; set; }

        /// <summary>
        /// 单位（必填）
        /// </summary>
        [Required(ErrorMessage = "单位不能为空")]
        [StringLength(20, ErrorMessage = "单位长度不能超过20个字符")]
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 使用量（必填）
        /// </summary>
        [Required(ErrorMessage = "使用量不能为空")]
        [Range(0.001, double.MaxValue, ErrorMessage = "使用量必须大于0")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 用料比例（百分比，0-100）
        /// </summary>
        [Range(0, 100, ErrorMessage = "用料比例必须在0-100之间")]
        public decimal UsageRatio { get; set; }

        /// <summary>
        /// 投入产出类型（必填）
        /// </summary>
        [Required(ErrorMessage = "投入产出类型不能为空")]
        public MaterialIOType IOType { get; set; }

        /// <summary>
        /// 损耗率（百分比，0-100）
        /// </summary>
        [Range(0, 100, ErrorMessage = "损耗率必须在0-100之间")]
        public decimal LossRate { get; set; } = 0;

        /// <summary>
        /// 是否必需物料
        /// </summary>
        public bool IsRequired { get; set; } = true;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 备注说明（可选）
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [StringLength(50, ErrorMessage = "版本号长度不能超过50个字符")]
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 是否当前有效版本
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 批量添加工序物料详情DTO
    /// </summary>
    public class BatchProcessStepMaterialDetailDto
    {
        /// <summary>
        /// 工序Id（必填）
        /// </summary>
        [Required(ErrorMessage = "工序ID不能为空")]
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [StringLength(50, ErrorMessage = "版本号长度不能超过50个字符")]
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 是否当前有效版本
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 物料详情列表（必填，至少包含一个物料）
        /// </summary>
        [Required(ErrorMessage = "物料详情列表不能为空")]
        [MinLength(1, ErrorMessage = "至少需要添加一个物料")]
        public List<ProcessStepMaterialDetailDto> MaterialDetails { get; set; } = new List<ProcessStepMaterialDetailDto>();
    }

    /// <summary>
    /// 工序物料详情列表显示DTO
    /// </summary>
    public class ProcessStepMaterialDetailListDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 工序ID
        /// </summary>
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        public string ProcessStepName { get; set; } = string.Empty;

        /// <summary>
        /// 物料ID
        /// </summary>
        public Guid MaterialId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string MaterialNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 使用量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 用料比例
        /// </summary>
        public decimal UsageRatio { get; set; }

        /// <summary>
        /// 投入产出类型
        /// </summary>
        public MaterialIOType IOType { get; set; }

        /// <summary>
        /// 投入产出类型名称
        /// </summary>
        public string IOTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal LossRate { get; set; }

        /// <summary>
        /// 是否必需物料
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 备注说明
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 是否当前有效版本
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}