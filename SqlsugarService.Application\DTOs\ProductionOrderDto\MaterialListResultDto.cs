using System;
using System.Collections.Generic;
using System.Linq;

namespace SqlsugarService.Application.DTOs.ProductionOrderDto
{
    /// <summary>
    /// 物料清单结果DTO
    /// </summary>
    public class MaterialListResultDto
    {
        /// <summary>
        /// 生产工单ID
        /// </summary>
        public Guid ProductionOrderId { get; set; }

        /// <summary>
        /// 工单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 工单名称
        /// </summary>
        public string OrderName { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        public string ProductNumber { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 物料清单
        /// </summary>
        public List<MaterialListItemDto> Materials { get; set; } = new List<MaterialListItemDto>();

        /// <summary>
        /// 物料总量
        /// </summary>
        public decimal TotalMaterialQuantity { get; set; }
    }
} 