# 父子关系测试脚本
# 专门用于测试BOM的父子关系逻辑

param(
    [Parameter(Mandatory=$true)]
    [string]$BomId,
    
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== 父子关系测试 ===" -ForegroundColor Green
Write-Host "BOM ID: $BomId" -ForegroundColor Yellow
Write-Host "API地址: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

try {
    # 1. 获取BOM树形结构
    Write-Host "1. 获取BOM树形结构..." -ForegroundColor White
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/ProductionPlans/bom-structure/$BomId" -Method Get
    
    if ($response.isSuc) {
        $bomTree = $response.data
        Write-Host "   获取成功!" -ForegroundColor Green
        Write-Host "   根节点数: $($bomTree.Count)" -ForegroundColor Gray
        
        # 2. 分析父子关系
        Write-Host "`n2. 分析父子关系..." -ForegroundColor White
        
        # 收集所有节点信息
        $allNodes = @()
        function Collect-AllNodes {
            param($nodes)
            foreach ($node in $nodes) {
                $allNodes += $node
                if ($node.children -and $node.children.Count -gt 0) {
                    Collect-AllNodes -nodes $node.children
                }
            }
        }
        Collect-AllNodes -nodes $bomTree
        
        Write-Host "   总节点数: $($allNodes.Count)" -ForegroundColor Gray
        
        # 显示每个节点的详细信息
        foreach ($node in $allNodes) {
            Write-Host "   节点ID: $($node.id)" -ForegroundColor Yellow
            Write-Host "   父节点ID: $($node.parentItemId)" -ForegroundColor Yellow
            Write-Host "   显示名称: $($node.displayName)" -ForegroundColor Yellow
            Write-Host "   层级: $($node.level)" -ForegroundColor Yellow
            Write-Host "   子节点数: $($node.children.Count)" -ForegroundColor Yellow
            Write-Host "   可展开: $($node.isExpandable)" -ForegroundColor Yellow
            Write-Host ""
        }
        
        # 3. 检查父子关系问题
        Write-Host "`n3. 检查父子关系问题..." -ForegroundColor White
        $issues = @()
        
        foreach ($node in $allNodes) {
            if (![string]::IsNullOrEmpty($node.parentItemId)) {
                $parentExists = $false
                foreach ($potentialParent in $allNodes) {
                    if ($potentialParent.id -eq $node.parentItemId) {
                        $parentExists = $true
                        break
                    }
                }
                
                if (-not $parentExists) {
                    $issues += "节点 $($node.id) 的父节点 $($node.parentItemId) 不存在"
                }
            }
        }
        
        if ($issues.Count -gt 0) {
            Write-Host "   发现以下问题:" -ForegroundColor Red
            foreach ($issue in $issues) {
                Write-Host "     - $issue" -ForegroundColor Red
            }
        } else {
            Write-Host "   未发现父子关系问题" -ForegroundColor Green
        }
        
        # 4. 显示树形结构
        Write-Host "`n4. 显示树形结构..." -ForegroundColor White
        function Show-TreeStructure {
            param($nodes, $level = 0)
            foreach ($node in $nodes) {
                $indent = "  " * $level
                $expandIcon = if ($node.isExpandable) { "▼" } else { "  " }
                Write-Host "$indent$expandIcon [$($node.sequence)] $($node.displayName) (ID: $($node.id))" -ForegroundColor Gray
                
                if ($node.children -and $node.children.Count -gt 0) {
                    Show-TreeStructure -nodes $node.children -level ($level + 1)
                }
            }
        }
        
        Show-TreeStructure -nodes $bomTree
        
    } else {
        Write-Host "   获取失败: $($response.msg)" -ForegroundColor Red
    }

    Write-Host "`n=== 测试完成 ===" -ForegroundColor Green

} catch {
    Write-Host "测试过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误信息: $($_.Exception)" -ForegroundColor Red
} 