﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Craftsmanship;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Plan
{
    /// <summary>
    /// 工单任务表
    /// </summary>
    [SugarTable("dispatchwork")]
    public class Dispatchwork : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 班组名称
        /// </summary>
        public string TeamName { get; set; } = string.Empty;

        /// <summary>
        /// 负责人名称
        /// </summary>
        public string Teamprincipal { get; set; } = string.Empty;

        /// <summary>
        /// 其他成员
        /// </summary>
        public string OtherMembers { get; set; } = string.Empty;

        /// <summary>
        /// 质检部门
        /// </summary>
        public string QualityTestingDept { get; set; } = string.Empty;
        /// <summary>
        /// 质检人员
        /// </summary>
        public string QualityTestingPeople { get; set; } = string.Empty;
        /// <summary>
        /// 备注
        /// </summary>
        public string Descr { get; set; } = string.Empty;
        /// <summary>
        /// 工单任务ID 外键
        /// </summary>
        public Guid WorkOrderTaskEntityId { get; set; }

    }
}
