﻿using SqlsugarService.Application.DTOs.SalesDto;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.InventoryChange;

namespace SqlsugarService.Application.IService.Sales
{
    public interface ISalesService
    {
        Task<ApiResult<PageResult<List<getsalesorderDto>>>> GetSalesOrderList(GetsalesorderSearchDto seach);
        Task<ApiResult> AddSalesOrder(insertupdatesalesorderDto salesoutbounddto);

        // 为 MES 工具服务添加的异步方法别名
        Task<ApiResult<PageResult<List<getsalesorderDto>>>> GetsalesorderListAsync(GetsalesorderSearchDto seach);
        Task<bool> UpdatesalesorderAsync(insertupdatesalesorderDto dto);
    }
}