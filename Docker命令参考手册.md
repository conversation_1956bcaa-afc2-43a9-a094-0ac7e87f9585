# Docker 命令参考手册

## 镜像操作

### 拉取镜像

```bash
docker pull <镜像名>:<标签>
```

### 查看本地镜像

```bash
docker images
docker image ls
```

### 删除镜像

```bash
docker rmi <镜像ID/镜像名>
```

### 构建镜像

```bash
docker build -t <镜像名>:<标签> .
```

### 查看镜像历史

```bash
docker history <镜像名>
```

### 镜像导出/导入

```bash
docker save -o <文件名>.tar <镜像名>
docker load -i <文件名>.tar
```

## 容器操作

### 运行容器

```bash
docker run -d --name <容器名> -p <宿主机端口>:<容器端口> <镜像名>
```

### 查看容器

```bash
docker ps
docker ps -a
```

### 启动/停止/重启容器

```bash
docker start <容器ID/容器名>
docker stop <容器ID/容器名>
docker restart <容器ID/容器名>
```

### 删除容器

```bash
docker rm <容器ID/容器名>
docker rm -f <容器ID/容器名>
```

### 进入容器

```bash
docker exec -it <容器ID/容器名> /bin/bash
docker exec -it <容器ID/容器名> sh
```

## 日志和监控

### 查看容器日志

```bash
docker logs <容器ID/容器名>
docker logs --tail 1000 <容器ID/容器名>
docker logs -f <容器ID/容器名>
```

### 监控容器资源

```bash
docker stats <容器ID/容器名>
```

### 查看容器详细信息

```bash
docker inspect <容器ID/容器名>
```

### 查看容器端口映射

```bash
docker port <容器ID/容器名>
```

## 文件操作

### 文件复制

```bash
docker cp <容器ID>:<容器路径> <宿主机路径>
docker cp <宿主机路径> <容器ID>:<容器路径>
```

## 网络操作

### 网络管理

```bash
docker network ls
docker network create <网络名>
docker network rm <网络名>
docker network inspect <网络名>
```

## 数据卷操作

### 数据卷管理

```bash
docker volume ls
docker volume create <卷名>
docker volume rm <卷名>
docker volume inspect <卷名>
docker volume prune
```

## 系统清理

### 清理命令

```bash
docker container prune
docker image prune
docker network prune
docker system prune
docker system prune -a
```

## Docker Compose

### Compose 操作

```bash
docker-compose up -d
docker-compose down
docker-compose ps
docker-compose logs -f
docker-compose restart
docker-compose up --build
```

## 常用组合命令

### 批量操作

```bash
docker stop $(docker ps -q)
docker rm $(docker ps -aq)
docker rmi $(docker images -q)
docker inspect <容器名> | grep IPAddress
```

## 常用示例

### MySQL 容器

```bash
docker run -d \
--name mysql \
-p 3306:3306 \
-e MYSQL_ROOT_PASSWORD=123456 \
-v /opt/mysql/data:/var/lib/mysql \
mysql:8.0
```

### Redis 容器

```bash
docker run -d \
--name redis \
-p 6379:6379 \
redis:latest
```

### Nginx 容器

```bash
docker run -d \
--name nginx \
-p 80:80 \
-v /opt/nginx/html:/usr/share/nginx/html \
nginx:latest
```

## 故障排查

### 查看容器状态

```bash
docker ps -a
docker logs <容器名>
docker inspect <容器名>
```

### 进入容器调试

```bash
docker exec -it <容器名> /bin/bash
docker exec -it <容器名> sh
```

### 查看资源使用

```bash
docker stats
docker system df
```
