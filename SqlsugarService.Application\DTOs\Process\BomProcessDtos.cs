﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlsugarService.Domain.Materials;

namespace SqlsugarService.Application.DTOs.Process
{
    /// <summary>
    /// Bom流程表
    /// </summary>
    public class BomProcessDtos { }

    /// <summary>
    /// 获取工艺路线Dto
    /// </summary>
    public class GetRoutingDto
    {
        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 工艺路线编号
        /// </summary>
        public string ProcessRouteNumber { get; set; } = string.Empty;
        /// <summary>
        /// 工艺路线名称
        /// </summary>
        public string ProcessRouteName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取工序Dto
    /// </summary>
    public class GetProcessDto
    {
        /// <summary>
        /// 工序ID
        /// </summary>
        public Guid Id { get; set; }
        

        /// <summary>
        /// 工序名称
        /// </summary>
        public string ProcessStepName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取所有物料清单Dto
    /// </summary>
    public class GetBillOfMaterialsDto
    {
        /// <summary>
        /// 物料ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string? MaterialNumber { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string? MaterialName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        public MaterialTypeEnum? MaterialType { get; set; }

        /// <summary>
        /// 物料属性
        /// </summary>
        public string? MaterialProperty { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 采购价格
        /// </summary>
        public decimal? PurchasePrice { get; set; }

        /// <summary>
        /// 销售价格
        /// </summary>
        public decimal? SalesPrice { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
}
