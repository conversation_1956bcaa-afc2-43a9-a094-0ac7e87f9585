{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning",
      "AuthService.Infrastructure.Database": "Warning"
    }
  },
  "Urls": "http://0.0.0.0:5143",
  "ConnectionStrings": {
    "DefaultConnection": "Host=*************;Port=5432;Database=authservice;Username=****;Password=****;SSL Mode=Disable;"
  },
  "Consul": {
    "Enabled": false, //开发环境禁用consul，部署之前开启
    "ConsulAddress": "http://*************:8500",
    "Datacenter": "dc1",
    "ServiceName": "auth-service",
    "ServiceAddress": "*************",
    "ServicePort": 5143,
    "ServiceTags": ["auth", "api", "microservice", "v1"],
    "ServiceMeta": {
      "version": "1.0.0",
      "environment": "development"
    },
    "HealthCheckPath": "",
    "HealthCheckInterval": 0,
    "HealthCheckTimeout": 0,
    "DeregisterCriticalServiceAfter": 30
  },
  "Jwt": {
    "Authority": "https://localhost:5001",
    "Audience": "auth-api",
    "RequireHttpsMetadata": false
  },
  "Cors": {
    "AllowAnyOrigin": false,
    "AllowedOrigins": [
      "https://localhost:3000",
      "http://localhost:3000",
      "https://localhost:5173",
      "http://localhost:5173"
    ],
    "AllowAnyMethod": true,
    "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    "AllowAnyHeader": true,
    "AllowedHeaders": ["Content-Type", "Authorization", "X-Tenant-Id", "X-Version"],
    "AllowCredentials": true
  }
}
