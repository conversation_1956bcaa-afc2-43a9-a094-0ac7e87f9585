{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "AuthService": "Debug"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=*************;Port=5432;Database=authservice;Username=****;Password=****;Pooling=true;Minimum Pool Size=1;Maximum Pool Size=15;Connection Timeout=30;"}, "MemoryOptimization": {"MaxConcurrentRequests": 50, "RequestTimeout": 30, "CacheSize": "64MB"}, "JwtSettings": {"SecretKey": "your-super-secret-key-that-is-at-least-32-characters-long", "Issuer": "AuthService", "Audience": "AuthService.Api", "ExpirationMinutes": 60}, "Consul": {"Enabled": true, "ConsulAddress": "http://*************:8500", "Host": "*************", "Port": 8500, "ServiceName": "authservice", "ServiceId": "authservice-1", "ServiceAddress": "*************", "ServicePort": 5143, "HealthCheckInterval": "00:00:30", "DeregisterCriticalServiceAfter": "00:01:00"}}