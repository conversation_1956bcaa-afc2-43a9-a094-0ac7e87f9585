﻿using SqlSugar;
using SqlsugarService.Domain.BOM;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Craftsmanship;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Materials
{
    /// <summary>
    ///  物料主数据
    /// </summary>
    public class MaterialEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 物料编号
        /// </summary>
        public string? MaterialNumber { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string? MaterialName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位【个、箱、件、套、台、米、条】
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        public MaterialTypeEnum? MaterialType { get; set; }

        /// <summary>
        /// 物料属性 【自制、委外、外购、其他】
        /// </summary>
        public string? MaterialProperty { get; set; }

        /// <summary>
        /// 物料分类Id
        /// </summary>
        public Guid? MaterialCategoryId { get; set; }

        [SqlSugar.SugarColumn(IsIgnore = true)]
        public MaterialCategory? MaterialCategory { get; set; }

        /// <summary>
        /// 状态 【启用、禁用】
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 有效日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? EffectiveDate { get; set; }

        /// <summary>
        /// 有效期单位
        /// </summary>
        public string? EffectiveUnit { get; set; }

        /// <summary>
        /// 报警天数【提前**天报警】
        /// </summary>
        public int? AlarmDays { get; set; }

        /// <summary>
        /// 库存上线
        /// </summary>
        public int? StockUpperLimit { get; set; }

        /// <summary>
        /// 库存下线
        /// </summary>
        public int? StockLowerLimit { get; set; }

        /// <summary>
        /// 采购价格
        /// </summary>
        public decimal? PurchasePrice { get; set; }

        /// <summary>
        /// 销售价格
        /// </summary>
        public decimal? SalesPrice { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 物料图片
        /// </summary>
        public string? MaterialImage { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public string? Attachment { get; set; }

        // 导航属性
        /// <summary>
        /// BOM明细列表
        /// </summary>
        public ICollection<BomItem> BomItems { get; set; } = new List<BomItem>();

        /// <summary>
        /// 工序-物料关联列表
        /// </summary>
        public ICollection<ProcessStepMaterial> ProcessStepMaterials { get; set; } = new List<ProcessStepMaterial>();

        /// <summary>
        /// 工艺路线-产品关联列表
        /// </summary>
        public ICollection<ProcessStepProduct> ProcessStepProducts { get; set; } = new List<ProcessStepProduct>();
    }


    /// <summary>
    /// 物料类型
    /// </summary>
    public enum MaterialTypeEnum
    {
        /// <summary>
        /// 物料
        /// </summary>
        Material = 1,
        /// <summary>
        /// 成品
        /// </summary>
        Product = 2
    }
}
