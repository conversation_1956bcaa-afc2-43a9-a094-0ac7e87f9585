using AuthService.Application.Middleware;
using AuthService.Infrastructure.Extensions;
using AuthService.Infrastructure.Database;

var builder = WebApplication.CreateBuilder(args);

// 配置监听地址 - 仅本地访问（开发环境）
builder.WebHost.UseUrls("http://localhost:5143");

// 添加服务到容器
builder.Services.AddControllers();
builder.Services.AddOpenApi();

// 添加 Swagger 服务
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "AuthService API",
        Version = "v1",
        Description = "动态API管理微服务 - 包含用户管理、动态API管理、监控等功能"
    });

    // 包含 XML 注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// 添加基础设施服务
builder.Services.AddInfrastructure(builder.Configuration);

// 添加应用服务
builder.Services.AddApplication();

// 添加认证和授权
builder.Services.AddAuthenticationAndAuthorization(builder.Configuration);

// 添加CORS配置
builder.Services.AddCorsConfiguration(builder.Configuration);

// 添加API版本控制 (临时禁用，方便测试)
// AuthService.Infrastructure.Extensions.ServiceCollectionExtensions.AddApiVersioning(builder.Services);

// 添加自定义健康检查
builder.Services.AddCustomHealthChecks(builder.Configuration);

// 添加监控和指标
builder.Services.AddMonitoring(builder.Configuration);

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "AuthService API v1");
        c.RoutePrefix = "swagger"; // Swagger UI 在 /swagger 路径

        // 优化显示
        c.DefaultModelsExpandDepth(-1);
        c.DefaultModelExpandDepth(2);
        c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.List);
        c.DisplayRequestDuration();
        c.EnableDeepLinking();

        // 自定义样式
        c.DocumentTitle = "AuthService API 文档";
    });
    app.UseDeveloperExceptionPage();
}

// 使用CORS
app.UseCors("DefaultPolicy");

// 使用HTTPS重定向
app.UseHttpsRedirection();

// 使用动态路由中间件 (在认证之前，这样可以处理不需要认证的动态端点)
app.UseMiddleware<DynamicRoutingMiddleware>();

// 使用认证和授权
app.UseAuthentication();
app.UseAuthorization();

// 映射控制器
app.MapControllers();

// 映射健康检查端点
app.MapHealthChecks("/health");

// 添加根路径重定向到 Swagger (仅在开发环境)
if (app.Environment.IsDevelopment())
{
    app.MapGet("/", () => Results.Redirect("/swagger")).ExcludeFromDescription();
}

// 运行数据库迁移
using (var scope = app.Services.CreateScope())
{
    try
    {
        var migrationRunner = scope.ServiceProvider.GetRequiredService<MigrationRunner>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("开始执行数据库迁移...");
        var success = await migrationRunner.MigrateAsync();

        if (success)
        {
            logger.LogInformation("数据库迁移执行成功");
        }
        else
        {
            logger.LogError("数据库迁移执行失败");
        }
    }
    catch (Exception ex)
    {
        var logger = app.Services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "数据库迁移执行异常");
    }
}

// === 初始化动态API映射 ===
// 在数据库迁移完成后，自动创建动态API端点映射
using (var scope = app.Services.CreateScope())
{
    try
    {
        var mappingService = scope.ServiceProvider.GetRequiredService<AuthService.Application.Services.IDynamicApiMappingService>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("开始初始化动态API映射...");

        // 初始化动态API映射
        // 这会扫描所有服务层方法并自动创建对应的API端点
        var createdCount = await mappingService.InitializeMappingsAsync();

        if (createdCount > 0)
        {
            logger.LogInformation("动态API映射初始化成功，创建了 {Count} 个端点", createdCount);
        }
        else
        {
            logger.LogInformation("动态API映射初始化完成，没有新的端点需要创建");
        }
    }
    catch (Exception ex)
    {
        var logger = app.Services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "动态API映射初始化失败");
        // 注意：这里不抛出异常，允许应用继续启动
        // 动态API映射失败不应该阻止整个应用的启动
    }
}

app.Run();
