-- 测试子BOM数据插入脚本
-- 用于测试BOM树形下拉列表的子BOM展开功能

-- 1. 插入测试物料（包含产品和物料）
INSERT INTO "MaterialEntity" ("Id", "MaterialNumber", "MaterialName", "SpecificationModel", "Unit", "MaterialType", "MaterialProperty", "Status", "CreatedAt", "UpdatedAt", "IsDeleted")
VALUES 
    -- 主产品
    ('11111111-1111-1111-1111-111111111111', 'PROD001', '主产品A', '主产品规格', '台', 2, '自制', '启用', NOW(), NOW(), false),
    
    -- 子产品（将被用作子BOM）
    ('22222222-2222-2222-2222-222222222222', 'PROD002', '子产品B', '子产品规格', '个', 2, '自制', '启用', NOW(), NOW(), false),
    
    -- 基础物料
    ('33333333-3333-3333-3333-333333333333', 'MAT001', '基础物料1', '基础规格1', '个', 1, '外购', '启用', NOW(), NOW(), false),
    ('44444444-4444-4444-4444-444444444444', 'MAT002', '基础物料2', '基础规格2', '个', 1, '外购', '启用', NOW(), NOW(), false),
    ('55555555-5555-5555-5555-555555555555', 'MAT003', '基础物料3', '基础规格3', '个', 1, '外购', '启用', NOW(), NOW(), false),
    ('66666666-6666-6666-6666-666666666666', 'MAT004', '基础物料4', '基础规格4', '个', 1, '外购', '启用', NOW(), NOW(), false)
ON CONFLICT ("Id") DO NOTHING;

-- 2. 插入子BOM信息（子产品B的BOM）
INSERT INTO "BomInfo" ("Id", "BomNumber", "IsSystemNumber", "IsDefault", "Version", "ProductId", "ProductName", "ColorCode", "Unit", "DailyOutput", "Remark", "CreatedAt", "UpdatedAt", "IsDeleted")
VALUES 
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'BOM002', true, true, '1.0', '22222222-2222-2222-2222-222222222222', '子产品B', '蓝色', '个', 50.0, '子BOM', NOW(), NOW(), false)
ON CONFLICT ("Id") DO NOTHING;

-- 3. 插入主BOM信息（主产品A的BOM）
INSERT INTO "BomInfo" ("Id", "BomNumber", "IsSystemNumber", "IsDefault", "Version", "ProductId", "ProductName", "ColorCode", "Unit", "DailyOutput", "Remark", "CreatedAt", "UpdatedAt", "IsDeleted")
VALUES 
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'BOM001', true, true, '1.0', '11111111-1111-1111-1111-111111111111', '主产品A', '红色', '台', 100.0, '主BOM', NOW(), NOW(), false)
ON CONFLICT ("Id") DO NOTHING;

-- 4. 插入子BOM项目（子产品B的组成）
INSERT INTO "BomItem" ("Id", "BomId", "MaterialId", "ParentItemId", "Quantity", "LossRate", "InOutType", "Unit", "Remark", "CreatedAt", "UpdatedAt", "IsDeleted")
VALUES 
    -- 子BOM的根节点（子产品B）
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '22222222-2222-2222-2222-222222222222', NULL, 1.0, 0.0, 2, '个', '子产品', NOW(), NOW(), false),
    
    -- 子BOM的子节点（基础物料3和4）
    ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '55555555-5555-5555-5555-555555555555', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 2.0, 0.05, 1, '个', '子BOM物料1', NOW(), NOW(), false),
    ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '66666666-6666-6666-6666-666666666666', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 1.5, 0.02, 1, '个', '子BOM物料2', NOW(), NOW(), false)
ON CONFLICT ("Id") DO NOTHING;

-- 5. 插入主BOM项目（主产品A的组成，包含子BOM）
INSERT INTO "BomItem" ("Id", "BomId", "MaterialId", "ParentItemId", "Quantity", "LossRate", "InOutType", "Unit", "Remark", "CreatedAt", "UpdatedAt", "IsDeleted")
VALUES 
    -- 主BOM的根节点（主产品A）
    ('ffffffff-ffff-ffff-ffff-ffffffffffff', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', NULL, 1.0, 0.0, 2, '台', '主产品', NOW(), NOW(), false),
    
    -- 主BOM的直接子节点（基础物料1和2）
    ('gggggggg-gggg-gggg-gggg-gggggggggggg', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '33333333-3333-3333-3333-333333333333', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 3.0, 0.05, 1, '个', '主BOM物料1', NOW(), NOW(), false),
    ('hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '44444444-4444-4444-4444-444444444444', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 2.0, 0.03, 1, '个', '主BOM物料2', NOW(), NOW(), false),
    
    -- 主BOM的子BOM节点（子产品B）
    ('iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '22222222-2222-2222-2222-222222222222', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 2.0, 0.01, 1, '个', '子BOM组件', NOW(), NOW(), false)
ON CONFLICT ("Id") DO NOTHING;

-- 6. 验证插入结果
SELECT '子BOM数据插入完成' as message;

-- 7. 查询验证
SELECT 
    'BomInfo' as table_name,
    COUNT(*) as record_count
FROM "BomInfo" 
WHERE "IsDeleted" = false
UNION ALL
SELECT 
    'BomItem' as table_name,
    COUNT(*) as record_count
FROM "BomItem" 
WHERE "IsDeleted" = false
UNION ALL
SELECT 
    'MaterialEntity' as table_name,
    COUNT(*) as record_count
FROM "MaterialEntity" 
WHERE "IsDeleted" = false;

-- 8. 显示BOM关系结构
SELECT 
    '主BOM结构' as structure_type,
    bi."Id",
    bi."ParentItemId",
    bi."Quantity",
    bi."Unit",
    m."MaterialName",
    m."MaterialNumber",
    m."MaterialType",
    CASE 
        WHEN bi."ParentItemId" IS NULL THEN '根节点'
        WHEN EXISTS (SELECT 1 FROM "BomItem" WHERE "ParentItemId" = bi."Id") THEN '父节点'
        ELSE '叶子节点'
    END as node_type
FROM "BomItem" bi
LEFT JOIN "MaterialEntity" m ON bi."MaterialId" = m."Id"
WHERE bi."BomId" = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'
  AND bi."IsDeleted" = false
UNION ALL
SELECT 
    '子BOM结构' as structure_type,
    bi."Id",
    bi."ParentItemId",
    bi."Quantity",
    bi."Unit",
    m."MaterialName",
    m."MaterialNumber",
    m."MaterialType",
    CASE 
        WHEN bi."ParentItemId" IS NULL THEN '根节点'
        WHEN EXISTS (SELECT 1 FROM "BomItem" WHERE "ParentItemId" = bi."Id") THEN '父节点'
        ELSE '叶子节点'
    END as node_type
FROM "BomItem" bi
LEFT JOIN "MaterialEntity" m ON bi."MaterialId" = m."Id"
WHERE bi."BomId" = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'
  AND bi."IsDeleted" = false
ORDER BY structure_type, bi."ParentItemId" NULLS FIRST, bi."Id"; 