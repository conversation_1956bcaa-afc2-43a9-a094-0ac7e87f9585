using System;

namespace SqlsugarService.Application.AI.KouZi_AI.Dtos
{
    /// <summary>
    /// 扣子空间智能体响应结果DTO
    /// 
    /// 此DTO封装了从扣子AI平台获取的响应数据，提供统一的响应格式：
    /// 
    /// 设计原则：
    /// 1. 标准化响应：无论流式还是非流式都使用相同的响应格式
    /// 2. 错误友好：包含详细的错误信息便于问题诊断
    /// 3. 元数据完整：提供足够的元数据支持业务逻辑
    /// 4. 扩展性好：预留字段支持未来功能扩展
    /// 5. 类型安全：使用强类型避免运行时错误
    /// 
    /// 使用场景：
    /// - API响应的统一封装
    /// - 客户端显示AI回复内容
    /// - 错误处理和用户提示
    /// - 会话状态管理
    /// - 性能监控和分析
    /// 
    /// 数据流向：
    /// 扣子AI平台 -> API处理 -> KouZiAIResponseDto -> 客户端应用
    /// 
    /// 响应状态：
    /// - Success = true: 成功获取AI回复，Content字段包含有效内容
    /// - Success = false: 处理失败，ErrorMessage字段包含错误描述
    /// 
    /// 最佳实践：
    /// - 始终检查Success字段确定处理结果
    /// - 错误时向用户显示友好的错误信息
    /// - 保存ConversationId用于后续对话
    /// - 记录响应时间用于性能分析
    /// </summary>
    public class KouZiAIResponseDto
    {
        /// <summary>
        /// AI智能体返回的消息内容 - 核心响应数据
        /// 
        /// 这是AI智能体生成的回复内容，特点：
        /// - 文本格式：目前主要支持纯文本（可能包含Markdown格式）
        /// - 内容质量：由智能体的训练数据和配置决定
        /// - 长度变化：根据用户问题和AI判断，长度可能差异很大
        /// - 多语言：支持中文、英文等多种语言
        /// 
        /// 内容来源：
        /// - 非流式响应：从API最终响应中提取的完整内容
        /// - 流式响应：由多个增量片段合并而成的完整内容
        /// - 合并响应：通过流式获取但合并为完整文本的内容
        /// 
        /// 处理建议：
        /// - 显示前进行安全性检查（XSS防护等）
        /// - 支持Markdown渲染提升显示效果
        /// - 考虑内容长度对用户界面的影响
        /// - 提供复制、分享等用户友好功能
        /// 
        /// 错误情况：
        /// - null或空字符串：AI未能生成有效回复
        /// - 错误提示：可能包含错误描述信息
        /// - 超时提示：处理超时时的说明信息
        /// 
        /// 示例内容：
        /// - "人工智能（AI）是计算机科学的一个分支..."
        /// - "根据您的需求，我建议以下几个步骤..."
        /// - "抱歉，我无法理解您的问题，请尝试换个方式描述"
        /// </summary>
        public string? Content { get; set; }
        
        /// <summary>
        /// 会话标识符 - 用于维持对话连续性的关键字段
        /// 
        /// 会话ID的重要作用：
        /// - 上下文关联：将当前回复与历史对话关联
        /// - 后续对话：客户端需要在下次请求中传入此ID
        /// - 状态维护：帮助AI理解对话的完整上下文
        /// - 数据追踪：便于分析用户的完整对话流程
        /// 
        /// ID特征：
        /// - 格式：通常为长整型数字字符串（如"7525814599107788800"）
        /// - 唯一性：全局唯一，不会重复
        /// - 生成方：由扣子AI平台生成和管理
        /// - 有效期：根据平台配置，通常24小时到7天
        /// 
        /// 使用方式：
        /// - 新对话：首次请求后获得ConversationId
        /// - 继续对话：在后续请求中传入此ConversationId
        /// - 结束对话：可以丢弃此ID，下次请求将开始新对话
        /// - 并发对话：不同主题使用不同的ConversationId
        /// 
        /// 存储建议：
        /// - 客户端临时存储：在会话期间保存在内存或本地存储
        /// - 不建议持久化：避免长期存储过期的会话ID
        /// - 异常处理：处理会话过期或无效的情况
        /// - 隐私保护：不要在日志中记录会话ID
        /// 
        /// 业务逻辑：
        /// - 多轮对话：确保上下文的正确传递
        /// - 会话管理：支持会话的创建、继续、结束
        /// - 用户体验：提供"开始新对话"的功能
        /// - 错误恢复：会话异常时的降级处理
        /// </summary>
        public string? ConversationId { get; set; }
        
        /// <summary>
        /// 请求处理成功标志 - 关键的状态指示器
        /// 
        /// 成功判断标准：
        /// - true：成功获取AI回复，Content字段包含有效内容
        /// - false：处理失败，需要检查ErrorMessage了解原因
        /// 
        /// 成功场景（Success = true）：
        /// - AI成功理解用户问题并生成回复
        /// - 流式响应完整接收并合并成功
        /// - 非流式响应处理完成且获得最终结果
        /// - 会话状态正常，上下文处理正确
        /// 
        /// 失败场景（Success = false）：
        /// - 网络连接问题导致API调用失败
        /// - 扣子AI平台返回错误响应
        /// - 请求参数验证失败
        /// - 处理超时或系统异常
        /// - JSON解析错误或数据格式问题
        /// 
        /// 错误处理最佳实践：
        /// - 始终检查Success字段再处理Content
        /// - 失败时显示ErrorMessage中的具体错误信息
        /// - 提供重试机制让用户重新尝试
        /// - 记录失败详情便于问题诊断和优化
        /// 
        /// 业务逻辑：
        /// - UI显示：根据Success状态决定显示内容还是错误信息
        /// - 重试策略：失败时可以自动或手动重试
        /// - 降级处理：连续失败时的备用方案
        /// - 用户反馈：向用户说明问题并提供解决建议
        /// 
        /// 监控价值：
        /// - 成功率统计：监控服务质量和稳定性
        /// - 错误分析：识别常见问题和改进方向
        /// - 性能指标：评估响应速度和可靠性
        /// - 用户体验：优化错误处理和用户提示
        /// </summary>
        public bool Success { get; set; } = true;
        
        /// <summary>
        /// 错误消息描述 - 详细的错误信息载体
        /// 
        /// 错误信息的设计原则：
        /// - 用户友好：使用通俗易懂的语言描述问题
        /// - 问题明确：准确指出发生错误的具体环节
        /// - 解决导向：尽可能提供解决问题的建议
        /// - 技术平衡：既有技术细节又考虑普通用户理解
        /// 
        /// 常见错误类型及信息：
        /// - 网络错误："网络连接失败，请检查网络状态后重试"
        /// - API错误："服务暂时不可用，请稍后重试"
        /// - 参数错误："请求参数无效，请检查输入内容"
        /// - 超时错误："处理超时，请重新发送消息"
        /// - 权限错误："无权限访问该服务，请联系管理员"
        /// 
        /// 错误信息来源：
        /// - HTTP状态码：如404、500等标准错误
        /// - API响应：扣子AI返回的具体错误描述
        /// - 系统异常：本地处理过程中的异常信息
        /// - 业务验证：自定义的业务规则验证失败
        /// 
        /// 使用建议：
        /// - 直接显示：大多数情况下可以直接向用户显示
        /// - 日志记录：记录详细错误信息便于问题排查
        /// - 错误分类：根据错误类型采取不同的处理策略
        /// - 用户指导：提供下一步操作的具体建议
        /// 
        /// 安全考虑：
        /// - 信息过滤：避免暴露敏感的系统内部信息
        /// - 攻击防护：不要泄露可被恶意利用的技术细节
        /// - 用户隐私：不要在错误信息中包含用户敏感数据
        /// - 合规要求：符合相关法规对错误信息的要求
        /// 
        /// 国际化支持：
        /// - 多语言：根据用户语言偏好返回相应语言的错误信息
        /// - 文化适应：考虑不同文化背景下的表达习惯
        /// - 本地化：结合本地的技术支持和服务情况
        /// </summary>
        public string? ErrorMessage { get; set; }
        
        /// <summary>
        /// 响应创建时间戳 - 重要的时间元数据
        /// 
        /// 时间戳的重要作用：
        /// - 性能监控：计算响应时间和处理延迟
        /// - 数据分析：分析用户使用模式和高峰时段
        /// - 问题排查：定位具体时间点的问题和异常
        /// - 缓存策略：实现基于时间的缓存和过期机制
        /// - 审计追踪：满足合规要求的操作记录
        /// 
        /// 时间标准：
        /// - 使用UTC时间避免时区混乱
        /// - ISO 8601格式便于标准化处理
        /// - 高精度：支持到毫秒级的精确度
        /// - 自动生成：由系统自动设置，无需手动指定
        /// 
        /// 业务应用：
        /// - 排序显示：按时间顺序展示对话历史
        /// - 超时判断：检查响应是否在合理时间内返回
        /// - 统计分析：生成使用报告和性能报告
        /// - 用户体验：显示"几分钟前"等相对时间
        /// 
        /// 技术实现：
        /// - 默认值：DateTime.UtcNow确保时间准确性
        /// - 序列化：JSON序列化时自动转换为ISO格式
        /// - 时区处理：客户端根据需要转换为本地时间
        /// - 精度控制：根据业务需要调整时间精度
        /// 
        /// 性能分析：
        /// - 响应时间：CreatedAt - 请求发起时间
        /// - 趋势分析：不同时间段的性能表现
        /// - 容量规划：基于历史数据预测未来需求
        /// - SLA监控：确保服务水平协议的达成
        /// 
        /// 最佳实践：
        /// - 一致性：整个系统使用统一的时间标准
        /// - 可读性：为用户显示时提供友好的格式
        /// - 存储效率：选择合适的数据库时间类型
        /// - 备份恢复：时间戳有助于数据的一致性检查
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 消耗的Token数量 - 重要的成本和性能指标
        /// 
        /// Token概念说明：
        /// - Token是AI模型处理文本的基本单位
        /// - 通常1个Token约等于0.75个英文单词或0.5个中文字符
        /// - Token数量直接影响API调用的成本
        /// - 不同模型的Token计算方式可能有差异
        /// 
        /// 使用统计意义：
        /// - 成本控制：监控和预算API调用费用
        /// - 性能优化：较少的Token通常意味着更快的处理速度
        /// - 容量规划：预估系统负载和资源需求
        /// - 用户限额：实现基于使用量的用户限制
        /// 
        /// Token组成：
        /// - 输入Token：用户问题和上下文消耗的Token
        /// - 输出Token：AI回复生成消耗的Token
        /// - 系统Token：提示词、格式化等系统开销
        /// - 总计Token：所有部分的Token总和
        /// 
        /// 业务应用：
        /// - 计费系统：基于Token使用量进行精确计费
        /// - 配额管理：为不同用户设置Token使用限额
        /// - 成本分析：分析不同功能和用户的成本结构
        /// - 优化建议：指导用户优化问题表达方式
        /// 
        /// 数据来源：
        /// - API响应：某些AI平台在响应中返回Token统计
        /// - 本地估算：使用Token计算库进行近似估算
        /// - 日志分析：从详细日志中提取Token信息
        /// - 定期同步：从平台获取准确的使用统计
        /// 
        /// 注意事项：
        /// - 准确性：流式响应中Token统计可能不够准确
        /// - 延迟性：Token统计可能在响应后才更新
        /// - 估算误差：本地计算与平台统计可能存在差异
        /// - 模型差异：不同AI模型的Token计算规则可能不同
        /// 
        /// 优化策略：
        /// - 问题精简：鼓励用户提出简洁明确的问题
        /// - 上下文管理：合理控制对话历史的长度
        /// - 模型选择：根据场景选择合适的AI模型
        /// - 缓存复用：避免重复的AI调用
        /// 
        /// 默认值说明：
        /// - 默认为0，表示Token信息不可用或未统计
        /// - 流式响应中通常无法实时统计Token
        /// - 可以通过后续的统计接口获取准确数据
        /// </summary>
        public int TokensUsed { get; set; }
    }
}