# 数据库配置和初始化说明

## 🎯 避免每次启动自动建表的配置

### 1. 配置文件设置

#### 主配置文件 (appsettings.json)

```json
{
  "Database": {
    "EnableAutoCreateTables": false,
    "CreateMode": "IfNotExists"
  }
}
```

#### 开发环境 (appsettings.Development.json)

```json
{
  "Database": {
    "EnableAutoCreateTables": true,
    "CreateMode": "IfNotExists"
  }
}
```

#### 生产环境 (appsettings.Production.json)

```json
{
  "Database": {
    "EnableAutoCreateTables": false,
    "CreateMode": "Never"
  }
}
```

### 2. 配置选项说明

**EnableAutoCreateTables**:

- `true`: 启用自动建表功能
- `false`: 禁用自动建表功能（推荐生产环境）

**CreateMode**:

- `"IfNotExists"`: 仅在表不存在时创建（推荐）
- `"Always"`: 总是创建表（会删除重建，慎用）
- `"Never"`: 从不自动创建表

## 🚀 手动初始化数据库

### 方式一：通过 API 接口

#### 1. 检查数据库连接

```http
GET /api/database/health
```

#### 2. 查看现有表

```http
GET /api/database/tables
```

#### 3. 初始化表（仅创建不存在的表）

```http
POST /api/database/initialize
```

#### 4. 强制重新创建所有表（会删除数据）

```http
POST /api/database/initialize?forceRecreate=true
```

### 方式二：通过代码

```csharp
// 在控制器或服务中注入
private readonly DatabaseInitializationService _databaseService;

// 初始化表
await _databaseService.InitializeTablesAsync(forceRecreate: false);

// 检查连接
await _databaseService.CheckDatabaseConnectionAsync();
```

## 📊 日志输出示例

### 禁用自动建表时

```
[INFO] 自动建表功能已禁用，跳过表初始化
```

### 启用自动建表时

```
[INFO] 自动建表功能已启用，开始初始化数据表...
[INFO] 表创建模式: IfNotExists
[DEBUG] 表 user (Users) 已存在，跳过创建
[INFO] 表 bominfo (BomInfo) 不存在，已创建
[INFO] 表初始化统计 - 创建: 1, 已存在: 25, 跳过: 0
[INFO] 数据表初始化完成
```

## 🔧 环境变量控制

可以通过环境变量临时启用自动建表：

```bash
# Windows
set ENABLE_AUTO_CREATE_TABLES=true

# Linux/Mac
export ENABLE_AUTO_CREATE_TABLES=true
```

## ⚠️ 注意事项

1. **生产环境建议**：设置 `EnableAutoCreateTables: false`
2. **数据安全**：使用 `forceRecreate=true` 会删除所有数据
3. **权限控制**：生产环境应限制数据库初始化 API 的访问权限
4. **备份策略**：在执行强制重建前务必备份数据

## 🎯 最佳实践

1. **开发环境**：启用自动建表，使用 `IfNotExists` 模式
2. **测试环境**：根据需要灵活配置
3. **生产环境**：禁用自动建表，手动管理数据库结构
4. **CI/CD**：在部署脚本中包含数据库初始化步骤
