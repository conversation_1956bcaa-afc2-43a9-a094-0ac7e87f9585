# 🔧 工单任务管理 API 使用指南

## 📋 功能概述

工单任务管理系统提供了完整的工单任务生命周期管理，包括：
- ✅ **分页查询** - 支持多条件筛选的工单任务列表
- ✅ **详情查看** - 获取单个工单任务的详细信息
- ✅ **报工功能** - 根据工单任务信息添加报工质检记录
- ✅ **批量报工** - 支持批量提交报工信息
- ✅ **报工记录** - 查看工单任务的历史报工记录

## 🚀 API 接口列表

### 1. 获取工单任务分页列表
```http
POST /api/WorkOrderTask/list
Content-Type: application/json

{
  "pageIndex": 1,
  "pageSize": 20,
  "taskNumber": "WO2024001",
  "taskName": "装配任务",
  "productionOrderId": "guid",
  "stationName": "装配站",
  "processCode": "P001",
  "processName": "装配工艺",
  "status": "进行中",
  "priority": 3,
  "planStartTimeBegin": "2024-01-01T00:00:00",
  "planStartTimeEnd": "2024-12-31T23:59:59"
}
```

**响应示例：**
```json
{
  "isSuc": true,
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalCount": 100,
    "totalPage": 5,
    "pageIndex": 1,
    "pageSize": 20,
    "items": [
      {
        "id": "guid",
        "sequenceNumber": 1,
        "taskNumber": "WO2024001",
        "taskName": "装配任务",
        "productionOrderId": "guid",
        "productionOrderNumber": "PO2024001",
        "stationName": "装配站",
        "processCode": "P001",
        "processName": "装配工艺",
        "planQuantity": 100,
        "actualQuantity": 50,
        "planStartTime": "2024-01-01T08:00:00",
        "planEndTime": "2024-01-01T18:00:00",
        "actualStartTime": "2024-01-01T08:30:00",
        "status": "进行中",
        "priority": 3,
        "createTime": "2024-01-01T00:00:00",
        "updateTime": "2024-01-01T12:00:00"
      }
    ]
  }
}
```

### 2. 获取工单任务详情
```http
GET /api/WorkOrderTask/{id}
```

### 3. 工单任务报工
```http
POST /api/WorkOrderTask/work-report
Content-Type: application/json

{
  "workOrderTaskId": "guid",
  "inspectionName": "首检报工",
  "inspectionType": "首检",
  "productId": "guid",
  "processStepId": "guid",
  "stationId": "guid",
  "teamId": "guid",
  "reporterId": "guid",
  "inspectorId": "guid",
  "reportedQuantity": 10,
  "reportTime": "2024-01-01T10:00:00",
  "inspectionTime": "2024-01-01T10:30:00",
  "inspectionDepartment": "质检部",
  "testedQuantity": 10,
  "qualifiedQuantity": 9,
  "unqualifiedQuantity": 1,
  "overallResult": "合格",
  "remark": "首检完成，质量良好"
}
```

**响应示例：**
```json
{
  "isSuc": true,
  "code": 200,
  "msg": "操作成功"
}
```

### 4. 批量工单任务报工
```http
POST /api/WorkOrderTask/batch-work-report
Content-Type: application/json

[
  {
    "workOrderTaskId": "guid1",
    "inspectionName": "首检报工",
    "inspectionType": "首检",
    "reporterId": "guid",
    "reportedQuantity": 10,
    "reportTime": "2024-01-01T10:00:00"
  },
  {
    "workOrderTaskId": "guid2",
    "inspectionName": "巡检报工",
    "inspectionType": "巡检",
    "reporterId": "guid",
    "reportedQuantity": 15,
    "reportTime": "2024-01-01T11:00:00"
  }
]
```

### 5. 获取工单任务的报工记录
```http
GET /api/WorkOrderTask/{workOrderTaskId}/work-reports
```

### 6. 获取状态选项
```http
GET /api/WorkOrderTask/status-options
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    { "value": "未开工", "label": "未开工" },
    { "value": "进行中", "label": "进行中" },
    { "value": "已完成", "label": "已完成" },
    { "value": "已暂停", "label": "已暂停" },
    { "value": "已取消", "label": "已取消" }
  ]
}
```

### 7. 获取优先级选项
```http
GET /api/WorkOrderTask/priority-options
```

### 8. 获取检验类型选项
```http
GET /api/WorkOrderTask/inspection-type-options
```

## 🔍 查询参数说明

### 分页查询参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageIndex | int | 是 | 页码，从1开始 |
| pageSize | int | 是 | 每页大小 |
| taskNumber | string | 否 | 任务编号（模糊查询） |
| taskName | string | 否 | 任务名称（模糊查询） |
| productionOrderId | guid | 否 | 生产工单ID |
| stationName | string | 否 | 站点名称（模糊查询） |
| processCode | string | 否 | 工艺编号（模糊查询） |
| processName | string | 否 | 工艺名称（模糊查询） |
| status | string | 否 | 任务状态 |
| priority | int | 否 | 优先级（1-低，2-中，3-高，4-紧急） |
| planStartTimeBegin | datetime | 否 | 计划开工时间-开始 |
| planStartTimeEnd | datetime | 否 | 计划开工时间-结束 |
| planEndTimeBegin | datetime | 否 | 计划完工时间-开始 |
| planEndTimeEnd | datetime | 否 | 计划完工时间-结束 |

### 报工参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| workOrderTaskId | guid | 是 | 工单任务ID |
| inspectionName | string | 是 | 检验单名称 |
| inspectionType | string | 是 | 检验类型（首检、巡检、末检等） |
| reporterId | guid | 是 | 报工人员ID |
| reportedQuantity | int | 是 | 报工数量（必须大于0） |
| reportTime | datetime | 否 | 报工时间（默认当前时间） |
| inspectionTime | datetime | 否 | 检验时间 |
| testedQuantity | int | 否 | 检测数量 |
| qualifiedQuantity | int | 否 | 合格数量 |
| unqualifiedQuantity | int | 否 | 不合格数量 |
| overallResult | string | 否 | 检测结果 |
| remark | string | 否 | 备注 |

## 🎯 业务逻辑说明

### 报工业务逻辑
1. **验证工单任务** - 检查工单任务是否存在
2. **生成检验单号** - 自动生成格式为 `QC{yyyyMMdd}{序号}` 的检验单号
3. **创建报工记录** - 在报工质检表中创建记录
4. **更新任务状态** - 自动更新工单任务的实际数量和状态：
   - 首次报工：设置实际开工时间，状态改为"进行中"
   - 达到计划数量：设置完工时间，状态改为"已完成"，计算实际用时

### 状态流转
```
未开工 → 进行中 → 已完成
   ↓       ↓
 已取消   已暂停
```

## 🧪 测试示例

### 使用 PowerShell 测试
```powershell
# 1. 获取工单任务列表
$response = Invoke-RestMethod -Uri "http://localhost:64922/api/WorkOrderTask/list" -Method POST -ContentType "application/json" -Body '{"pageIndex":1,"pageSize":10}'

# 2. 报工测试
$workReportBody = @{
    workOrderTaskId = "your-task-id"
    inspectionName = "测试报工"
    inspectionType = "首检"
    reporterId = "your-reporter-id"
    reportedQuantity = 5
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:64922/api/WorkOrderTask/work-report" -Method POST -ContentType "application/json" -Body $workReportBody
```

## 📝 注意事项

1. **数据验证** - 所有必填字段都有验证，报工数量必须大于0
2. **事务处理** - 报工操作使用数据库事务，确保数据一致性
3. **自动编号** - 检验单号自动生成，无需手动指定
4. **状态管理** - 工单任务状态会根据报工情况自动更新
5. **时间记录** - 系统会自动记录实际开工时间和完工时间

---

**🎉 工单任务管理功能已完成！**
- ✅ 编译成功，无错误
- ✅ 服务正常运行
- ✅ API 接口完整
- ✅ 业务逻辑完善
