# SqlsugarService.API 启动指南

## 🚀 快速启动

### 方法一：使用PowerShell脚本启动

```powershell
# 创建启动脚本 start-app.ps1
cd SqlsugarService.API\bin\Release\net6.0\publish
$env:ASPNETCORE_ENVIRONMENT="Development"
$env:ASPNETCORE_URLS="http://localhost:8080"
dotnet SqlsugarService.API.dll
```

### 方法二：使用批处理文件启动

创建 `start-app.bat` 文件：
```batch
@echo off
cd /d "SqlsugarService.API\bin\Release\net6.0\publish"
set ASPNETCORE_ENVIRONMENT=Development
set ASPNETCORE_URLS=http://localhost:8080
dotnet SqlsugarService.API.dll
pause
```

## 🔧 配置优化

### 1. 端口配置
如果8080端口也被占用，可以使用其他端口：
```powershell
$env:ASPNETCORE_URLS="http://localhost:9000"  # 或其他可用端口
```

### 2. 数据库配置优化

#### 生产环境配置 (appsettings.Production.json)
```json
{
  "Database": {
    "EnableAutoCreateTables": false,
    "CreateMode": "Never"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

#### 开发环境配置 (appsettings.Development.json)
```json
{
  "Database": {
    "EnableAutoCreateTables": true,
    "CreateMode": "IfNotExists"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "SqlsugarService": "Debug"
    }
  }
}
```

## 🐛 故障排除

### 1. 端口被占用
```powershell
# 检查端口占用
netstat -ano | findstr :8080

# 如果被占用，使用其他端口
$env:ASPNETCORE_URLS="http://localhost:9000"
```

### 2. 数据库连接问题
```powershell
# 测试数据库连接
Test-NetConnection -ComputerName "*************" -Port 5432
```

### 3. 查看详细日志
```powershell
$env:Logging__LogLevel__Default="Debug"
$env:Logging__LogLevel__SqlsugarService="Debug"
```

## 🌐 访问应用

启动成功后，在浏览器中访问：
- **主页（重定向到Swagger）**: http://localhost:8080/
- **Swagger文档**: http://localhost:8080/swagger
- **健康检查**: http://localhost:8080/health

## 📝 常见问题

### Q: 应用启动后立即退出？
A: 检查数据库连接和配置文件，确保：
- 数据库服务器可访问
- 连接字符串正确
- 禁用自动建表功能（生产环境）

### Q: 浏览器显示"无法访问此网站"？
A: 检查：
- 应用是否正在运行
- 端口是否正确
- 防火墙设置

### Q: 如何在后台运行？
A: 使用Windows服务或任务计划程序，或者：
```powershell
Start-Process -FilePath "dotnet" -ArgumentList "SqlsugarService.API.dll" -WindowStyle Hidden
```

## 🔄 自动启动脚本

创建 `auto-start.ps1`：
```powershell
# 自动检测可用端口并启动
$ports = @(8080, 9000, 9001, 9002)
$started = $false

foreach ($port in $ports) {
    $test = Test-NetConnection -ComputerName "localhost" -Port $port -WarningAction SilentlyContinue
    if (-not $test.TcpTestSucceeded) {
        Write-Host "使用端口 $port 启动应用..." -ForegroundColor Green
        $env:ASPNETCORE_URLS = "http://localhost:$port"
        $env:ASPNETCORE_ENVIRONMENT = "Development"
        
        Set-Location "SqlsugarService.API\bin\Release\net6.0\publish"
        Start-Process "http://localhost:$port"
        dotnet SqlsugarService.API.dll
        $started = $true
        break
    }
}

if (-not $started) {
    Write-Host "所有端口都被占用，请手动指定端口" -ForegroundColor Red
}
```
